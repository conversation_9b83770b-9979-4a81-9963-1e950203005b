<template>
  <div style="margin-right: 12px">
    <el-dropdown id="personDropdown" trigger="click" placement="bottom" @command="handleCommand">
      <span class="el-dropdown-link">
        <div class="avatar">
          <el-avatar slot="reference" size="small"
            style="color: #fff; width: 25px; height: 25px; line-height: 25px; cursor: pointer">
            {{ currentUser.name && currentUser.name.slice(0, 1).toUpperCase() }}
          </el-avatar>
        </div>
      </span>
      <el-dropdown-menu slot="dropdown" class="popper_class">
        <el-dropdown-item id="pc-dropdown-void" style="height: 0"></el-dropdown-item>
        <el-dropdown-item class="version" disabled>
          {{ $t('header.currentVersion') }}：{{ version }}
        </el-dropdown-item>
        <el-dropdown-item command="userInfo">
          <i class="edc-icon-gerenzhongxin" style="margin-right: 8px; padding: 0; color: #0854a1"></i>{{
            $t('header.personalInfo') }}
        </el-dropdown-item>
        <el-dropdown-item command="goDataFlow" v-if="userPermission.includes('dataFlow') && !isDataFlow">
          <i class="edc-icon-dataFlow" style="margin-right: 8px; padding: 0; color: #0854a1"></i>{{
            $t('header.dataFlow') }}
        </el-dropdown-item>
        <el-dropdown-item command="goCompanyManagementPlatform"
          v-if="userPermission.includes('manageCenter') && (isDataFlow || !isManagementPlatform)">
          <i class="edc-icon-guanlizhongxin" style="margin-right: 8px; padding: 0;color: #0854a1"></i>{{
            $t('managementCenter') }}
        </el-dropdown-item>
        <el-dropdown-item command="toChangePassword">
          <i class="edc-icon-password" style="margin-right: 8px; padding: 0; color: #0854a1"></i>{{
            $t('header.changePassword') }}
        </el-dropdown-item>
        <el-dropdown-item command="logout">
          <i class="edc-icon-sign-out" style="margin-right: 8px; padding: 0; color: #0854a1"></i>{{ $t('header.logout')
          }}
        </el-dropdown-item>
        <el-dropdown-item v-for="(item, index) in thirdExtendOptions" :key="index" :command="item.cmd">
          <img :src="item.iconBase64" class="extend-img" />{{ item.name }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <user-info ref="userInfo" :current-user="currentUser"></user-info>
    <change-password ref="toChangePassword" @logout="logout"></change-password>
  </div>
</template>

<script>
import UserInfo from './personal-center/UserInfo.vue'
import ChangePassword from './personal-center/ChangePassword.vue'
import { UserViaSelectExtensionPoint } from '../../extension-point/user-via-select'
import { userAuthApi } from '@/api/header'
import { clickLogApi } from '@/api/onehit'
import ErrorHandler from '@/utils/ErrorHandler'
import baseUrls from "@/api/baseUrlConfig";
import FingerUtil from '@/utils/FingerUtil';
import { userManager } from 'finger';

export default {
  name: 'personal-center',
  components: { UserInfo, ChangePassword },
  data() {
    return {
      currentUser: '',
      thirdExtendOptions: [],
      userPermission: [],
      isManagementPlatform: false,
      isDataFlow: false,
      version: '********'
    }
  },
  created() {
    this.getUserPermission()
  },
  async mounted() {
    this.watchSize()
    this.currentUser = await userManager.getCurrentUser()
    if (process.env.NODE_ENV === 'production') {
      await this.loadExtendOption()
    }
    this.isManagementPlatform = sessionStorage.getItem('isManagementPlatform') === 'true'
    this.isDataFlow = window.location.pathname.includes('AIMS')
  },
  methods: {
    watchSize() {
      const targetClassName = 'el-dropdown-menu--small'
      const targetElement = document.getElementById('personDropdown')
      const observer = new MutationObserver(mutationsList => {
        for (const mutation of mutationsList) {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            const currentClasses = mutation.target.className
            if (currentClasses.includes(targetClassName)) {
              mutation.target.classList.remove(targetClassName)
            }
          }
        }
      })
      const observerConfig = {
        attributes: true,
        attributeFilter: ['class'],
        subtree: true
      }
      observer.observe(targetElement, observerConfig)
    },
    getUserPermission() {
      userAuthApi.getUserPermission().then(({ data }) => {
        this.userPermission = Object.keys(data).filter(key => data[key].length !== 0)
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
    },
    handleCommand(command) {
      if (!command) return
      const option = this.thirdExtendOptions.find(opt => opt.cmd === command)
      if (command === 'userInfo' || command === 'toChangePassword') {
        this.$refs[command].open()
      } else if (option && typeof option.click === 'function') {
        option.click.call(this)
      } else {
        this[command]()
      }
    },
    logout() {
      const clickLog = sessionStorage.getItem('clickLog')
      if (clickLog) {
        clickLogApi.updateClickLogApi(clickLog).then(function (response) {
          if (response.status === 200) {
            sessionStorage.removeItem('clickLog')
          }
        })
      }
      const { origin, pathname, search, hash } = window.location;

      // 构造新的 logout 跳转 URL
      const logoutPath = pathname.concat('logout');
      const logoutUrl = `${origin}${logoutPath}${search}${hash}`;
      // 执行跳转
      window.location.href = logoutUrl;
    },
    goCompanyManagementPlatform() {
      const url = baseUrls.contextPath + '/Scenator/#/?isManagementPlatform=true'
      console.log("ManagementPlatform" + url)
      window.open(url)
    },
    goDataFlow() {
      const url = baseUrls.contextPath + '/AIMS/'
      console.log("aims" + url)
      window.open(url)
    },
    async loadExtendOption() {
      if (FingerUtil.default) {
        const point = FingerUtil.default.getExtensionPoint(UserViaSelectExtensionPoint.prototype.getName())
        if (point.hasExtension()) {
          point.getExtensionImplementResults().forEach(optionItem => {
            optionItem.cmd = Symbol('cmd')
            this.thirdExtendOptions.push(optionItem)
          })
        }
      }
    }
  }
}
</script>

<style scoped>
.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  line-height: 32px;
}

.avatar:hover {
  background-color: #34577D;
}

.el-dropdown-menu__item:not(.is-disabled):hover {
  color: #182A4E;
}

.extend-img {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  padding: 0px;
  vertical-align: middle;
}

.el-dropdown-menu {
  padding: 4px 0;
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px #D9D9D9;
}

.el-dropdown-menu__item {
  padding: 0 16px;
}

::v-deep .popper__arrow {
  display: inline-block !important;
}

.popper_class .el-dropdown-menu__item {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: #182A4E;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
}

.popper_class .el-dropdown-menu__item.version {
  color: #94999D;
  height: 38px;
  line-height: 38px;
  font-size: 12px;
}

.el-dropdown-menu.el-popper.popper_class {
  margin-top: 10px !important;
}
</style>
