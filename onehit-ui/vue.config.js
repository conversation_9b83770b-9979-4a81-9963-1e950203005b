const webpack = require('webpack')
const path = require('path');
const {defineConfig} = require('@vue/cli-service')
const FileManagerPlugin = require("filemanager-webpack-plugin");

module.exports = defineConfig({
    lintOnSave: true,
    productionSourceMap: true,
    runtimeCompiler: true,
    css: {
        extract: false
    },
    publicPath: "./resources/apps/OneHit/",
    chainWebpack: config => {
        config.plugin('provide').use(webpack.ProvidePlugin, [{
            $: 'jquery',
            jQuery: 'jquery',
            'window.jQuery': 'jquery'
        }])
        if (process.env.NODE_ENV === 'production' && process.argv[4] === 'lib') {
            config.externals({
                finger: 'finger',
                vue: 'vue',
                vuex: 'vuex',
                axios: 'axios',
                'element-ui': 'element-ui',
                'vue-i18n': 'vue-i18n',
                'vue-router': 'vue-router',
                jquery: "jquery",
                lodash: "lodash",
                i18next: "i18next",
                Renderer3D: 'Renderer3D'
            })
        }
        config.module.rule('handlebars')
            .test(/\.handlebars$/)
            .use('handlebars-loader')
            .loader('handlebars-loader')
            .end()

        config.module
            .rule('images')
            .set('parser', {
                dataUrlCondition: {
                    maxSize: 30 * 1024 // 4KiB
                }
            })
        if (process.env.NODE_ENV === 'production' && process.argv.includes('build-productSwitch')) {
            config.plugin('FileManagerPlugin')
                .use(FileManagerPlugin).tap(() => [{
                events: {
                    onEnd: {
                        copy: [
                            {
                                source: path.resolve(__dirname, "./src/app/system/product-switch-extension/config"),
                                destination: path.resolve(__dirname + '/..' + '/onehit-scenator/apps/productSwitch/config'),
                            },
                            {
                                source: path.resolve(__dirname, "./src/app/system/product-switch-extension/config"),
                                destination: path.resolve(__dirname + '/..' + '/onehit-edc-scenator/apps/productSwitch/config'),
                            }
                        ]
                    }
                }
            }])
        } else {
            config.plugin('FileManagerPlugin')
                .use(FileManagerPlugin).tap(() => [{
                events: {
                    onEnd: {
                        copy: [
                            {
                                source: path.resolve(__dirname, "./src/languages"),
                                destination: path.resolve(__dirname + '/..' + '/onehit-scenator/apps/OneHit/languages'),
                            },
                            {
                                source: path.resolve(__dirname, "./src/config"),
                                destination: path.resolve(__dirname + '/..' + '/onehit-scenator/apps/OneHit/config'),
                            },
                            {
                                source: path.resolve(__dirname, './src/assets/labelModel/static'),
                                destination: path.resolve(__dirname + '/..' + '/onehit-scenator/apps/OneHit/images'),
                            },
                            {
                                source: path.resolve(__dirname, './src/assets/labelModel/iframeCss.css'),
                                destination: path.resolve(__dirname + '/..' + '/onehit-scenator/apps/OneHit/css/iframeCss.css'),
                            },
                            {
                                source: path.resolve(__dirname, './src/assets/relatedDocuments/css/iframeCss.css'),
                                destination: path.resolve(__dirname + '/..' + '/onehit-scenator/apps/OneHit/css/relatedDocuments/iframeCss.css'),
                            },
                            {
                                source: path.resolve(__dirname, "./src/assets/relatedDocuments/js/previewIframe.js"),
                                destination: path.resolve(__dirname + '/..' + '/onehit-scenator/apps/OneHit/js/relatedDocuments/previewIframe.js'),
                            },
                            {
                                source: path.resolve(__dirname, './src/assets/newFeatureDescription/gif'),
                                destination: path.resolve(__dirname + '/..' + '/onehit-scenator/apps/OneHit/gif'),
                            },
                            {
                                source: path.resolve(__dirname, "./src/utils/monaco"),
                                destination: path.resolve(__dirname + '/..' + '/onehit-scenator/apps/OneHit/js/monaco'),
                            },
                        ]
                    }
                }
            }])
        }
    },

    configureWebpack: {
        module: {
            rules: [
                {
                    test: /\.png$|\.jpg$|\.svg$|\.gif$|\.swf$|\.IVE|\.3DS$|\.ico$|\.woff$|\.ttf$|\.FBX$|\.wav$|\.mp3$/,
                    loader: "url-loader",
                    type: 'javascript/auto',
                    options: {
                        name: "images/[name]-[hash:7].[ext]",
                        publicPath: "./resources/apps/OneHit/",
                        esModule: false
                    },
                },
                {
                    test : /\.scss$/,
                    loader : "sass-loader"
                }
            ]
        }
    },
})


