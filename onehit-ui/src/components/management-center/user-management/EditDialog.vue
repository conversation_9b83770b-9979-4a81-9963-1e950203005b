<template>
  <el-dialog
    width="480px"
    append-to-body
    :title="title"
    v-if="dialogVisible"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :before-close="handleClose">
    <el-form
      style="overflow: hidden auto"
      :style="{maxHeight: dialogBodyMaxHeight}"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="100px">
      <el-form-item prop="username" :label="$t('username') +'：'" v-if="title!==$t('变更部门') && title!==$t('变更角色')">
        <el-input :disabled="!!form.id" v-model="form.username" :placeholder="$t('pleaseEnter')"></el-input>
      </el-form-item>
      <el-form-item prop="name" :label="$t('name') + '：'" v-if="title!==$t('变更部门') && title!==$t('变更角色')">
        <el-input v-model="form.name" :placeholder="$t('pleaseEnter')"></el-input>
      </el-form-item>
      <el-form-item  v-if="title!==$t('变更部门') && title!==$t('变更角色')" prop="mobilePhone" :label="$t('mobilePhone') + '：'">
        <el-input v-model="form.mobilePhone" :placeholder="$t('pleaseEnter')"></el-input>
      </el-form-item>
      <el-form-item prop="email" :label="$t('email') + '：'" v-if="title!==$t('变更部门') && title!==$t('变更角色')">
        <el-input v-model="form.email" :placeholder="$t('pleaseEnter')"></el-input>
      </el-form-item>
      <el-form-item prop="position" :label="$t('职位') + '：'" v-if="title!==$t('变更部门') && title!==$t('变更角色')">
        <el-input v-model="form.position" :placeholder="$t('pleaseEnter')"></el-input>
      </el-form-item>
      <el-form-item v-if="title!==$t('变更部门')" prop="roles" :label="$t('角色') + '：'">
        <el-popover placement="bottom-start" popper-class="role-select"
                    :width="rolePopoverWidth" @show="popoverShow('role')" @hide="popoverHide">
          <div>
            <el-input style="margin: 0 16px 8px 16px;width: calc(100% - 32px);" :placeholder="$t('搜索')"
                      clearable prefix-icon="el-icon-search" v-model.trim="roleFilterValue"/>
            <div v-if="roleList.length" class="role-list">
              <div v-for="i in roleList" :key="i.id">
                <el-checkbox v-model="i.checked"></el-checkbox>
                <div class="content">
                  <span class="name" v-html="roleFilterHighLight(i.name)"></span>
                  <el-tooltip v-if="i.remark" :open-delay="500" :content="i.remark">
                    <span class="remark" v-html="roleFilterHighLightWithEllipsis(i.remark)"></span>
                  </el-tooltip>
                </div>
              </div>
            </div>
            <table-empty v-else table-height="320"/>
          </div>
          <el-button plain slot="reference" class="role-popover"
            style="width: 100%; font-size: 14px; height: 32px"
            :style="{'color': form.roles.length ? '#182A4E' : '#94999D'}">
            <div class="checked-button" @mouseenter="selectMouseEnter('role')" @mouseleave="selectMouseLeave('role')">
              <div class="tags-box" v-if="form.roles.length">
                <el-tag
                  type="info"
                  closable
                  v-for="item in form.roles"
                  :key="item.id"
                  @close="handleRemoveTag(item, 'role')">{{item.name}}</el-tag>
              </div>
              <span v-else>{{ $t('请选择') }}</span>
              <i
                class="checked-button-icon"
                :class="{'el-icon-close':isRoleSelectMouseEnter,'el-icon-arrow-down':!isRoleSelectMouseEnter}"
                @click.stop="handleClean('role')"></i>
            </div>
          </el-button>
        </el-popover>
      </el-form-item>
      <el-form-item
        v-if="title!==$t('变更部门') && title!==$t('变更角色')"
        :prop="isInternalUser?'departments':'company'"
        :label="isInternalUser? $t('department') + '：' : $t('company') + '：'">
        <el-popover
          v-if="isInternalUser && dialogVisible"
          placement="bottom-start"
          popper-class="department-select"
          :width="popoverWidth"
          @show="popoverShow('department')"
          @hide="popoverHide">
          <user-nav-list
            ref="userNavList"
            :nav-tree-height="170"
            use-to-edit-user
            :default-checked-nodes="form.departments"
            @nodeCheck="handleNodeCheck"/>
          <el-button
            plain
            slot="reference"
            class="company-popover"
            style="width: 100%; font-size: 14px; height: 32px"
            :style="{'color': form.departments.length ? '#182A4E' : '#94999D'}">
            <div class="checked-button" @mouseenter="selectMouseEnter('department')" @mouseleave="selectMouseLeave('department')">
              <div class="tags-box" v-if="form.departments.length && isInternalUser">
                <el-tag
                  type="info"
                  closable
                  v-for="item in form.departments"
                  :key="item.id"
                  @close="handleRemoveTag(item, 'department')">{{item.name}}</el-tag>
              </div>
              <template v-else-if="form.departments.length && !isInternalUser">
                <span>{{form.departments[0].name}}</span>
              </template>
              <span v-else>{{ $t('请选择') }}</span>
              <i
                class="checked-button-icon"
                :class="{'el-icon-close':isDepartmentSelectMouseEnter,'el-icon-arrow-down':!isDepartmentSelectMouseEnter}"
                @click.stop="handleClean('department')"></i>
            </div>
          </el-button>
        </el-popover>
        <el-select
          v-else
          class="company-select"
          popper-class="company-select-popper"
          style="width: 100%"
          v-model="form.companyId"
          filterable
          clearable
          :disabled="editDisabled && !!form.id"
          :filter-method="handleCompanyFilter"
          :placeholder="$t('请选择')"
          @mouseenter.native="selectMouseEnter('department')"
          @mouseleave.native="selectMouseLeave('department')"
          @change="handleCompanyChange"
          @clear="handleCleanCompany"
          @visible-change="handleVisibleChange">
          <template slot="empty">
            <table-empty table-height="190px" :description="$t('noData')"></table-empty>
          </template>
          <div
            style="overflow: auto;height: 192px;position: relative"
            v-infinite-scroll="infiniteScrollLoad"
            :infinite-scroll-disabled="infiniteScrollLoading"
            :infinite-scroll-immediate="false">
            <el-option
              class="company-select-option"
              :class="'class' + item.id"
              v-for="item in collaborativeCompanyList"
              :key="item.id"
              :label="item.name"
              :value="item.id">
              <el-tooltip :open-delay="500" :content="item.name">
                <span v-html="companyFilterHighLight(item.name)"></span>
              </el-tooltip>
            </el-option>
            <div
              class="infinite-scroll-loading">
              <div class="scenator_loading" style="width: 30px; height: 3px">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <span>{{ $t('加载中') }}</span>
            </div>
          </div>
        </el-select>
      </el-form-item>
      <el-form-item v-if="title=== $t('变更部门')" prop="changedDepartments" :label="$t('部门') +'：'">
        <dropdown-selection-tree
          :default-checked-nodes="form.changedDepartments"
          @popoverShow="handlePopoverIsShow('department')"
          @nodeCheck="handleNodeCheck">
          <template #navTree>
            <nav-tree
              style="margin: 0 16px"
              ref="navTree"
              is-high-light
              :placeholder="$t('搜索部门')"
              tree-obj-id="changedDepartments"
              :tree-height="180"
              :empty-height="220"
              :is-default-select="false"
              :default-checked-nodes="form.changedDepartments"
              @checkedNodes="handleNodeCheck"
              @search="getDepartmentData"/>
          </template>
        </dropdown-selection-tree>
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button @click="handleClose">{{ $t('取消') }}</el-button>
      <el-button type="primary" :disabled='commitDisabled' @click="handleEdited">{{ $t('确定') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import ErrorHandler from '@/common/ErrorHandler'
import UserNavList from './UserNavList.vue'
import { userManagementApi } from '@/api/userManagement'
import TextTool from '@/utils/TextTool'
import TableEmpty from '@/components/base/TableEmpty.vue'
import { debounce } from 'lodash'
import NavTree from '@/components/base/navigation/tree_filter_position/Index'
import DropdownSelectionTree from '@/components/base/navigation/DropdownSelectionTree.vue'
import { userApi } from '@/api/uams'
import { roleManagementApi } from '@/api/role'
import store from '@/store'
export default {
  name: 'edit-po-property',
  components: { DropdownSelectionTree, NavTree, TableEmpty, UserNavList },
  props: {
    isInternalUser: {
      type: Boolean,
      default: true
    },
    editDisabled: {
      type: Boolean,
      default: () => false
    }
  },
  data () {
    return {
      dialogVisible: false,
      popoverWidth: 0,
      rolePopoverWidth: 0,
      commitDisabled: false,
      usernameLimit: 20,
      title: '',
      form: {
        id: '',
        username: '',
        name: '',
        mobilePhone: '',
        email: '',
        departments: [],
        company: '',
        companyId: '',
        changedDepartments: [],
        roles: [],
        position: ''
      },
      rules: {
        username: [
          { required: true, message: '用户名不能为空', trigger: 'change' },
          { validator: this.validateUserName, trigger: 'blur' }
        ],
        name: [
          { required: true, message: '姓名不能为空', trigger: 'change' },
          { validator: this.validateRealName, trigger: 'blur' }
        ],
        mobilePhone: [
          { validator: this.validatePhoneNumber, trigger: 'blur' }
        ],
        email: [
          { validator: this.validateEmail, trigger: 'blur' }
        ],
        departments: [
          { required: true, message: '所属部门不能为空', trigger: 'change' }
        ],
        company: [
          { required: true, message: '所属公司不能为空', trigger: 'change' }
        ],
        changedDepartments: [
          { required: true, message: '部门不能为空', trigger: 'change' }
        ]
      },
      collaborativeCompanyList: [],
      companyFilterValue: '',
      isDepartmentSelectMouseEnter: false,
      isRoleSelectMouseEnter: false,
      infiniteScrollNum: 1,
      infiniteScrollSize: 7,
      infiniteScrollTotal: 0,
      infiniteScrollLoading: false,
      userToDeptOrCompChangedIds: new Set(),
      multipleSelection: [],
      departmentsData: [],
      roleList: [],
      defaultRole: null,
      roleFilterValue: ''
    }
  },
  computed: {
    dialogBodyMaxHeight () {
      return 'calc(100vh - 96px - 92px - 32px)'
    }
  },
  mounted () {
    this.getUserNameLengthLimit()
    this.debounceFilterRoleList = debounce(this._filterRoleList, 500)
    this.debounce = debounce(this._filterCompany, 500)
  },
  watch: {
    dialogVisible (val) {
      if (val) {
        this.$nextTick(() => {
          this.rolePopoverWidth = document.querySelector('button.role-popover')?.offsetWidth
          this.popoverWidth = document.querySelector('button.company-popover')?.offsetWidth
        })
      }
    },
    companyFilterValue (val) {
      val = val?.trim()
      this.infiniteScrollNum = 1
      val && this.debounce()
      this.collaborativeCompanyList = []
      if (!val) {
        if (this.form.companyId) {
          this.collaborativeCompanyList[0] = { id: this.form.companyId, name: this.form.company }
        }
        this.getCollaborativeCompany()
      }
    },
    infiniteScrollLoading: {
      handler (val) {
        const loadingDom = document.querySelector('.el-popper.company-select-popper .infinite-scroll-loading')
        if (loadingDom) {
          if (val && this.collaborativeCompanyList.length) {
            loadingDom.style.cssText += 'display:flex!important'
          } else {
            loadingDom.style.cssText += 'display:none!important'
          }
        }
      },
      immediate: true
    },
    roleFilterValue (val) {
      val = val?.trim()
      if (val) {
        this.debounceFilterRoleList()
      } else {
        this.loadRoleList()
      }
    },
    roleList: {
      handler (val) {
        const map = this.form.roles.reduce((acc, obj) => {
          acc.set(obj.id, obj)
          return acc
        }, new Map())
        val.filter(item => item.checked).forEach(item => {
          map.set(item.id, item)
        })
        val.filter(item => !item.checked).forEach(item => {
          map.delete(item.id)
        })
        this.form.roles = [...map.values()]
      },
      deep: true
    }
  },
  methods: {
    async loadRoleList () {
      await this.getRoleList()
    },
    _filterRoleList () {
      this.getRoleList()
    },
    async getRoleList () {
      await roleManagementApi.loadRoles(this.roleFilterValue).then(({ data }) => {
        data = data.filter(item => item.name !== '公司管理员')
        data.forEach(item => {
          item.checked = this.form.roles.some(role => role.id === item.id)
          if (this.title === this.$t('add') && this.defaultRole && this.defaultRole.id === item.id) {
            item.checked = true
          }
        })
        const ir = data.find(item => this.defaultRole && item.id === this.defaultRole.id)
        const or = data.filter(item => this.defaultRole && item.id !== this.defaultRole.id)
        if (ir) {
          this.roleList = [ir, ...or]
        } else {
          this.roleList = [...or]
        }
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
    },
    async getDefaultRole() {
      await roleManagementApi.getDefaultRole().then(({data}) => {
        this.defaultRole = data
      })
    },
    roleFilterHighLight (label) {
      return TextTool.highlightFilters(label, this.roleFilterValue)
    },
    roleFilterHighLightWithEllipsis (label) {
      return TextTool.highlightFiltersWithEllipsis(
        '.role-list .remark',
        label, this.roleFilterValue, 12
      )
    },
    getUserNameLengthLimit () {
      userApi.getUserNameLengthLimit().then(({ data }) => {
        this.usernameLimit = data
      })
    },
    init () {
      this.collaborativeCompanyList = []
      this.companyFilterValue = ''
      this.isDepartmentSelectMouseEnter = false
      this.isRoleSelectMouseEnter = false
      this.infiniteScrollNum = 1
      this.infiniteScrollTotal = 0
      this.infiniteScrollLoading = false
      this.userToDeptOrCompChangedIds.clear()
      this.roleFilterValue = ''
      this.roleList = []
    },
    /**
     * 参数说明
     * @param title 标题
     * @param userData 用户数据
     * @param selectedData 选中的目录树数据
     */
    async openDialog (title, userData, selectedData) {
      this.form.companyId = ''
      Object.keys(this.form).forEach(key => {
        this.form[key] = (key === 'departments' || key === 'changedDepartments' || key === 'roles') ? [] : ''
      })
      this.init()
      this.title = title
      if (this.isInternalUser) {
        // 获取默认角色
        await this.getDefaultRole()
        if (this.title === this.$t('add')) {
          this.form.departments = [selectedData]
        } else {
          // 变更部门 / 变更角色
          this.multipleSelection = selectedData
          if (this.title === this.$t('变更角色')) {
            if (selectedData.length === 1) {
              this.form.roles = selectedData[0].roles ? JSON.parse(JSON.stringify(selectedData[0].roles)).filter(obj => obj.name !== '公司管理员') : []
            }
          } else {
            if (selectedData.length === 1) {
              this.form.changedDepartments = JSON.parse(JSON.stringify(selectedData[0].departments))
            }
            await this.getDepartmentData()
          }
        }
      } else {
        if (this.title === this.$t('变更角色')) {
          this.multipleSelection = selectedData
          if (selectedData.length === 1) {
            this.form.roles = selectedData[0].roles ? JSON.parse(JSON.stringify(selectedData[0].roles)).filter(obj => obj.name !== '公司管理员') : []
          }
        } else {
          if (selectedData?.id !== 'allUser') { // 协同公司 && 选中的不是“全部协同公司”
            this.form.company = selectedData?.name
            this.form.companyId = selectedData?.id
            this.collaborativeCompanyList.push({ id: selectedData?.id, name: selectedData?.name })
          }
        }
      }
      this.getCollaborativeCompany()
      if (userData) {
        this.form.id = userData.id
        this.form.username = userData.username.trim()
        this.form.name = userData.name.trim()
        this.form.mobilePhone = userData.mobilePhone
        this.form.email = userData.email?.trim()
        this.form.position = userData.position?.trim()
        this.form.departments = userData.departments || []
        this.form.roles = userData.roles ? userData.roles.filter(obj => obj.name !== '公司管理员') : []
        this.form.company = userData.company || this.form.departments[0]?.name
        this.form.companyId = userData.companyId || this.form.departments[0]?.id
        this.collaborativeCompanyList[0] = { id: this.form.companyId, name: this.form.company }
      }
      await this.loadRoleList()
      if (this.isInternalUser) {
        if (this.title === this.$t('edit')) {
          this.form.departments.map(e => e.id).forEach(id => {
            this.userToDeptOrCompChangedIds.add(id)
          })
        }
      } else {
        if (this.title !== this.$t('变更角色')) {
          this.userToDeptOrCompChangedIds.add(JSON.parse(JSON.stringify(this.form.companyId)))
        }
      }
      this.dialogVisible = true
      this.popperPositionListener()
    },
    async getCollaborativeCompany () {
      if (this.isInternalUser) return
      await userManagementApi.getCollaborativeCompany({
        keyWord: this.companyFilterValue?.trim(),
        pageNum: this.infiniteScrollNum,
        pageSize: this.infiniteScrollSize
      }).then(({ data }) => {
        this.infiniteScrollTotal = data.page.total
        if (this.collaborativeCompanyList[0]) {
          this.collaborativeCompanyList.push(...data.page.results.filter(item => item.id !== this.collaborativeCompanyList[0].id))
        } else {
          this.collaborativeCompanyList.push(...data.page.results)
        }
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
    },
    async _filterCompany () {
      if (this.companyFilterValue?.trim()) {
        await userManagementApi.getCollaborativeCompany({
          keyWord: this.companyFilterValue?.trim(),
          pageNum: this.infiniteScrollNum,
          pageSize: this.infiniteScrollSize
        }).then(({ data }) => {
          this.infiniteScrollTotal = data.page.total || 0
          this.collaborativeCompanyList.push(...data.page.results)
        }).catch(err => {
          ErrorHandler.formatError(err)
        })
      }
    },
    async infiniteScrollLoad () {
      if (this.infiniteScrollTotal > this.collaborativeCompanyList.length) {
        this.infiniteScrollNum++
        this.infiniteScrollLoading = true
        this.companyFilterValue?.trim() ? await this._filterCompany() : await this.getCollaborativeCompany()
        this.infiniteScrollLoading = false
      }
    },
    popperPositionListener () {
      if (!this.isInternalUser) return
      this.$nextTick(() => {
        const targetDom = this.$refs.form.$el.querySelector('.checked-button')
        if (!targetDom) return
        targetDom.querySelector('.checked-button-icon').style.height = targetDom.children[0]?.offsetHeight + 'px'
        const observer = new MutationObserver(() => {
          targetDom.style.height = targetDom.children[0]?.offsetHeight + 'px'
          targetDom.querySelector('.checked-button-icon').style.height = targetDom.children[0]?.offsetHeight + 'px'
          const domPosition = targetDom.getBoundingClientRect()
          const changedDom = document.querySelector('.el-popover.department-select')
          if (changedDom?.style?.top) {
            changedDom.style.top = `${Math.ceil(domPosition.bottom) + 1}px`
          }
        })
        observer.observe(targetDom, {
          attributes: false,
          childList: true,
          characterData: false,
          subtree: true
        })
      })
    },
    handleNodeCheck (data) {
      if (this.title === this.$t('变更部门')) {
        this.form.changedDepartments = data
      } else {
        this.form.departments = data
        data.map(e => e.id).forEach(id => {
          this.userToDeptOrCompChangedIds.add(id)
        })
      }
    },
    selectMouseEnter (type) {
      if (type === 'role') {
        if (this.form.roles?.length) {
          this.isRoleSelectMouseEnter = true
        }
      } else if (type === 'department') {
        if (this.isInternalUser && this.form.departments?.length) {
          this.isDepartmentSelectMouseEnter = true
        }
      }
    },
    selectMouseLeave (type) {
      if (type === 'role') {
        this.isRoleSelectMouseEnter = false
      } else if (type === 'department') {
        this.isDepartmentSelectMouseEnter = false
      }
    },
    handleClean (type) {
      if (type === 'role') {
        if (this.isRoleSelectMouseEnter) {
          this.form.roles = []
          this.roleList.forEach(item => {
            item.checked = false
          })
        } else {
          document.querySelector('button.role-popover').click()
        }
      } else if (type === 'department') {
        this.isDepartmentSelectMouseEnter && (this.form.departments = [])
      }
    },
    handleRemoveTag (item, type) {
      if (type === 'role') {
        const index = this.form.roles.findIndex(v => v.id === item.id)
        index >= 0 && this.form.roles.splice(index, 1)
        const roleItem = this.roleList.find(v => v.id === item.id)
        roleItem && (roleItem.checked = false)
      } else if (type === 'department') {
        const index = this.form.departments.findIndex(v => v.id === item.id)
        index >= 0 && this.form.departments.splice(index, 1)
      }
    },
    popoverShow (type) {
      this.$refs.form.$el.querySelector('.checked-button-icon') &&
      (this.$refs.form.$el.querySelector('.checked-button-icon').style.cssText += 'color:#fff;background:#0854a1')
      this.$nextTick(() => {
        if (type === 'role') {
          document.querySelector('.role-select').style.cssText += 'padding:8px 0 0!important;'
          document.querySelector('.role-select .popper__arrow').style.display = 'none'
        } else if (type === 'department') {
          document.querySelector('.department-select').style.cssText += 'padding:8px 0 0!important;'
          document.querySelector('.department-select .popper__arrow').style.display = 'none'
        }
      })
    },
    popoverHide () {
      const dom = this.$refs.form.$el.querySelector('.checked-button-icon')
      dom && dom.style.removeProperty('color')
      dom && dom.style.removeProperty('background')
      this.roleFilterValue = ''
    },
    handleCompanyFilter (value) {
      this.companyFilterValue = value?.trim()
    },
    handleCompanyChange (val) {
      this.form.companyId = val
      this.form.company = this.collaborativeCompanyList.find(item => item.id === val)?.name
    },
    handleCleanCompany () {
      this.form.company = ''
      this.form.companyId = ''
    },
    handleVisibleChange (val) {
      if (val) {
        // 协同公司新增用户弹窗，选择公司下拉框宽度跟随select框宽度
        this.$nextTick(() => {
          const inputBoxWidth = document.querySelector('.company-select').offsetWidth
          const selectOptionBox = document.querySelector('.el-popper.company-select-popper')
          if (inputBoxWidth && selectOptionBox) {
            selectOptionBox.style.width = inputBoxWidth + 'px'
            const loadingDom = selectOptionBox.querySelector('.infinite-scroll-loading')
            if (loadingDom) {
              loadingDom.style.cssText += 'position: sticky;\n' +
                '              left: 0;\n' +
                '              bottom: 0;\n' +
                '              width: 100%;\n' +
                '              height: 36px;\n' +
                '              display: none;\n' +
                '              align-items: center;\n' +
                '              justify-content: center;\n' +
                '              background-color: #fff;'
              loadingDom.children[loadingDom.children.length - 1].style.cssText += 'color: #182A4E;\n' +
                '                padding: 0 8px;'
            }
          }
        })
      }
    },
    handleEdited () {
      this.commitDisabled = true
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.commitDisabled = false
          return
        }
        let action
        const internalUserRelationTreeIds = []
        if (this.title === this.$t('变更部门')) {
          const body = []
          const departmentIds = this.form.changedDepartments.map(item => item.id)
          this.userToDeptOrCompChangedIds = new Set(departmentIds)
          this.multipleSelection.forEach(item => {
            item.departments.forEach(dept => {
              this.userToDeptOrCompChangedIds.add(dept.id)
            })
            body.push({
              userId: item.id,
              departmentIds
            })
          })
          action = userManagementApi.assignmentDepartmentApi(body)
        } else if (this.title === this.$t('变更角色')) {
          const body = []
          const roleIds = this.form.roles.map(item => item.id)
          this.multipleSelection.forEach(item => {
            body.push({
              userId: item.id,
              roleIds
            })
          })
          action = userManagementApi.assignmentRoleApi(body)
        } else {
          const body = {
            username: this.form.username?.trim(),
            name: this.form.name?.replace(/\s*/g, ''),
            mobilePhone: this.form.mobilePhone?.trim(),
            email: this.form.email?.trim(),
            departmentIds: this.isInternalUser ? this.form.departments.map(item => item.id) : [this.form.companyId],
            roleIds: this.form.roles.map(item => item.id),
            position: this.form.position?.trim()
          }
          if (this.form.id) {
            action = userManagementApi.editUser(this.form.id, body)
          } else {
            action = userManagementApi.addUser(body)
          }
          !this.isInternalUser && (this.userToDeptOrCompChangedIds.add(this.form.companyId))
          // 内部用户新增时，统计新增的用户的所有部门及其的父级部门id，如果选中的节点在这其中，则右侧用户列表要添加进去此用户
          if (this.isInternalUser) {
            const ref = this.$refs.userNavList.$refs.companyTree
            ref && this.form.departments.forEach(item => {
              this.getParentId(ref, ref.getNode(item.id), id => internalUserRelationTreeIds.push(id))
            })
            this.userToDeptOrCompChangedIds = new Set(internalUserRelationTreeIds)
          }
        }
        action.then(async ({ data }) => {
          /**
           * edited事件参数说明
           * @param {String} optType 操作类型，add新增，edit编辑，delete删除，upload导入,changeDepartment变更部门
           * @param {Object} userData 用户信息
           * @param {Set} userToDeptOrCompChangedIds 用户关联的部门或公司(此时不包含父级)id集合
           */
          this.$emit('edited',
            this.title === this.$t('变更部门') ? 'changeDepartment' : this.form.id || this.multipleSelection.length > 0 ? 'edit' : 'add',
            { ...data, ...this.form, id: data.id, internalUserRelationTreeIds },
            this.userToDeptOrCompChangedIds)
          this.$message.success(`${this.title === this.$t('add') ? this.$t('新增成功') : this.$t('修改成功')}`)
          const user = await store.getters.user
          const isEditCurrentUserSysRole = (
            (this.form.id && this.form.id === user.id) ||
            (this.title === this.$t('变更角色') && this.multipleSelection.some(item => item.id === user.id))
          ) && !this.form.roles.some(role => role.name === '系统管理员')
          if (isEditCurrentUserSysRole) {
            window.location.reload()
          }
        }).catch((err) => {
          ErrorHandler.formatError(err)
        }).finally(() => {
          this.handleClose()
        })
      })
    },
    getParentId (ref, node, cb) {
      cb && cb(node.data.id)
      if (node.data.parentId) {
        this.getParentId(ref, ref.getNode(node.data.parentId), cb)
      }
    },
    handleClose () {
      this.dialogVisible = false
      this.commitDisabled = false
    },
    companyFilterHighLight (label) {
      return TextTool.highlightFilters(label, this.companyFilterValue)
    },
    async getDepartmentData (searchText) {
      try {
        const { data } = await userManagementApi.getDepartment({ keyWord: searchText?.trim() || '' })
        this.departmentsData = data
        if (this.$refs.navTree) {
          this.initTree()
        }
      } catch (e) {
        ErrorHandler.formatError(e)
      }
    },
    handlePopoverIsShow (val) {
      if (val && this.$refs.navTree) {
        this.initTree()
      }
    },
    initTree () {
      this.$refs.navTree.initTree(this.departmentsData, null, {
        check: {
          enable: true,
          chkStyle: 'checkbox',
          chkboxType: { Y: '', N: '' }
        }
      })
    },
    async validateUserName (rule, value, callback) {
      if (value?.trim()) {
        let { data } = await userManagementApi.checkUserNameExist({
          username: value.trim()
        })
        if (this.form.id) {
          data = false
        }
        if (/[^0-9a-zA-Zа-яА-Я@_.-]/.test(value.trim())) {
          callback(new Error('仅支持数字、英文、"-"、"_"、"."、"@"的组合'))
        } else if (value.trim()?.length > this.usernameLimit || value.trim()?.length < 2) {
          callback(new Error('用户名长度需在2-' + this.usernameLimit + '个字符之间'))
        } else if (data) {
          callback(new Error('用户名已存在'))
        } else {
          callback()
        }
      } else {
        callback(new Error('用户名不能为空'))
      }
    },
    validateRealName (rule, value, callback) {
      if (value?.trim()) {
        if (/[`~!@$%^&*()+=|{}':;',[\]\\.<>?~！@￥%……&*（）——+|{}【】‘；：”“’。，、？"]/g.test(value.trim())) {
          callback(new Error('不能含有特殊字符'))
        } else if (value?.trim()?.length > 50 || value?.trim()?.length < 2) {
          callback(new Error('姓名长度需在2-50个字符之间'))
        } else {
          callback()
        }
      } else {
        callback(new Error('姓名不能为空'))
      }
    },
    async validatePhoneNumber (rule, value, callback) {
      if (value?.trim()) {
        const { data } = await userManagementApi.checkMobilePhoneExist({
          mobilePhone: value.trim(),
          userId: this.form.id
        })
        if (value?.trim()?.length !== 11 || !/^[0-9]*$/.test(value.trim())) {
          callback(new Error('请输入正确的手机号'))
        } else if (data) {
          callback(new Error('手机号已存在'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validateEmail (rule, value, callback) {
      if (value?.trim() && value?.trim()?.length > 100) {
        callback(new Error('最多可输入100个字'))
      } else if (value?.trim() && !/^[a-zA-Z0-9]+([._-]*[a-zA-Z0-9])*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value.trim())) {
        callback(new Error('请输入正确的邮箱'))
      } else {
        callback()
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label {
  &:before {
    content: '*' !important;
  }
  &:after {
    display: none;
  }
}
::v-deep .el-form-item__label, ::v-deep .el-form-item__content {
  line-height: 32px !important;
}

::v-deep .el-form {
  padding-top: 8px;
  margin-bottom: -10px;
}

//popover弹出框+button模拟下拉选择
.el-popover__reference{
  padding: 0 !important;
  border-color: #89919A !important;
  height: unset !important;
  overflow: hidden;
  &[disabled='disabled'] {
    background-color: rgba(242, 242, 242, 0.50) !important;
  }
  &:hover {
    border-color: #0854a1!important;
    background-color: #fff!important;
  }
  &:active,&:focus {
    border-color: #0854a1!important;
    background-color: #fff!important;
  }
  .checked-button-icon {
    width: 30px;
    min-height: 32px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0854a1;
    &:hover {
      color: #0854a1;
      background: rgba(0,129,255,0.12);
    }
    &:active {
      color: #FFFFFF;
      background: #0854A1;
    }
  }
  .checked-button {
    min-height: 32px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    > span {
      padding-left: 8px;
    }
  }
  .tags-box {
    overflow: auto;
    padding: 4px 8px 0;
    width: calc(100% - 32px);
    max-height: 84px;
    min-height: 27px;
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    span {
      margin: 0 6px 4px 0;
      height: 22px;
      line-height: 22px;
    }
  }
}

::v-deep .el-input__inner[disabled='disabled'] {
  opacity: 1 !important;
  color: #182A4E !important;
  background-color: rgba(242, 242, 242, 0.50) !important;
}

::v-deep .company-select {
  .el-input__suffix .el-icon-circle-close {
    &::before {
      content: '\e6db';
      font-weight: 400;
      font-size: 16px;
    }
  }
  .el-input__inner:focus + .el-input__suffix .el-icon-circle-close {
    color: #FFFFFF;
  }
}

::v-deep .checked-button .el-tag {
  border-radius: 2px !important;
  box-shadow: 0 0 0 1px rgba(24,42,78,0.12) !important;
  display: flex;
  align-items: center;
  &:hover {
    color: #0854a1 !important;
    box-shadow: 0 0 0 1px #0854A1 !important;
    background-color: rgba(0, 129, 255, 0.12) !important;
    .el-tag__close.el-icon-close {
      color: #0854a1 !important;
    }
  }
  .el-tag__close.el-icon-close {
    border-radius: 2px !important;
    line-height: 18px !important;
    top: 0 !important;
    &:hover {
      color: #0854a1 !important;
      box-shadow: 0 0 0 1px #0854A1 !important;
      background-color: rgba(0, 129, 255, 0.12) !important;
    }
    &:active {
      color: #FFFFFF !important;
      box-shadow: 0 0 0 1px #0854A1 !important;
      background-color: #0854A1 !important;
    }
  }
}

.el-select-dropdown .el-empty {
  padding-top: 20px !important;
  height: 190px !important;
}

::v-deep .edc_tree {
  margin-right: -16px !important;
  overflow: hidden auto !important;
  .node-tree {
    min-width: unset !important;
  }
  li {
    .checkbox_true_part::after,.checkbox_true_part_focus::after,.checkbox_false_disable::after {
      background: url("data:image/svg+xml;base64,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") no-repeat center !important;
    }
  }
}
.role-list {
  height: 226px;
  overflow: hidden auto;
  > div {
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    width: 100%;
    &:hover {
      background-color: #F2F3F5;
    }
    .el-checkbox {
      padding: 6px 8px 0 0;
      box-sizing: border-box;
    }
    .content {
      width: calc(100% - 24px);
      padding: 5px 0;
      box-sizing: border-box;
    }
    .name {
      display: block;
      height: 22px;
      font-size: 14px;
      color: #182A4E;
      line-height: 22px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .remark {
      display: block;
      height: 20px;
      font-size: 12px;
      color: #5D697A;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      ::v-deep > * {
        font-size: 12px !important;
      }
    }
  }
}

</style>
