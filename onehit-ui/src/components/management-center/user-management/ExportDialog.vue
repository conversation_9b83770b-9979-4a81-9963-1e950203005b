<template>
  <div>
    <el-dialog
      class="export-dialog"
      :title="dialogTitle"
      width="1024px"
      append-to-body
      :visible.sync="dialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :before-close="handleClose">
      <div
        class="export-dialog-wrapper"
        style="overflow: hidden auto"
        :style="{maxHeight: dialogBodyMaxHeight,minHeight: dialogBodyMinHeight}">
        <user-nav-list
          v-if="dialogVisible && isInternalUser"
          :nav-tree-height="parseInt(dialogBodyMinHeight) - 56"
          :use-to-edit-user="false"
          :default-checked-nodes="checkedNodes"
          :is-internal-user="isInternalUser"
          :is-change-department="dialogTitle===$t('变更部门')"
          @nodeCheck="handleNodeCheck"/>
        <!--协同公司数据量大，全选会导致浏览器卡死，这里另外布局然后直接操作dom。-->
        <div v-else class="export-list-wrapper">
          <el-input
            style="margin: 0 16px 8px;width: calc(100% - 32px);"
            :placeholder="$t('搜索协同公司')"
            clearable
            prefix-icon="el-icon-search"
            v-model.trim="searchText"/>
          <div v-if="companyList.length" class="export-list-box" ref="exportListBox">
            <div class="export-list-item" v-show="!searchText">
              <input type="checkbox" id="checkAllUser" @click="clickCheckAll"/>
              <i class="edc-icon-yonghu export-list-icon"></i>
              <span>{{ $t('checkAll') }}</span>
            </div>
            <div
              v-infinite-scroll="infiniteScrollLoad"
              :infinite-scroll-disabled="infiniteScrollLoading"
              :infinite-scroll-immediate="false"
              :style="{height: searchText ? '100%': 'calc(100% - 28px)'}"
              class="export-list">
              <div>
                <div
                  v-for="i in companyList"
                  :key="i.id"
                  class="export-list-item">
                  <input type="checkbox" :id="'id_'+i.id" @click="clickCheckItem(i)"/>
                  <i class="edc-icon-gongsi export-list-icon"></i>
                  <el-tooltip :open-delay="500" :content="i.name" :disabled="i.remark">
                    <span class="span_box" :id="i.id"  v-html="companyFilterHighLight(i.name)" @click="clickCheckItem(i, true)"></span>
                  </el-tooltip>
                </div>
              </div>
              <div
                v-show="infiniteScrollLoading && companyList.length"
                class="infinite-scroll-loading">
                <div class="scenator_loading" style="width: 30px; height: 3px">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <span>{{ $t('加载中') }}</span>
              </div>
            </div>
          </div>
          <table-empty v-else :table-height="parseInt(dialogBodyMinHeight) - 56 + 40" :description="$t('noData')"/>
        </div>
        <div class="checked-wrapper">
          <div class="statistics">
            <span>
              {{isInternalUser ? $t('已选{n}个部门', {count: calcNums.companies}):$t('已选{n}个协同公司', {count: calcNums.companies})}}<span v-if="dialogTitle!==$t('变更部门')">，
             {{ $t('共包含{n}名用户', {count: isInternalUser?checkedPeopleNum:calcNums.people}) }}
            </span></span>
            <el-link
              type="primary"
              :underline="false"
              :disabled="!checkedNodes.length"
              @click="handleClearAll">{{ $t('筛选对象_清空') }}</el-link>
          </div>
          <div
            v-infinite-scroll="infiniteScrollLoadRight"
            :infinite-scroll-disabled="infiniteScrollLoadingRight"
            :infinite-scroll-immediate="false"
            class="checked-box">
            <div
              v-show="checkedNodes.length"
              class="checked-list"
              v-for="i in checkedNodes"
              :key="i.id">
              <div class="checked-item">
                <div class="item-name">
                  <div class="edc-icon-zuzhijiagou"></div>
                  <el-tooltip :open-delay="500" :content="i.name" :disabled="i.remark">
                    <div :id="i.id" class="name_box">{{i.name}}</div>
                  </el-tooltip>
                </div>
                <el-tooltip :open-delay="500" v-if="isInternalUser" :content="i.path" :disabled="i.showTooltip">
                  <div :id="i.id" class="item-path">{{i.path}}</div>
                </el-tooltip>
              </div>
              <i class="checked-remove el-icon-close" @click="handleRemove(i)">
              </i>
            </div>
            <table-empty
              v-show="!checkedNodes.length"
              :table-height="40"
              :description="isInternalUser ? $t('请在目录树中选择部门或公司') : $t('请选择协同公司')"
              :image="emptyCursorImg"/>
          </div>
        </div>
      </div>
      <span slot="footer">
        <el-button @click="handleClose">{{ $t('取消') }}</el-button>
        <el-button
          type="primary"
          :disabled="!checkedNodes.length || isProcessing"
          @click="handleExportOrChange">{{dialogTitle===$t('变更部门') ? $t('确定'):$t('导出')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import UserNavList from './UserNavList.vue'
import TableEmpty from '@/components/base/TableEmpty.vue'
import { userManagementApi } from '@/api/userManagement'
import ErrorHandler from '@/common/ErrorHandler'
import TextTool from '@/utils/TextTool'
import { debounce, difference } from 'lodash'
import $ from 'jquery'
import FileUtil from '@/utils/FileUtil'
import { emptyCursorImg } from '@/utils/Base64ImgUtil'

export default {
  name: 'ExportDialog',
  components: { TableEmpty, UserNavList },
  props: {
    isInternalUser: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      emptyCursorImg,
      dialogTitle: '',
      dialogVisible: false,
      checkedNodes: [],
      uncheckedNodes: [], // 手动取消选中的节点，用于搜索和清除搜索后数据刷新，scrollLoadedChecked为true时后续滚动出来的节点取消选中
      isProcessing: false,
      companyList: [],
      searchText: '',
      isCompanyCheckAll: false, // 是否全选，会由某个子项的取消选中而变为false，因此不能用于判断后续滚动出来的是否要选中
      scrollLoadedChecked: false, // 后续滚动出来的是否要选中
      infiniteScrollNum: 1,
      infiniteScrollSize: 40,
      infiniteScrollTotal: 0,
      userTotal: 0,
      infiniteScrollLoading: false,
      infiniteScrollLoadingRight: false,
      departmentsToUserIds: {},
      checkedPeopleNum: 0,
      multipleSelection: []
    }
  },
  computed: {
    calcNums () {
      const data = {}
      if (this.isCompanyCheckAll || this.scrollLoadedChecked) {
        data.companies = this.infiniteScrollTotal - this.uncheckedNodes.length
        data.people = this.userTotal - this.uncheckedNodes.reduce((total, item) => total + item.userCount, 0)
      } else {
        data.companies = this.checkedNodes.length
        data.people = this.checkedNodes.reduce((total, item) => total + item.userCount, 0)
      }
      return data
    },
    dialogBodyMaxHeight () {
      return 'calc(100vh - 96px - 92px - 32px)'
    },
    dialogBodyMinHeight () {
      return window.innerHeight * 0.6 - 92 - 32 + 'px'
    }
  },
  watch: {
    async searchText (val) {
      this.infiniteScrollNum = 1
      this.companyList = []
      val && this.debounce && this.debounce()
      if (!val) {
        await this.getCompany()
      }
    },
    companyList (val) {
      if (val?.length) {
        this.$nextTick(() => {
          document.querySelectorAll('.span_box').forEach(dom => {
            const index = this.companyList.findIndex(item => item.id === dom.getAttribute('id'))
            this.companyList[index].remark = dom.scrollWidth < document.getElementsByClassName('export-list')[0].clientWidth
          })
        })
      }
    },
    checkedNodes (val) {
      this.checkedPeopleNum = 0
      let ids = []
      if (val.length) {
        this.$nextTick(() => {
          const parentWidth = document.getElementsByClassName('checked-list')[0].clientWidth
          document.querySelectorAll('.name_box').forEach(dom => {
            const index = this.checkedNodes.findIndex(item => item.id === dom.getAttribute('id'))
            if (!this.checkedNodes[index]?.remark) {
              this.checkedNodes[index].remark = dom.scrollWidth < parentWidth
            }
          })
          document.querySelectorAll('.item-path').forEach(dom => {
            const index = this.checkedNodes.findIndex(item => item.id === dom.getAttribute('id'))
            this.$set(this.checkedNodes[index], 'showTooltip', dom.scrollWidth < parentWidth)
          })
        })
        if (JSON.stringify(this.departmentsToUserIds) !== '{}') {
          val.map(item => item.id).forEach(id => {
            this.departmentsToUserIds[id]?.length && ids.push(...this.departmentsToUserIds[id])
          })
          ids = new Set(ids)
          this.checkedPeopleNum = ids.size
        }
      } else {
        this.$nextTick(() => {
          const emptyDom = document.querySelector('.checked-box .el-empty')
          emptyDom &&
          (emptyDom.style.cssText += `padding: ${emptyDom.parentElement.offsetHeight * 0.25}px 0 0 !important;`)
        })
      }
    }
  },
  methods: {
    init () {
      this.searchText = ''
      this.isCompanyCheckAll = false
      this.scrollLoadedChecked = false
      this.checkedNodes = []
      this.uncheckedNodes = []
      this.companyList = []
      this.infiniteScrollNum = 1
      this.infiniteScrollTotal = 0
      this.userTotal = 0
      this.infiniteScrollLoading = false
      this.infiniteScrollLoadingRight = false
      this.multipleSelection = []
    },
    openDialog (title, data) {
      this.init()
      this.multipleSelection = data
      if (this.isInternalUser) {
        this.getDepartmentsToUserIds()
      } else {
        this.getCompany()
      }
      this.dialogTitle = title
      this.dialogVisible = true
      this.$nextTick(() => {
        const emptyDom = document.querySelector('.checked-box .el-empty')
        emptyDom &&
        (emptyDom.style.cssText += `padding: ${emptyDom.parentElement.offsetHeight * 0.25}px 0 0 !important;`)
      })
    },
    getDepartmentsToUserIds () {
      userManagementApi.getDepartmentsToUserIds().then(({ data }) => {
        this.departmentsToUserIds = data
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
    },
    async getCompany () {
      await userManagementApi.getCollaborativeCompany({
        keyWord: this.searchText,
        pageNum: this.infiniteScrollNum,
        pageSize: this.infiniteScrollSize
      }).then(({ data }) => {
        this.infiniteScrollTotal = data.page.total
        this.userTotal = data.userTotal
        this.companyList.push(...data.page.results)
        !this.isInternalUser && this.handleDomListener(differenceBy(data.page.results, this.checkedNodes, 'id'))
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
      this.debounce = debounce(this._filterCompany, 500)
    },
    _filterCompany () {
      if (this.searchText?.trim()) {
        userManagementApi.getCollaborativeCompany({
          keyWord: this.searchText,
          pageNum: this.infiniteScrollNum,
          pageSize: this.infiniteScrollSize
        }).then(({ data }) => {
          this.infiniteScrollTotal = data.page.total || 0
          this.userTotal = data.userTotal || 0
          this.companyList.push(...data.page.results)
          !this.isInternalUser && this.handleDomListener(differenceBy(data.page.results, this.checkedNodes, 'id'))
        }).catch(err => {
          ErrorHandler.formatError(err)
        })
      }
    },
    async infiniteScrollLoad () {
      if (this.infiniteScrollTotal > this.companyList.length) {
        this.infiniteScrollNum++
        this.infiniteScrollLoading = true
        await this.getCompany()
        this.infiniteScrollLoading = false
      }
    },
    async infiniteScrollLoadRight () {
      if (!this.checkedNodes.length) return
      if (this.isCompanyCheckAll || this.scrollLoadedChecked) {
        if (this.infiniteScrollTotal > this.companyList.length) {
          this.infiniteScrollNum++
          this.infiniteScrollLoadingRight = true
          await this.getCompany()
          this.infiniteScrollLoadingRight = false
        }
      }
    },
    handleDomListener (differenceArr) {
      this.$nextTick(() => {
        if (this.$refs.exportListBox) {
          if (this.isCompanyCheckAll) {
            $('#checkAllUser')[0].checked = true
            $('.export-list-box input[id^=id_]').each(function () {
              $(this)[0].checked = true
            })
            this.companyList.forEach(item => {
              !this.checkedNodes.find(i => i.id === item.id) && this.checkedNodes.push(item)
            })
          }
          if (this.scrollLoadedChecked) {
            const checkingArr = differenceBy(differenceArr, this.uncheckedNodes, 'id')
            checkingArr.forEach(item => {
              !this.checkedNodes.find(i => i.id === item.id) && this.checkedNodes.push(item)
            })
          }
          !this.isCompanyCheckAll && this.checkedNodes.forEach(item => {
            const dom = $(`.export-list-box #id_${item.id}`)[0]
            dom && (dom.checked = true)
          })
          this.$refs.exportListBox.removeEventListener('click', this._listenerFunction)
          this.$refs.exportListBox.addEventListener('click', this._listenerFunction)
        }
      })
    },
    clickCheckAll () {
      if ($('#checkAllUser')[0].checked) {
        this.scrollLoadedChecked = true
        this.isCompanyCheckAll = true
        this.checkedNodes = [...this.companyList]
        this.uncheckedNodes = []
        $('.export-list-box input[id^=id_]').each(function () {
          $(this)[0].checked = true
        })
      } else {
        this.scrollLoadedChecked = false
        this.isCompanyCheckAll = false
        this.checkedNodes = []
        $('.export-list-box input[id^=id_]').each(function () {
          $(this)[0].checked = false
        })
      }
    },
    clickCheckItem (obj, clickText) {
      if (clickText) $(`.export-list-box #id_${obj.id}`)[0].checked = !$(`.export-list-box #id_${obj.id}`)[0].checked
      if ($(`.export-list-box #id_${obj.id}`)[0].checked) {
        let i = 0
        $('.export-list-box input[id^=id_]').each(function () {
          $(this)[0].checked && i++
        })
        if (i === this.infiniteScrollTotal && !this.searchText) {
          this.isCompanyCheckAll = true
          $('#checkAllUser')[0].checked = true
        }
        this.checkedNodes.push(obj)
        this.uncheckedNodes = this.uncheckedNodes.filter(item => item.id !== obj.id)
      } else {
        $('#checkAllUser')[0].checked = false
        this.isCompanyCheckAll = false
        this.uncheckedNodes.push(obj)
        this.checkedNodes.splice(this.checkedNodes.findIndex(item => item.id === obj.id), 1)
      }
    },
    _listenerFunction (e) {
      e.stopPropagation()
      // 取消所有行选中样式
      this.$refs.exportListBox.querySelectorAll('.export-list-item').forEach(item => {
        item.classList.remove('list-item-clicked')
      })
      // 目标行添加行选中样式
      e.target.closest('.export-list-item').classList.add('list-item-clicked')
    },
    handleNodeCheck (checkedNodes) {
      this.checkedNodes = checkedNodes
    },
    handleClearAll () {
      this.isCompanyCheckAll = false
      this.scrollLoadedChecked = false
      this.checkedNodes = []
      this.uncheckedNodes = []
      $('#checkAllUser')[0] && ($('#checkAllUser')[0].checked = false)
      $('.export-list-box input[id^=id_]').each(function () {
        $(this)[0].checked = false
      })
    },
    handleRemove (item) {
      this.isCompanyCheckAll = false
      $('#checkAllUser')[0] && ($('#checkAllUser')[0].checked = false)
      const dom = $(`.export-list-box #id_${item.id}`)[0]
      dom && (dom.checked = false)
      this.checkedNodes.splice(this.checkedNodes.findIndex(i => i.id === item.id), 1)
      this.uncheckedNodes.push(item)
    },
    async handleExportOrChange () {
      let checkedIds = this.checkedNodes.map(item => item.id)
      this.isProcessing = true
      // 导出部门/公司
      if (this.dialogTitle.toUpperCase().includes(this.$t('导出').toUpperCase())) {
        if (this.isInternalUser) {
          await FileUtil.downloadByPostUrl('/UAMS/user/manager/user/internal/export', checkedIds)
        } else {
          // 协同公司导出，如果全选过，就用全部的id-手动取消的id
          if (this.isCompanyCheckAll || this.scrollLoadedChecked) {
            try {
              const { data } = await userManagementApi.getCollaborativeCompany({ pageNum: 1, pageSize: 99999 })
              checkedIds = data.page.results.map(item => item.id)
            } catch (e) {
              ErrorHandler.formatError(e)
            }
          }
          checkedIds = difference(checkedIds, this.uncheckedNodes.map(item => item.id))
          await FileUtil.downloadByPostUrl('UAMS/user/manager/user/cooperate/export', checkedIds)
        }
        this.$emit('changeSuccess', this.dialogTitle)
        this.handleClose()
      } else {
        // 变更部门
        const body = []
        const departmentIds = this.checkedNodes.map(item => item.id)
        const userToDeptOrCompChangedIds = new Set(departmentIds)
        this.multipleSelection.forEach(item => {
          item.departments.forEach(dept => {
            userToDeptOrCompChangedIds.add(dept.id)
          })
          body.push({
            userId: item.id,
            departmentIds
          })
        })
        userManagementApi.assignmentDepartmentApi(body).then(res => {
          this.$emit('changeSuccess',
            this.dialogTitle,
            'changeDepartment',
            userToDeptOrCompChangedIds)
          this.handleClose()
          this.isProcessing = false
          this.$message.success(this.dialogTitle === this.$t('导出') ? this.$t('exportSuccess') : this.$t('修改成功'))
        }).catch(err => {
          this.isProcessing = err.message === 'cancel'
          ErrorHandler.formatError(err)
        }).finally(() => {
        })
      }
    },
    handleClose () {
      this.dialogVisible = false
      this.isProcessing = false
    },
    companyFilterHighLight (label) {
      return TextTool.highlightFilters(label, this.searchText)
    }
  }
}

/**
 * differenceBy 返回一个排除掉 values 数组的新数组
 * @param { array } array 要检查的数组
 * @param { array } values 排除的值
 * @param { string } iteratee 调用每个元素
 * @returns {*[]}
 */
function differenceBy (array, values, iteratee) {
  if (!Array.isArray(array) || !Array.isArray(values)) {
    return []
  }
  const set = new Set(values.map(value => value[iteratee]))
  return array.filter(item => !set.has(item[iteratee]))
}
</script>

<style scoped lang="scss">
.export-dialog-wrapper {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  border: 1px solid #E5E5E5;
  height: fit-content;
  > div {
    width: 50%;
    overflow: hidden;
    &:first-child {
      border-right: 1px solid #E5E5E5;
      padding: 16px 0 0;
      ::v-deep .user-tree-wrapper {
        height: calc(100% - 40px) !important;
      }
    }
  }
  .export-list-wrapper {
    .export-list-box {
      height: calc(100% - 40px);
      margin-left: 16px;
      .export-list-item {
        height: 28px;
        line-height: 28px;
        white-space: nowrap;
        color: #182A4E;
        display: flex;
        align-items: center;
        &:hover {
          background-color: #EFF2F4;
        }
        > input[type='checkbox'] {
          width: 16px;
          height: 16px;
          border: 1px solid #89919A;
          box-sizing: border-box;
          border-radius: 2px;
          transition: border-color 0.25s;
          background-color: #FFFFFF;
          outline: none;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          position: relative;
          &:hover {
            border-color: #0854a1;
          }
          &:checked {
            &:before {
              position: absolute;
              content: '';
              background: url("data:image/svg+xml;base64,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") no-repeat center center;
              font-size: 16px;
              left: 0;
              right: 0;
              top: 0;
              bottom: 0;
            }
          }
        }
      }
      .export-list {
        overflow: auto;
        > div:nth-child(1) {
          min-width: fit-content;
        }
      }
      .export-list-icon {
        padding: 0 6px;
        color: #0854a1;
        &.edc-icon-yonghu::before {
          font-size: 12px;
        }
      }
    }
  }
  .checked-wrapper {
    .statistics {
      display: flex;
      justify-content: space-between;
      padding: 0 16px;
      span {
        display: inline-block;
        height: 32px;
        line-height: 32px;
        font-size: 12px;
        color: #5D697A;
      }
    }
  }
}
.checked-box {
  height: calc(100% - 32px);
  overflow: hidden auto;
  margin: 0 8px;
  .checked-list {
    &:hover {
      background-color: #EFF2F4;
    }
    margin-bottom: 8px;
    border: 1px solid #E5E5E5;
    border-radius: 2px;
    padding: 8px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .checked-item {
      width: calc(100% - 32px);
      .item-name {
        display: flex;
        align-items: center;
        height: 20px;
        line-height: 20px;
        color: #182A4E;
        div:first-child {
          color: #0854a1;
          margin-right: 4px;
        }
        div:last-child {
          width: calc(100% - 20px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .item-path {
        height: 20px;
        line-height: 20px;
        color: #5D697A;
        padding-left: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .checked-remove {
      width: 16px;
      height: 16px;
      border: 1px solid transparent;
      box-sizing: border-box;
      border-radius: 2px;
      cursor: pointer;
      &:hover {
        border-color: #0854a1;
        background-color: rgba(0, 129, 255, 0.12);
        color: #0854a1;
      }
      &:active {
        border-color: #0854a1;
        background-color: #0854a1;
        color: #FFFFFF;
      }
    }
  }
  .el-empty {
    height: 100% !important;
    overflow: hidden;
    ::v-deep .el-empty__description {
      position: static;
    }
  }
}
.infinite-scroll-loading {
  position: sticky;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  z-index: 2;
  > span {
    color: #182A4E;
    padding: 0 8px;
  }
}
.list-item-clicked, list-item-clicked:hover {
  background-color: rgba(0, 129, 255, 0.12) !important;
  color: #0854a1 !important;
}
</style>
