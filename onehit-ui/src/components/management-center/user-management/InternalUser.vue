<template>
  <div style="margin: -16px 0 -16px -16px">
    <split-panes class="box default-theme internal-user" :dbl-click-splitter="false">
      <pane :size="leftPaneSize" :min-size="leftPaneMinSize" max-size="50">
        <user-nav-tree
          ref="userNavTree"
          @handleNodeClick="handleNodeClick"/>
      </pane>
      <pane :size="100 - leftPaneSize" style="border-left: 1px solid #d9d9d9">
        <user-table
          style="padding-left: 16px"
          :current-tree-data="currentTreeData"
          is-internal-user
          @uploadSuccess="handleUploadedSuccess"
          @changedUserToTreeData="handleChangedData"/>
      </pane>
    </split-panes>
  </div>
</template>

<script>
import { Splitpanes as SplitPanes, Pane } from 'splitpanes'
import UserNavTree from './UserNavTree.vue'
import UserTable from './UserTable.vue'

export default {
  name: 'InternalUser',
  components: {
    UserTable,
    UserNavTree,
    SplitPanes,
    Pane
  },
  data () {
    return {
      leftPaneSize: 0,
      leftPaneMinSize: 0,
      currentTreeData: null
    }
  },
  computed: {
    isPause () {
      return this.$root.isPause
    }
  },
  watch: {
    isPause (val) {
      if (!val) {
        this.$refs.userNavTree && this.$refs.userNavTree.init()
      }
    }
  },
  mounted () {
    this.calcPanesMinMaxWidth()
  },
  methods: {
    handleNodeClick (data) {
      this.currentTreeData = data
    },
    /**
     * 上传成功后
     * @param noChanged 是否有新增或删除过树节点
     */
    handleUploadedSuccess (noChanged) {
      // 树节点没有删除或新增，就只改变人数，此时不知道上传后哪些树节点的人数有变动过，所以整个变
      if (noChanged) {
        this.handleChangedData('upload', null)
      } else {
        // 树节点变了，就重刷树
        this.$refs.userNavTree && this.$refs.userNavTree.init()
      }
    },
    /**
     * @param {String} optType 操作类型
     * @param {Set} userToDeptOrCompChangedIds 用户关联的部门或公司(此时不包含父级)id集合
     */
    handleChangedData (optType, userToDeptOrCompChangedIds) {
      this.$refs.userNavTree.handleChangedNodes(optType, userToDeptOrCompChangedIds)
    },
    calcPanesMinMaxWidth () {
      let paneWrapperWidth = document.querySelector('.internal-user')?.parentElement?.clientWidth
      if (!paneWrapperWidth) return
      paneWrapperWidth = paneWrapperWidth - 8
      this.leftPaneSize = 312 / paneWrapperWidth * 100
      this.leftPaneMinSize = 240 / paneWrapperWidth * 100
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .splitpanes__pane {
  padding: 16px 0;
}
</style>
