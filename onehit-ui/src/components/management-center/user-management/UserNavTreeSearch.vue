<template>
  <ul
    class="search_result"
    v-infinite-scroll="infiniteScrollLoad"
    :infinite-scroll-disabled="loading"
    :infinite-scroll-immediate="false"
    :style="{height: height + 'px'}">
    <p v-if="total" style="color: #5D697A;margin-bottom: 8px">筛选结果共{{total}}条</p>
    <div v-if="list.length"  style="position:relative;">
      <li v-for="item in list"
          :key="item.id + item.path"
          @click="locationNode(item)"
          class="search_li"
          :class="{'search_li_active':(item.path+item.id) === (curentClickItem.path+curentClickItem.id)}">
        <span class="edc-icon-zuzhijiagou" style="margin-right: 2px;color: #0854a1;position: absolute; left: 8px"></span>
        <div>
          <el-tooltip :open-delay="500" effect="dark" :content="item.name" placement="bottom">
            <span
              style="white-space: nowrap;text-overflow: ellipsis;overflow: hidden;display: block;"
              v-html="nameFilterHighLight(item.name)"></span>
          </el-tooltip>
        </div>
        <el-tooltip class="" :open-delay="500" effect="dark" :content="item.path" placement="bottom">
          <span class="search_item_cate">{{ item.path }}</span>
        </el-tooltip>
      </li>
    </div>
    <table-empty v-show="!loading" v-else :table-height="height + 40" :description="$t('noData')"/>
    <div v-show="loading" class="scroll-loading">
      <div class="scenator_loading" style="width: 26px; height: 3px">
        <span></span>
        <span></span>
        <span></span>
      </div>
      <span style="margin-left: 5px;color: #182A4E">加载中</span>
    </div>
  </ul>
</template>
<script>
import tableEmpty from '@/components/base/TableEmpty.vue'
import TextTool from '@/utils/TextTool'
export default {
  props: ['list', 'searchText', 'total', 'loading'],
  components: {
    tableEmpty
  },
  data () {
    return {
      curentClickItem: {}
    }
  },
  computed: {
    height () {
      return window.innerHeight - 50 - 32 - 40 - 54
    }
  },
  methods: {
    nameFilterHighLight (name) {
      return TextTool.highlightFilters(name, this.searchText)
    },
    locationNode (item) {
      this.curentClickItem = item
      this.$emit('locationNode', item)
    },
    infiniteScrollLoad () {
      if (this.total > this.list.length) {
        this.$emit('loadmore')
      }
    }
  }

}
</script>
<style lang="scss" scoped>
*{
  margin: 0;
  padding: 0;
  list-style: none;
  box-sizing: border-box;
}
.search_result{
  flex: 1;
  overflow: auto;
  padding: 0 16px;
  font-size: 14px;
  position: relative;
}
.search_result .search_li{
  width: 100%;
  position: relative;
  padding: 8px 9px 8px 28px;
  border: 1px solid rgba(24,42,78,0.12);
  border-radius: 2px;
  cursor: pointer;
  margin-bottom: 16px;
  background: rgba(24,42,78,0.06);
}
.search_result .search_li_active,
.search_result .search_li:hover{
  border: 1px solid #0854A1;
  background: rgba(0,129,255,0.12);
}
.search_item_name{
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  color: #182A4E;
  display: inline-block;
  max-width: 100%;
}
.search_item_ech{
  width: 8px;
  height: 8px;
  float: left;
  border-radius: 4px;
  margin-left: -12px;
  margin-top: 6px;
}
.search_item_cate{
  color: #5D697A;
  margin-top: 4px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
  display: inline-block;
}
.high-color{
  color: #E9730C;
}
.scroll-loading {
  position: sticky;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}
</style>
