<template>
  <div>
    <table-toolbar :filterLength="selectFilterData.length" ref="tableToolbar">
      <template v-slot:filter>
        <select-filter-group
          ref="selectFilterGroup"
          :select-filter-data="selectFilterData"
          @changeChecked="handleFilterChange">
        </select-filter-group>
      </template>
    </table-toolbar>
    <div
      style="display: flex;align-items: center;justify-content: space-between;margin: 16px 0">
      <div>
        <el-button
          type="primary"
          @click="addUser">
          {{$t('add')}}
        </el-button>
        <el-button
          type="primary"
          plain
          @click="batchUpload">
         {{$t('批量导入/导出')}}
        </el-button>
        <el-button
          type="primary"
          plain
          :disabled="!multipleSelection.length"
          @click="changeRole">
          {{$t('变更角色')}}
        </el-button>
        <el-button
          v-show="isInternalUser"
          type="primary"
          plain
          :disabled="!multipleSelection.length"
          @click="changeDepartment">
          {{$t('变更部门')}}
        </el-button>
        <el-button
          type="primary"
          plain
          :disabled="!multipleSelection.length"
          @click="batchDelete">
          {{$t('batchDelete')}}
        </el-button>
        <el-switch
          v-show="isInternalUser"
          style="transform: scale(0.6);margin-left: 9px"
          v-model="includeSubDepartment">
        </el-switch>
        <span v-show="isInternalUser" style="color: #182A4E">{{$t('展示子部门用户')}}</span>
      </div>
      <el-input
        style="width: 240px"
        :placeholder="$t('搜索用户')"
        clearable
        prefix-icon="el-icon-search"
        v-model.trim="searchText"/>
    </div>
    <div :style="{height: tableIsLoading ? parseInt(tableHeight) + 'px' : 'fit-content'}"
         style="position: relative">
      <table-loading :table-height="tableHeight" v-show="tableIsLoading"/>
      <el-table
        border
        stripe
        auto-fit-column
        ref="userTable"
        highlight-current-row
        :data="tableData"
        :max-height="tableHeight"
        :row-class-name="rowClassName"
        :header-cell-style=" { background: '#EFEFEF'}"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange">
        <el-table-column
          type="selection"
          fixed="left"
          width="42">
        </el-table-column>
        <el-table-column
          fixed="left"
          prop="username"
          sortable="custom"
          :label="multipleSelection.length ? $t('选中数量Tip', {length: multipleSelection.length}) : $t('username')"
          show-overflow-tooltip>
          <template v-slot="{ row }">
            <span v-html="filterHighLight(row.username)"></span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="left"
          prop="name"
          sortable="custom"
          :label="$t('name')"
          show-overflow-tooltip>
          <template v-slot="{ row }">
            <span v-html="filterHighLight(row.name.replace(/\s*/g, ''))"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="mobilePhone"
          :label="$t('mobilePhone')"
          show-overflow-tooltip>
          <template v-slot="{ row }">
            <span v-html="filterHighLight(row.mobilePhone)"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="email"
          :label="$t('email')"
          show-overflow-tooltip>
          <template v-slot="{ row }">
            <span v-html="filterHighLight(row.email)"></span>
          </template>
        </el-table-column>
       <el-table-column
          max-width="200"
          prop="position"
          :label="$t('职位')"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="roleName"
          :label="$t('角色')"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="departmentName"
          :label="isInternalUser ? $t('department') : $t('company')"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          :label="$t('control')"
          width="80"
          fixed="right">
          <template v-slot="{ row }">
            <el-link
              :underline="false"
              type="primary"
              @click="editUser(row)">{{ $t('edit') }}
            </el-link>
            <el-dropdown
              placement="bottom-end"
              trigger="click"
              @command="command => handleCommand(command, row)"
              @visible-change="val => handleVisibleChange(val, row)">
              <el-button type="primary" plain icon="el-icon-more"/>
              <el-dropdown-menu slot="dropdown" class="user_table_dropdown">
                <el-dropdown-item command="changeRole">{{ $t('变更角色') }}</el-dropdown-item>
                <el-dropdown-item command="changeDepartment" v-show="isInternalUser">{{ $t('变更部门') }}</el-dropdown-item>
                <el-dropdown-item command="resetPwd" v-show="row.id!==userSelfId">{{ $t('重置密码') }}</el-dropdown-item>
                <el-dropdown-item command="delete" v-show="row.id!==userSelfId">{{ $t('delete') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
        <template slot="empty">
          <el-empty :image="emptyDataImg" :style="{height: height + 'px', padding: padding}" v-show="!tableIsLoading">
            <template slot="description">
                <span v-if="currentTreeData && currentTreeData.userCount && !includeSubDepartment && !this.searchText && currentTreeData.children">当前部门没有直属用户，可选择
                  <span class="empty_text" @click="showSubUser">{{ $t('展示下级部门用户') }}</span></span>
              <span v-else>{{ $t('noData') }}</span>
            </template>
          </el-empty>
        </template>
      </el-table>
      <el-pagination
        v-if="tableData && tableData.length"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="sizes, prev, pager, next, jumper, ->, total"
        :total="total">
      </el-pagination>
    </div>
    <related-data-opt-dialog
      ref="relatedDataOptDialog"
      @handleAbsentSkip="closeDelChkDialogVisible">
      <template #title>{{ $t('用户账号下存在未审批结束的申请单，暂不支持删除') }}</template>
      <template #footerButton>
        <el-button @click="closeDelChkDialogVisible">{{ $t('关闭') }}</el-button>
      </template>
    </related-data-opt-dialog>
    <edit-dialog
      ref="editDialog"
      :isInternalUser="isInternalUser"
      :editDisabled="editDisabled"
      @edited="handleEdited"/>
    <upload-dialog
      ref="uploadDialog"
      :isInternalUser="isInternalUser"
      @uploaded="handleUploaded"
      @exportUser="handleExportUser"/>
    <export-dialog
      ref="exportDialog"
      :isInternalUser="isInternalUser"
      @changeSuccess="changeSuccess"/>
  </div>
</template>

<script>
import TableLoading from '@/components/base/TableLoading.vue'
import RelatedDataOptDialog from '@/components/base/RelatedDataOptDialog'
import { debounce } from 'lodash'
import TextTool from '@/utils/TextTool'
import { userManagementApi } from '@/api/userManagement'
import EditDialog from './EditDialog.vue'
import UploadDialog from './UploadDialog.vue'
import ExportDialog from './ExportDialog.vue'
import store from '@/store'
import ErrorHandler from '@/common/ErrorHandler'
import { emptyDataImg } from '@/utils/Base64ImgUtil'
import SelectFilterGroup from '@/components/base/SelectFilterGroup.vue'
import TableToolbar from '@/components/base/TableToolbar'

export default {
  name: 'UserTable',
  components: { SelectFilterGroup, TableToolbar, ExportDialog, TableLoading, EditDialog, UploadDialog, RelatedDataOptDialog },
  props: {
    currentTreeData: {
      type: Object,
      default: () => null
    },
    editDisabled: {
      type: Boolean,
      default: () => false
    },
    isInternalUser: {
      type: Boolean,
      default: () => false
    }
  },
  data () {
    return {
      store,
      emptyDataImg,
      includeSubDepartment: true,
      searchText: '',
      tableData: [],
      multipleSelection: [],
      tableIsLoading: true,
      sortOpts: {
        userNameOrder: null,
        nameOrder: null
      },
      filterAndValue: {},
      total: 0,
      currentPage: 1,
      pageSize: 30,
      selectFilterData: [{ title: this.$t('角色'), checkboxData: [], showInput: true, paramKey: 'roleIds' }]
    }
  },
  computed: {
    tableHeight () {
      return window.innerHeight - 50 - 32 - 48 - 64 - (this.tableData?.length ? 48 : 0)
    },
    height () {
      return parseInt(this.tableHeight) - 40
    },
    padding () {
      // 在1/4处显示
      const paddingTop = this.height * 0.25
      return `${paddingTop}px 0 0`
    },
    isPause () {
      return this.$root.isPause
    },
    userSelfId () {
      return store.state.user?.id
    },
    titleToParamKey () {
      const map = new Map()
      this.selectFilterData.forEach(({ title, paramKey }) => {
        map.set(title, paramKey)
      })
      return map
    }
  },
  watch: {
    isPause (val) {
      if (!val) {
        this.searchText = ''
        this.includeSubDepartment = true
      }
    },
    'sortOpts.userNameOrder' () {
      this.debounce()
    },
    'sortOpts.nameOrder' () {
      this.debounce()
    },
    currentTreeData (val) {
      if (val?.id) {
        this.currentPage = 1
        this.getUserList()
      } else {
        this.tableData = []
        this.tableIsLoading = false
      }
    },
    includeSubDepartment () {
      this.currentPage = 1
      this.getUserList()
    },
    searchText () {
      this.currentPage = 1
      this.debounce()
    }
  },
  mounted () {
    this.debounce = debounce(this.getUserList, 500)
  },
  methods: {
    measureText (pText, pFontSize, pStyle) {
      return TextTool.measureText(pText, pFontSize, pStyle)
    },
    getUserList () {
      const handler = (baseData) => {
        if (baseData) {
          return baseData.map(item => {
            return {
              value: item.id || this.$t('无'),
              text: item.name || this.$t('无'),
              labelText: item.name || this.$t('无')
            }
          })
        }
        return []
      }
      this.$refs.userTable && this.$refs.userTable.clearSelection()
      this.tableIsLoading = true
      const action = this.isInternalUser ? userManagementApi.getInternalUser : userManagementApi.getCollaborativeUser
      action({
        ...this.sortOpts,
        ...this.filterAndValue,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        deptId: (this.currentTreeData?.id === 'allUser' ||
          (this.isInternalUser && !this.currentTreeData?.parentId && this.includeSubDepartment)) ? '' : this.currentTreeData?.id,
        includeSubFolder: this.includeSubDepartment,
        mobilePhoneLike: this.searchText,
        userNameLike: this.searchText,
        nameLike: this.searchText,
        emailLike: this.searchText
      }).then(({ data }) => {
        this.tableData = data.page.results
        if (!this.tableData) {
          this.tableData = [];
        }
        this.tableData.forEach(item => {
          item.roleName = item.roles ? item.roles.filter(obj => obj.name !== '公司管理员').map(role => role.name).join('、') : []
          item.departmentName = item.departments.map(dept => dept.name).join('、')
        })
        this.total = data.page.total
        this.selectFilterData[0].checkboxData = handler(data.roles)
        this.tableIsLoading = false
      }).catch((err) => {
        this.tableIsLoading = err.message === 'cancel'
      })
    },
    handleFilterChange (allFilter, title) {
      this.currentPage = 1
      allFilter.forEach(({ title, checkedValues }) => {
        this.$set(this.filterAndValue, this.titleToParamKey.get(title), checkedValues)
      })
      this.debounce()
    },
    addUser () {
      this.$refs.editDialog.openDialog(this.$t('add'), null, this.currentTreeData)
    },
    checkUserSearch (rowUserData) {
      return rowUserData.name.includes(this.searchText) || rowUserData.username.includes(this.searchText) ||
      rowUserData.email.includes(this.searchText) || rowUserData.mobilePhone.includes(this.searchText)
    },

    /**
     * 事件参数
     * @param {String} optType 操作类型，add新增，edit编辑，delete删除，upload导入
     * @param {Object} rowUserData 用户信息
     * @param {Set} userToDeptOrCompChangedIds 用户关联的部门或公司(此时不包含父级)id集合
     */
    handleEdited (optType, rowUserData, userToDeptOrCompChangedIds) {
      this.$emit('changedUserToTreeData', optType, userToDeptOrCompChangedIds)
      if (optType === 'add') {
        // 判断在当前节点下，右侧用户列表在新增后加不加上去
        const isAddCurTable = (this.includeSubDepartment && rowUserData.internalUserRelationTreeIds.includes(this.currentTreeData?.id)) ||
          rowUserData.departments.some(dept => dept.id === this.currentTreeData?.id) ||
          this.currentTreeData?.id === 'allUser' ||
          this.currentTreeData?.id === rowUserData.companyId
        if (isAddCurTable) {
          if (!this.searchText) {
            this.total++
            this.tableData.unshift(rowUserData)
          } else if (this.checkUserSearch(rowUserData)) {
            this.total++
            this.tableData.unshift(rowUserData)
          }
          this.tableData[0].roleName = this.tableData[0].roles ? this.tableData[0].roles.filter(obj => obj.name !== '公司管理员').map(role => role.name).join('、') : []
          if (this.isInternalUser) {
            this.tableData[0].departmentName = this.tableData[0].departments.map(dept => dept.name).join('、')
          } else {
            this.tableData[0].departmentName = this.tableData[0].company
            this.tableData[0].departments = [{
              id: this.tableData[0].companyId,
              name: this.tableData[0].company
            }]
          }
        }
        if (this.tableData.length > this.pageSize) {
          this.tableData.pop()
        }
      } else {
        this.getUserList()
      }
    },
    batchUpload () {
      this.$refs.uploadDialog.openDialog(this.isInternalUser)
    },
    /**
     * 参数说明
     * @param noChanged 是否有树节点的新增删除
     */
    handleUploaded (noChanged = false) {
      this.$emit('uploadSuccess', noChanged)
      this.getUserList()
    },
    handleExportUser () {
      this.$refs.exportDialog.openDialog(this.$t('选择导出范围'))
    },
    changeDepartment () {
      this.$refs.editDialog.openDialog(this.$t('变更部门'), null, this.multipleSelection)
    },
    changeRole () {
      this.$refs.editDialog.openDialog(this.$t('变更角色'), null, this.multipleSelection)
    },
    async batchDelete () {
      const ids = []
      const userToDeptOrCompChangedIds = new Set()
      this.multipleSelection.forEach(item => {
        ids.push(item.id)
        item.departments.forEach(dept => {
          userToDeptOrCompChangedIds.add(dept.id)
        })
      })
      if (ids.includes(this.userSelfId)) {
        this.$message.warning(this.$t('不支持删除本人'))
        return
      }
      this.$confirm(this.$t('用户删除后不可恢复'), this.$t('提示'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'error'
      }).then(() => {
        userManagementApi.deleteUsers(ids).then(() => {
          this.getUserList()
          this.$emit('changedUserToTreeData', 'delete', userToDeptOrCompChangedIds)
          this.$message.success(this.$t('删除成功'))
        }).catch(err => {
          ErrorHandler.formatError(err)
        })
      }).catch(() => {})
    },
    editUser (row) {
      this.$refs.editDialog.openDialog(this.$t('edit'), row, this.currentTreeData, this.isInternalUser)
    },
    async handleCommand (command, row) {
      if (command === 'changeDepartment') {
        this.$refs.editDialog.openDialog(this.$t('变更部门'), null, [row])
      } else if (command === 'changeRole') {
        this.$refs.editDialog.openDialog(this.$t('变更角色'), null, [row])
      } else if (command === 'resetPwd') {
        this.$confirm(this.$t('确定重置密码？'), this.$t('提示'), {
          confirmButtonText:  this.$t('确定'),
          cancelButtonText: this.$t('取消'),
          type: 'warning'
        }).then(() => {
          userManagementApi.resetPasswordApi(row.id).then(() => {
            this.$message.success(this.$t('重置密码成功'))
          }).catch(err => {
            ErrorHandler.formatError(err)
          })
        }).catch(() => {})
      } else if (command === 'delete') {
        this.$confirm(this.$t('用户删除后不可恢复'), this.$t('提示'), {
          confirmButtonText:  this.$t('确定'),
          cancelButtonText: this.$t('取消'),
          type: 'error'
        }).then(() => {
          const userToDeptOrCompChangedIds = new Set()
          row.departments.forEach(dept => {
            userToDeptOrCompChangedIds.add(dept.id)
          })
          userManagementApi.deleteUsers([row.id]).then(() => {
            this.getUserList()
            this.$emit('changedUserToTreeData', 'delete', userToDeptOrCompChangedIds)
            this.$message.success(this.$t('删除成功'))
          }).catch(err => {
            ErrorHandler.formatError(err)
          })
        }).catch(() => {})
      }
    },
    async checkEnableDelete (ids) {
      const { data } = await userManagementApi.checkEnableDelete(ids)
      if (data?.length) {
        this.$refs.relatedDataOptDialog.open(this.$t('提示'), data)
      }
      return !data?.length
    },
    handleVisibleChange (visible, row) {
      if (row?.id) {
        this.$refs.userTable.$el.querySelectorAll(`.rowId${row.id} .el-dropdown button`).forEach(item => {
          if (visible) {
            item.style.cssText += 'background-color:#0854A1!important;color:#FFFFFF!important'
          } else {
            item.style.cssText += 'background-color:transparent!important;color:#0854A1!important'
          }
        })
      }
    },
    showSubUser () {
      this.includeSubDepartment = true
    },
    filterHighLight (text) {
      return TextTool.highlightFilters(text, this.searchText)
    },
    sortChange (column) {
      let order = column.order
      let prop = column.prop
      switch (order) {
        case 'descending':
          order = 'DESC'
          break
        case 'ascending':
          order = 'ASC'
          break
        default:
          order = null
      }
      if (prop === 'username') {
        prop = 'userName'
      }
      this.sortOpts[prop + 'Order'] = order
      Object.keys(this.sortOpts).forEach(item => {
        if (item !== prop + 'Order') {
          this.sortOpts[item] = null
        }
      })
    },
    rowClassName ({ row }) {
      return 'rowId' + row.id
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    handleSizeChange (size) {
      this.pageSize = size
      this.debounce()
    },
    handleCurrentChange (currentPage) {
      this.currentPage = currentPage
      this.debounce()
    },
    closeDelChkDialogVisible () {
      this.$refs.relatedDataOptDialog.handleAbsentSkip()
    },
    /**
     * 变更部门后
     * @param title
     * @param optType
     * @param userToDeptOrCompChangedIds 变更的用户关联的部门(此时不包含父级)id集合，包括用户之前的所在部门和变更到的新部门
     */
    changeSuccess (title, optType, userToDeptOrCompChangedIds) {
      if (title.includes(this.$t('导出'))) {
        // 导出完成了，关闭上传页面的弹窗
        this.$refs.uploadDialog.dialogVisible = false
      } else {
        this.$emit('changedUserToTreeData', optType, userToDeptOrCompChangedIds)
        this.getUserList()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.el-empty {
  justify-content: flex-start;
}
::v-deep .el-empty__description{
  margin-top: 0px;
}
.empty_text{
  color: #0854a1;
  &:hover{
    cursor: pointer;
  }
}
.el-table .el-dropdown {
  vertical-align: bottom;
  margin-left: 12px;
  > button {
    padding: 0;
    border: none;
    background-color: transparent !important;
  }
}
.user_table_dropdown {
  border-radius: 2px;
  padding: 0;
  left: unset !important;
  right: 16px;
  margin-top: 0 !important;
  li {
    color: #182A4E;
    height: 44px;
    line-height: 44px;
    padding: 0 16px;
    min-width: 88px;
    box-sizing: border-box;
    &:hover {
      color: #182A4E;
      background-color: #eff2f4;
    }
  }
  > ::v-deep .popper__arrow {
    display: none;
  }
}
</style>
