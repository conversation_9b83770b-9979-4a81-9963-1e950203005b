<template>
  <div>
    <el-input
      style="margin: 0 16px 8px;width: calc(100% - 32px);"
      :placeholder="!navTreeHeight ? $t('按公司名称搜索用户'): $t('搜索部门')"
      clearable
      prefix-icon="el-icon-search"
      v-model.trim="searchText"/>
    <div
      style="padding-left: 16px;height: 24px;color: #5D697A;font-size: 12px;"
      v-show="!infiniteScrollLoading && searchText && !navTreeHeight">{{ $t('筛选结果共{n}条', {count: infiniteScrollTotal}) }}</div>
    <div
      class="user-tree-wrapper"
      :style="{height: treeHeight + 'px',overflowX:useToEditUser?'hidden':'auto'}"
      v-infinite-scroll="infiniteScrollLoad"
      :infinite-scroll-disabled="infiniteScrollLoading"
      infinite-scroll-immediate="false"
      :infinite-scroll-distance="1"
      style="margin-left: 16px;position: relative">
      <el-tree
        class="user-tree"
        :style="{minWidth:useToEditUser?'100%':'fit-content'}"
        v-if="companyData.length"
        highlight-current
        ref="companyTree"
        node-key="id"
        check-strictly
        :check-on-click-node="true"
        :show-checkbox="!!navTreeHeight"
        :expand-on-click-node="false"
        :default-expanded-keys="expandedKeys"
        :default-checked-keys="checkedKeys"
        :data="companyData"
        :props="{label: 'name', children: 'children'}"
        @check="handleCheck"
        @check-change="handleCheckChange"
        @node-click="handleNodeClick">
        <template slot-scope="{ node }">
          <span :class="{textOVHidden:useToEditUser}">
            <i
              v-if="node.data.id && node.data.id === 'allUser'"
              class="edc-icon-yonghu"
              style="padding-right: 6px;color: #0854a1"></i>
            <i v-else-if="node.level === 1" class="edc-icon-gongsi" style="padding-right: 6px;color: #0854a1"></i>
            <i v-else class="edc-icon-zuzhijiagou" style="padding-right: 6px;color: #0854a1"></i>
            <el-tooltip :open-delay="500" :content="node.label" :disabled="node.text">
              <span :id="node.data.id" class="text_box" style="display: inline-block" v-html="companyFilterHighLight(node.label) +
               (navTreeHeight ? '' : ' (' + (node.data.userCount || 0) + '人)')"></span>
            </el-tooltip>
          </span>
        </template>
      </el-tree>
      <table-empty v-show="!infiniteScrollLoading"  v-else :table-height="treeHeight + 40" :description="$t('noData')"/>
      <div
        v-show="infiniteScrollLoading"
        class="infinite-scroll-loading">
        <div class="scenator_loading" style="width: 30px; height: 3px">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span>{{ $t('加载中') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import TableEmpty from '@/components/base/TableEmpty.vue'
import TextTool from '@/utils/TextTool'
import { userManagementApi } from '@/api/userManagement'
import { debounce } from 'lodash'
import ErrorHandler from '@/common/ErrorHandler'

export default {
  name: 'UserNavigation',
  components: { TableEmpty },
  props: {
    navTreeHeight: {
      type: Number,
      default: 0
    },
    useToEditUser: {
      type: Boolean,
      default: false
    },
    defaultCheckedNodes: {
      type: Array,
      default: () => []
    },
    isInternalUser: {
      type: Boolean,
      default: true
    },
    isChangeDepartment: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      searchText: '',
      company: [],
      infiniteScrollList: [],
      infiniteScrollNum: 1,
      infiniteScrollSize: 40,
      infiniteScrollTotal: 0,
      infiniteScrollLoading: true,
      filterList: [],
      expandedKeys: [],
      checkedKeys: [],
      checkedNodes: []
    }
  },
  computed: {
    treeHeight () {
      return this.navTreeHeight ||
        (window.innerHeight - 50 - 32 - 40 - (this.searchText ? 24 : 0))
    },
    isPause () {
      return this.$root.isPause
    },
    companyData () {
      this.$nextTick(() => {
        if (this.$refs.companyTree) {
          this.$refs.companyTree.setCheckedKeys(this.checkedKeys)
          if (!this.navTreeHeight) {
            this.$refs.companyTree.$el.querySelectorAll(
              '.el-tree-node__expand-icon.el-icon-caret-right').forEach(i => {
              i.style.display = 'none'
            })
            this.$refs.companyTree.$el.querySelectorAll('.text_box').forEach(dom => {
              this.$refs.companyTree.getNode(dom.getAttribute('id')).text = dom.scrollWidth <
                document.getElementsByClassName('user-tree-wrapper')[0].clientWidth
            })
          }
        }
      })
      if (this.searchText) {
        return this.filterList
      } else if (!this.navTreeHeight) {
        return this.infiniteScrollList
      } else {
        return this.company
      }
    }
  },
  watch: {
    isPause (val) {
      this.debounce = null
      if (!val) {
        this.infiniteScrollNum = 1
        this.company = []
        this.filterList = []
        this.searchText = ''
        this.infiniteScrollList = []
        this.getTreeOrList()
      }
    },
    async searchText (val) {
      this.infiniteScrollNum = 1
      this.filterList = []
      this.infiniteScrollList = []
      this.infiniteScrollTotal = 0
      this.infiniteScrollLoading = true
      val && this.debounce && this.debounce()
      if (!val) {
        this.infiniteScrollLoading = false
        if (this.navTreeHeight) {
          this.expandedKeys = []
          this.expandedKeys.push(this.company[0]?.id)
        } else {
          await this.getTreeOrList()
        }
      }
    },
    defaultCheckedNodes: {
      handler (val) {
        this.checkedKeys = []
        this.checkedNodes = []
        val.forEach(item => {
          this.checkedKeys.push(item.id)
          // 选中的节点变化时，重新添加到选中节点数组中
          if (this.checkedNodes.findIndex(i => i.id === item.id) === -1) {
            this.checkedNodes.push(item)
          }
        })
        this.$refs.companyTree && this.$refs.companyTree.setCheckedKeys(this.checkedKeys)
      },
      deep: true,
      immediate: true
    }
  },
  async mounted () {
    await this.getTreeOrList()
  },
  methods: {
    async getTreeOrList () {
      let res
      try {
        if (this.navTreeHeight) {
          res = await userManagementApi.getDepartment()
        } else {
          res = await userManagementApi.getCollaborativeCompany({
            keyWord: this.searchText,
            pageNum: this.infiniteScrollNum,
            pageSize: this.infiniteScrollSize
          })
        }
        this.infiniteScrollTotal = res.data.page?.total || 0
        // 非协同公司用户页面的目录树
        if (this.navTreeHeight) {
          this.company = res.data || []
          if (this.company.length) {
            this.expandedKeys.push(this.companyData[0]?.id)
            // 公司内部管理>编辑>所属部门
            if (this.useToEditUser) {
              this.$nextTick(() => {
                this.$refs.companyTree.setCheckedKeys(this.checkedKeys)
              })
            }
          } else {
            this.$emit('nodeClick', { id: null })
          }
        } else {
          // 协同公司用户页面的目录树
          this.infiniteScrollList = [{ id: 'allUser', name: '全部用户', userCount: res.data.userTotal }]
          this.infiniteScrollList.push(...res.data.page.results)
          this.$nextTick(() => {
            if (this.$refs.companyTree) {
              this.$refs.companyTree.setCurrentKey(this.companyData[0]?.id)
              this.handleNodeClick()
            }
          })
        }
        this.debounce = debounce(this.navTreeHeight ? this._filterDepartment : this._filterCompany, 500)
      } catch (err) {
        this.$emit('nodeClick', { id: null })
        ErrorHandler.formatError(err)
      }
      this.infiniteScrollLoading = false
    },
    /**
     * @param {string} optType 操作类型，add新增，edit编辑，delete删除，upload上传
     * @param {Set} userToDeptOrCompChangedIds 用户关联的公司(此时不包含父级)id集合
     */
    async handleChangedNodes (optType, userToDeptOrCompChangedIds) {
      const collaborativeCompanyHash = {}
      let allUserCount = 0
      try {
        const { data } = await userManagementApi.getCollaborativeCompany({
          keyWord: this.searchText,
          pageNum: 1,
          pageSize: this.infiniteScrollNum * this.infiniteScrollSize
        })
        // 公司总数量
        this.infiniteScrollTotal = data.page.total || 0
        allUserCount = data.userTotal
        data.page.results.forEach(item => {
          collaborativeCompanyHash[item.id] = item
        })
      } catch (e) {
        ErrorHandler.formatError(e)
      }
      if (userToDeptOrCompChangedIds?.size) {
        for (const id of userToDeptOrCompChangedIds) {
          if (this.searchText?.trim()) {
            const companyDataItem = this.filterList.find(item => item.id === id)
            if (companyDataItem) {
              companyDataItem.userCount = collaborativeCompanyHash[id]?.userCount || 0
            }
          } else {
            const companyDataItem = this.infiniteScrollList.find(item => item.id === id)
            if (companyDataItem) {
              companyDataItem.userCount = collaborativeCompanyHash[id]?.userCount || 0
            }
            // 总人数（所有公司人数去重后）
            this.infiniteScrollList[0].userCount = allUserCount
          }
        }
      }
      // 上传后树节点没有删除或新增，就只改变人数，此时不知道上传后哪些树节点的人数有变动过，所以整个变
      if (optType === 'upload') {
        if (this.searchText?.trim()) {
          this.filterList.forEach(item => {
            item.userCount = collaborativeCompanyHash[item.id]?.userCount || 0
          })
        } else {
          this.infiniteScrollList.forEach(item => {
            item.userCount = collaborativeCompanyHash[item.id]?.userCount || 0
          })
          // 总人数（所有公司人数去重后）
          this.infiniteScrollList[0].userCount = allUserCount
        }
      }
    },
    // 上传后树节点有新增或删除时的重刷方法
    uploadRefresh () {
      this.infiniteScrollNum = 1
      this.searchText = ''
      this.infiniteScrollList = []
      this.getTreeOrList()
    },
    _filterDepartment () {
      if (this.searchText?.trim()) {
        this.expandedKeys = []
        this.filterList = filterTree(this.company, node => {
          return node.name.includes(this.searchText)
        }, node => {
          this.expandedKeys.push(node?.id)
        })
      }
      this.infiniteScrollLoading = false
    },
    _filterCompany () {
      if (this.searchText?.trim()) {
        this.$emit('nodeClick', { id: null })
        userManagementApi.getCollaborativeCompany({
          keyWord: this.searchText,
          pageNum: this.infiniteScrollNum,
          pageSize: this.infiniteScrollSize
        }).then(({ data }) => {
          this.filterList = []
          this.infiniteScrollTotal = data.page.total || 0
          this.filterList.push(...data.page.results)
          this.infiniteScrollLoading = false
        }).catch(err => {
          ErrorHandler.formatError(err)
          this.infiniteScrollLoading = err.message === 'cancel'
        })
      }
    },
    async infiniteScrollLoad () {
      if (!this.navTreeHeight) {
        if (this.infiniteScrollTotal > (this.searchText ? this.filterList.length : this.infiniteScrollList.length)) {
          this.infiniteScrollNum++
          this.infiniteScrollLoading = true
          const params = {
            keyWord: this.searchText,
            pageNum: this.infiniteScrollNum,
            pageSize: this.infiniteScrollSize
          }
          const { data } = await userManagementApi.getCollaborativeCompany(params)
          this.searchText ? this.filterList.push(...data.page.results) : this.infiniteScrollList.push(...data.page.results)
          this.infiniteScrollLoading = false
        }
      }
    },
    handleCheck (data, checkedData) {
      this.checkedKeys = checkedData.checkedKeys
      const checked = checkedData.checkedKeys.includes(data.id)
      this._calcCheckedNodes(data, checked)
      if (this.navTreeHeight && !this.useToEditUser && !this.isChangeDepartment) {
        // 公司内部部门导出时，选中父节点，子节点也会被选中
        travelTree(data.children || [], node => {
          if (checked) {
            this.checkedKeys = this.checkedKeys.concat([node.id])
          } else {
            this.checkedKeys = this.checkedKeys.filter(item => item !== node.id)
          }
          this.$refs.companyTree.setCheckedKeys(this.checkedKeys)
          this._calcCheckedNodes(node, checked)
        })
        this.$nextTick(() => {
          this.$emit('nodeCheck', this.checkedNodes)
        })
      }
    },
    handleCheckChange (data, isChecked) {
      if (this.navTreeHeight && (this.useToEditUser || this.isChangeDepartment)) {
        this._calcCheckedNodes(data, isChecked)
        this.$emit('nodeCheck', this.checkedNodes)
      }
    },
    _calcCheckedNodes (data, isChecked) {
      if (isChecked) {
        if (!this.checkedNodes.find(item => item.id === data.id)) {
          const nameArr = []
          this.getParentForPath(this.$refs.companyTree.getNode(data.id)?.parent, name => nameArr.unshift(name))
          data.path = nameArr.length ? '/' + nameArr.join('/') : ''
          this.checkedNodes.push(data)
        }
      } else {
        this.checkedNodes = this.checkedNodes.filter(item => item.id !== data.id)
      }
    },
    getParentForPath (node, callback) {
      if (node.level < 1) return
      callback && callback(node.data.name)
      return this.getParentForPath(node.parent, callback)
    },
    handleNodeClick () {
      const { id, name } = this.$refs.companyTree.getCurrentNode()
      this.$emit('nodeClick', { id, name })
    },
    companyFilterHighLight (label) {
      return TextTool.highlightFilters(label, this.searchText)
    }
  }
}

function filterTree (treeArray, condition, callback) {
  const result = []
  for (let i = 0; i < treeArray.length; i++) {
    const node = treeArray[i]
    if (node?.children?.length) {
      const filteredChildren = filterTree(node.children, condition, callback)
      if (filteredChildren?.length) {
        result.push({
          ...node,
          children: filteredChildren
        })
        callback && callback(node)
      } else {
        condition(node) && result.push(node)
      }
    } else {
      condition(node) && result.push(node)
    }
  }
  return result
}
function travelTree (treeArray, callback) {
  for (let i = 0; i < treeArray.length; i++) {
    callback && callback(treeArray[i], i)
    if (treeArray[i]?.children?.length) {
      travelTree(treeArray[i].children, callback)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-tree .el-tree-node__content > .el-tree-node__expand-icon {
  flex-shrink: 0;
  padding: 0;
}

::v-deep .el-tree .el-tree-node__content:hover {
  background-color: #EFF2F4;
}
::v-deep .el-tree .is-focusable.is-checked .el-tree-node__content:hover, ::v-deep .el-tree .is-focusable .el-tree-node__content:hover {
  background-color: #EFF2F4;
}
::v-deep .el-tree .is-current.is-focusable.is-checked .el-tree-node__content:hover{
  background-color: rgba(0, 129, 255, 0.12);
}
::v-deep .user-tree {
  span {
    color: #182A4E;
  }

  .is-current > .el-tree-node__content span {
    color: #0854A1;
  }

  .edc-icon-yonghu::before {
    font-size: 12px;
  }

  .jump-page {
    margin-left: 8px;
    i {
      display: inline-block;
      box-sizing: border-box;
      width: 14px;
      height: 14px;
      line-height: 13px;
      text-align: center;
      font-size: 13px;
      border: 1px solid #224f82;
      border-radius: 50%;
      color: #224f82;
      &:first-child {
        margin-right: 8px;
      }
      &:hover {
        color: #0854a1;
        border-color: #0854a1;
        font-weight: bold;
      }
    }
  }
}

.textOVHidden {
  overflow: hidden;
  display: flex;
  align-items: center;
  .el-tooltip {
    flex-grow: 1;
    width: calc(100% - 66px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .jump-page {
    flex-shrink: 0;
  }
}

.company-select .el-empty {
  padding-top: 18px !important;
  height: 180px !important;
}

.infinite-scroll-loading {
  width: 100%;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  > span {
    color: #182A4E;
    padding: 0 8px;
  }
}
</style>
