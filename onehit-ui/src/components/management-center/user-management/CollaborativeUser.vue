<template>
  <div style="margin: -16px 0 -16px -16px">
    <split-panes class="box default-theme collaborative-user" :dbl-click-splitter="false">
      <pane :size="leftPaneSize" :min-size="leftPaneMinSize" max-size="50">
        <user-nav-list
          ref="userNavList"
          @nodeClick="handleNodeClick"/>
      </pane>
      <pane :size="100 - leftPaneSize" style="border-left: 1px solid #d9d9d9">
        <user-table
          style="padding-left: 16px"
          :current-tree-data="currentTreeData"
          :editDisabled="true"
          @uploadSuccess="handleUploadedSuccess"
          @changedUserToTreeData="handleChangedData"/>
      </pane>
    </split-panes>
  </div>
</template>

<script>
import { Splitpanes as SplitPanes, Pane } from 'splitpanes'
import UserNavList from './UserNavList'
import UserTable from './UserTable'

export default {
  name: 'CollaborativeUser',
  components: {
    UserTable,
    UserNavList,
    SplitPanes,
    Pane
  },
  data () {
    return {
      leftPaneSize: 0,
      leftPaneMinSize: 0,
      currentTreeData: null,
      statisticallyChangedNodes: {}
    }
  },
  mounted () {
    this.calcPanesMinMaxWidth()
  },
  methods: {
    handleNodeClick (data) {
      this.currentTreeData = data
    },
    /**
     * 上传成功后
     * @param noChanged 是否有新增或删除过树节点
     */
    handleUploadedSuccess (noChanged) {
      // 树节点没有删除或新增，就只改变人数，此时不知道上传后哪些树节点的人数有变动过，所以整个变
      if (noChanged) {
        this.handleChangedData('upload', null)
      } else {
        // 树节点变了，就重刷树
        this.$refs.userNavList && this.$refs.userNavList.uploadRefresh()
      }
    },
    /**
     * 事件参数
     * @param {String} optType 操作类型，add新增，edit编辑，delete删除，upload上传
     * @param {Set} userToDeptOrCompChangedIds 用户关联的公司(此时不包含父级)id集合
     */
    handleChangedData (optType, userToDeptOrCompChangedIds) {
      this.$refs.userNavList.handleChangedNodes(optType, userToDeptOrCompChangedIds)
    },
    calcPanesMinMaxWidth () {
      let paneWrapperWidth = document.querySelector('.collaborative-user')?.clientWidth
      if (!paneWrapperWidth) return
      paneWrapperWidth = paneWrapperWidth - 8
      this.leftPaneSize = 312 / paneWrapperWidth * 100
      this.leftPaneMinSize = 240 / paneWrapperWidth * 100
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .splitpanes__pane {
  padding: 16px 0;
}
</style>
