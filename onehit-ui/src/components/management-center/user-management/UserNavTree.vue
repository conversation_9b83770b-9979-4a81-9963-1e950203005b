<template>
  <div style="height: 100%" class="internal-user-nav">
    <div style="padding: 0 16px">
      <el-input
        style="width: 100%;margin-bottom: 8px"
        :placeholder="$t('搜索部门')"
        clearable
        prefix-icon="el-icon-search"
        v-model.trim="searchText"
        @keyup.enter.native="searchInputChange"
        @clear="clear">
      </el-input>
    </div>
    <el-tabs v-if="isSearch" v-model="comp">
      <el-tab-pane :label="$t('目录')" name="tree"></el-tab-pane>
      <el-tab-pane :label="$t('结果')" name="list"></el-tab-pane>
    </el-tabs>
    <div v-show="!isSearch || (isSearch && comp==='tree')" style="padding-left: 16px;box-sizing: border-box">
      <div
        :style="{height: treeHeight + 'px'}"
        v-show="treeData.length"
        class="scenator_ztree scenator-tree-box edc_tree">
        <ul id="userNavTree" class="ztree node-tree"></ul>
      </div>
      <table-empty v-show="!treeData.length && !loadTreeData" :table-height="treeHeight + 40" :description="$t('noData')"/>
    </div>
    <user-nav-tree-search
      ref="projectSearchResult"
      v-show="isSearch && comp==='list'"
      :total="total"
      :loading="loading"
      :searchText="searchText"
      :list="searchList"
      @locationNode="locationNode"
      @loadmore="loadmore"></user-nav-tree-search>
</div>
</template>
<script>
import $ from 'jquery'
import '@ztree/ztree_v3/js/jquery.ztree.all.js'
import TableEmpty from '@/components/base/TableEmpty.vue'
import tool from '@/utils/tool'
import UserNavTreeSearch from './UserNavTreeSearch.vue'
import ArrayUtil from '@/utils/ArrayUtil'
import { userManagementApi } from '@/api/userManagement'
import ErrorHandler from '@/common/ErrorHandler'
import { debounce } from 'lodash'

export default {
  components: { TableEmpty, UserNavTreeSearch },
  data () {
    return {
      nodeId: '',
      currentNode: null,
      waitLocationNodeIds: [],
      searchPageSize: 40,
      searchPageNum: 1,
      loadTreeData: true,
      isSearch: false,
      loading: false,
      searchList: [],
      comp: 'list',
      total: 0,
      zTreeObj: null,
      pageSize: 99999,
      searchText: '',
      treeData: [],
      treeDataClone: {},
      zTreeSetting: {
        view: {
          showIcon: true,
          showLine: false,
          showTitle: false,
          nameIsHTML: true
        },
        data: {
          render: {
            name: (name, node) => {
              return `${this.$escape(name)} (${node.userCount || 0}人)`
            }
          }
        },
        callback: {
          onClick: (event, treeId, treeNode) => {
            if (treeNode.id) {
              this.currentNode = treeNode
              this.$emit('handleNodeClick', treeNode)
            }
          },
          beforeCollapse: this.beforeCollapse,
          beforeExpand: this.beforeExpand
        }
      },
      currentPage: 1
    }
  },
  computed: {
    treeHeight () {
      return this.isSearch ? window.innerHeight - 50 - 32 - 40 - 54 : window.innerHeight - 50 - 32 - 40
    }
  },
  watch: {
    nodeId (val) {
      if (val && this.comp === 'tree') {
        this._rollingPosition()
      }
    },
    searchText (value, old) {
      if (value !== old) {
        this.changeHighLight()
      }
      if (!value) {
        this.isSearch = false
        this.comp = 'list'
        this.searchPageNum = 1
      }
    },
    waitLocationNodeIds (newVal) {
      const length = newVal.length
      if (length === 0) {
        return
      }
      const nodeId = newVal[length - 1]
      const node = this.zTreeObj.getNodeByParam('id', nodeId)
      if (!node) {
        return
      }

      if (length === 1) {
        this.zTreeObj.selectNode(node, false, true)
        this.currentNode = node
        this.$emit('handleNodeClick', node)
        this.nodeId = node.tId
        this.waitLocationNodeIds = []
        return
      }

      if (node.open) {
        const child = ArrayUtil.getSecondLastValue(this.waitLocationNodeIds)
        const find = node.children.find(node => node.id === child)
        if (find) {
          this.waitLocationNodeIds.pop()
          return
        }
      }
      node.isParent = true
      if (node?.isParent && node?.children?.length) {
        // 针对搜索的情况，下个节点可能不在本节点的当前页中，需要翻页
        this.beforeExpand(node.tId, node)
        // 找的到,则说明分页了
        if (this.treeDataClone[nodeId]) {
          // 下个节点在目前节点中所处的页数
          const child = this.treeDataClone[nodeId].find(e => e.id === newVal[length - 2])
          this.currentPage = child ? child.locatePageNum : 1
          // 把下个目标节点替换进来
          this.zTreeObj.removeChildNodes(node)
          this.zTreeObj.addNodes(node,
            this.treeDataClone[nodeId].filter(e => e.locatePageNum === this.currentPage), true)
        }
        this.zTreeObj.expandNode(node, true, false, false, true)
        this.waitLocationNodeIds.pop()
      }
    }
  },
  mounted () {
    this.init()
    !this.debounce && (this.debounce = debounce(this.searchResult, 500))
  },
  methods: {
    init () {
      this.searchText = ''
      this.isSearch = false
      this.comp = 'list'
      this.searchPageNum = 1
      this.loadTreeData = true
      this.initTree()
    },
    async initTree () {
      try {
        const { data } = await userManagementApi.getDepartment()
        this.treeData = data.length ? data : [{ id: 'root', name: this.$t('公司名称'), parentId: null }]
        this.zTreeObj = $.fn.zTree.init($('#userNavTree'), this.zTreeSetting, this.treeData)
        const node = this.zTreeObj.getNodeByParam('parentId', null)
        this.zTreeObj.expandNode(node, true, false, false, true)
        this.zTreeObj.selectNode(node)
        this.currentNode = node
        this.$emit('handleNodeClick', node)
      } catch (e) {
          console.log(e)
        this.$emit('handleNodeClick', { id: null })
      }
      this.loadTreeData = false
    },
    /**
     * @param {string} optType 操作类型，add新增，edit编辑，delete删除，upload上传
     * @param {Set} userToDeptOrCompChangedIds 用户关联的部门或公司(此时不包含父级)id集合
     */
    async handleChangedNodes (optType, userToDeptOrCompChangedIds) {
      let departmentListHash = {}
      if (this.zTreeObj) {
        try {
          const { data } = await userManagementApi.getDepartmentList()
          departmentListHash = data.reduce((acc, cur) => {
            acc[cur.id] = cur
            return acc
          }, {})
        } catch (e) {
          ErrorHandler.formatError(e)
        }
        if (userToDeptOrCompChangedIds?.size) {
          for (const id of userToDeptOrCompChangedIds) {
            this.recursiveParent(id, node => {
              if (node) {
                node.userCount = departmentListHash[node.id]?.userCount || 0
                this.zTreeObj.updateNode(node)
              }
            })
          }
        }
        // 上传后树节点没有删除或新增，就只改变人数，此时不知道上传后哪些树节点的人数有变动过，所以整个变
        if (optType === 'upload') {
          for (const key in departmentListHash) {
            const node = this.zTreeObj.getNodeByParam('id', departmentListHash[key].id)
            if (node) {
              node.userCount = departmentListHash[key].userCount || 0
              this.zTreeObj.updateNode(node)
            }
          }
        }
      }
    },
    recursiveParent (id, cb) {
      const treeNode = this.zTreeObj.getNodeByParam('id', id)
      cb && cb(treeNode)
      treeNode?.parentId && this.recursiveParent(treeNode.parentId, cb)
    },
    locationNode (node) {
      if (node) {
        this.comp = 'tree'
        const ancestors = node.pathId.split('/').filter(item => item)
        ancestors.reverse()
        this.waitLocationNodeIds = ancestors
      }
    },
    _rollingPosition () {
      this.$nextTick(() => {
        $('.internal-user-nav .edc_tree').animate({
          scrollTop: $(`.internal-user-nav .edc_tree #${this.nodeId}`)[0].offsetTop -
            $('.internal-user-nav .edc_tree').height()
        }, 500)
      })
    },
    searchInputChange () {
      if (!this.searchText?.length) return
      this.comp = 'list'
      this.searchPageNum = 1
      this.isSearch = true
      this.searchList = []
      this.$refs.projectSearchResult.curentClickItem = {}
      this.loading = true
      this.debounce()
    },
    async searchResult (clearSearchList) {
      if (!clearSearchList) {
        this.searchList.splice(0)
      }
      this.total = 0
      if (this.searchText) {
        const res = await userManagementApi.getDepartmentByFilterList({
          keyWord: this.searchText,
          pageNum: this.searchPageNum,
          pageSize: this.searchPageSize
        })
        this.total = res.data.total
        res.data.results.map(v => {
          v.highlightName = tool.getHighlightStrArray(v.name, this.searchText)
          return v
        })
        this.loading = false
        this.searchList = [...this.searchList, ...res.data.results]
        this.comp = 'list'
      } else {
        this.clear()
      }
    },
    loadmore () {
      this.loading = true
      if (this.total > (this.searchPageNum) * this.searchPageSize) {
        this.searchPageNum = this.searchPageNum + 1
        this.searchResult(true)
      } else {
        this.loading = false
      }
    },
    clear () {
      this.searchText = ''
      this.isSearch = false
      this.comp = 'list'
      this.searchPageNum = 1
    },
    beforeCollapse (treeId, treeNode) {
      $(`#${treeNode.tId}_a > span[name='page']`).remove()
    },
    beforeExpand (treeId, treeNode) {
      treeNode.children.forEach((e, i) => {
        if (!e.locatePageNum) {
          e.locatePageNum = Math.floor(i / this.pageSize) + 1
        }
      })
      this.currentPage = treeNode.children[0].locatePageNum || 1
      // 子节点超过pageSize变成分页
      if (treeNode.children?.length > this.pageSize || this.treeDataClone[treeNode.id]?.length > this.pageSize) {
        treeNode.maxPage =
          Number.parseInt((this.treeDataClone[treeNode.id]?.length + this.pageSize - 1) / this.pageSize) ||
          Number.parseInt((treeNode.children.length + this.pageSize - 1) / this.pageSize)
        if (!$(`#${treeNode.tId}_a > span[name='page']`)[0]) {
          const addStr =
            `<span name='page'>
            <i class='edc-tree-page-btn edc-icon-left'/>
            <i class='edc-tree-page-btn edc-icon-right'/>
          </span>`
          $(`#${treeNode.tId}_a`).append(addStr)
          $(`#${treeNode.tId}_a > span[name='page']`).bind('dblclick', (event) => {
            // 解决有分页按钮的时候双击当前span会触发ztree默认双击折叠的问题
            event.stopPropagation()
          })
          $(`#${treeNode.tId}_a .edc-icon-left`).bind('click', (event) => {
            event.stopPropagation()
            this.currentPage = treeNode.children[0].locatePageNum - 1 < 1
              ? 1 : treeNode.children[0].locatePageNum - 1
            this.goPage(treeNode, this.currentPage)
          })
          $(`#${treeNode.tId}_a .edc-icon-right`).bind('click', (event) => {
            event.stopPropagation()
            this.currentPage = treeNode.children[0].locatePageNum + 1 > treeNode.maxPage
              ? treeNode.maxPage : treeNode.children[0].locatePageNum + 1
            this.goPage(treeNode, this.currentPage)
          })
        }
        if (!this.treeDataClone[treeNode.id]) {
          this.treeDataClone[treeNode.id] = JSON.parse(JSON.stringify(treeNode.children))
        }
        this.zTreeObj.removeChildNodes(treeNode)
        this.zTreeObj.addNodes(treeNode,
          this.treeDataClone[treeNode.id].filter(e => e.locatePageNum === this.currentPage), true)
      }
    },
    goPage (treeNode, pageNum) {
      if (treeNode.children[0]?.locatePageNum !== pageNum) {
        this.zTreeObj.removeChildNodes(treeNode)
        this.zTreeObj.addNodes(treeNode, this.treeDataClone[treeNode.id].filter(e => e.locatePageNum === pageNum))
      }
    },
    changeHighLight () {
      if (this.searchList.length) {
        this.searchList = this.searchList.map(v => {
          v.highlightName = tool.getHighlightStrArray(v.name, this.searchText)
          return v
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep.scenator-tree-box {
  width: 100%;
  padding: 0 !important;
  overflow: auto !important;
  ul {
    &.node-tree {
      //height: 100%;
      width: fit-content;
      min-width: calc(100% - 22px);
    }
    li {
      a {
        &:hover {
          background-color: #EFF2F4 !important;
          &:before {
            background-color: #EFF2F4 !important;
          }
        }
        &.curSelectedNode{
          background-color: rgba(0, 129, 255, 0.12)!important;
          &:before {
            background-color: rgba(0, 129, 255, 0.12)!important;
          }
        }
        span[id^="userNavTree"][id$="ico"] {
          display: inline-block !important;
          &::after {
            font-family: 'edc-icon';
            content: '\e603' !important;
            background: none !important;
            position: static !important;
            line-height: 28px !important;
            font-size: 16px;
            color: #0854a1;
          }
        }
        span#userNavTree_1_ico::after {
          content: '\e604' !important;
        }
        span.node_name {
          font-family: 'PingFangSC-Regular', 'PingFang SC';
          width: auto !important;
          margin: 0 !important;
          text-overflow: unset !important;
        }
        span[name='page'] {
          width: 40px;
          height: 28px;
          line-height: 28px;
          padding: 0 !important;
          margin: 0 0 0 8px !important;
          i.edc-tree-page-btn {
            width: 14px;
            height: 14px;
            border: 1px solid #224f82;
            box-sizing: border-box;
            border-radius: 50%;
            color: #224f82;
            &:hover {
              color: #0854a1;
              border-color: #0854a1;
            }
            &.edc-icon-right {
              margin-left: 4px;
            }
          }
        }
      }
    }
  }
}
.tab_height{
  height: calc(100% - 60px);
}
::v-deep .el-tabs__nav-scroll{
  display: flex;
  justify-content: center;
}
</style>
