<template>
  <div>
    <import-and-export ref="importAndExport" @on-success="onSuccess">
      <template v-slot:download>
        <li>
          <span>·{{ $t('如需批量新增用户，你可以') }} </span>
          <span style="color: #0854A1;cursor: pointer"  @click="downloadEmptyTemplate">{{ $t('下载空模板') }}</span>
          <span>{{ $t('完善后上传') }}</span>
        </li>
        <li>
          <span>· {{ $t('如需批量修改用户信息，你可以') }}</span>
          <span style="color: #0854A1;cursor: pointer" @click="exportUser">
            {{isInternalUser? $t('按部门导出用户信息'):$t('按协同公司导出用户信息')}}
          </span>
          <span>{{ $t('完善后上传') }}</span>
        </li>
        <li>
          <span>· {{ $t('如需保存到本地，你可以') }}</span>
          <span style="color: #0854A1;cursor: pointer" @click="exportUser">
            {{isInternalUser? $t('按部门导出用户信息'):$t('按协同公司导出用户信息')}}
          </span>
          <span>。</span>
        </li>
      </template>
      <template v-slot:textType>{{ $t('仅支持xls和xlsx格式文件') }}</template>
    </import-and-export>
    <related-data-opt-dialog
      ref="relatedDataOptDialog"
      @dialog-close="handleAbsentSkip">
      <template #title>
        {{ $t('系统中不存在以下') }} <span style="color: #0854a1">{{absentList.length}}</span> {{ $t('个') }}{{isInternalUser? $t('部门'):$t('公司')}}，{{ $t('需要自动添加至') }}{{isInternalUser?$t('公司组织'):$t('协同公司')}}吗？
      </template>
      <template #footerButton>
        <el-button @click="handleAbsentSkip">{{ $t('GlobalUploader.跳过') }}</el-button>
        <el-button type="primary" @click="handleAutoAdd">{{ $t('自动新增') }}</el-button>
      </template>
    </related-data-opt-dialog>
  </div>
</template>

<script>
import FileUtil from '@/utils/FileUtil'
import TextTool from '@/utils/TextTool'
import RelatedDataOptDialog from '@/components/base/RelatedDataOptDialog'
import { userManagementApi } from '@/api/userManagement'
import ErrorHandler from '@/common/ErrorHandler'
import ImportAndExport from '@/components/base/file-upload/importAndExport'
export default {
  name: 'UploadDialog',
  components: { ImportAndExport, RelatedDataOptDialog },
  data () {
    return {
      isInternalUser: true,
      dialogTitle: '',
      dialogVisible: false,
      isUploadSuccess: false,
      isUploading: false,
      absentDialogTitle: '',
      absentList: [],
      failedReportPathId: '',
      actionCount: {
        failedCount: 0,
        successCount: 0
      }
    }
  },
  computed: {
    dialogBodyMaxHeight () {
      return 'calc(100vh - 96px - 92px - 32px)'
    }
  },
  methods: {
    measureText (pText, pFontSize, pStyle) {
      return TextTool.measureText(pText, pFontSize, pStyle)
    },
    init () {
      this.isUploadSuccess = false
      this.isUploading = false
      this.absentList = []
      this.failedReportPathId = ''
      this.actionCount = {
        failedCount: 0,
        successCount: 0
      }
    },
    openDialog (isInternalUser) {
      this.init()
      this.isInternalUser = isInternalUser
      if (isInternalUser) {
        this.dialogTitle = this.$t('批量导入或导出用户')
        this.absentDialogTitle = this.$t('发现新部门')
      } else {
        this.dialogTitle = this.$t('批量导入或导出用户')
        this.absentDialogTitle = this.$t('发现新协同公司')
      }
      this.$refs.importAndExport.open(this.isInternalUser ? '/UAMS/user/manager/user/internal/import' : '/UAMS/user/manager/user/cooperate/import', this.dialogTitle)
    },
    downloadEmptyTemplate () {
      if (this.isInternalUser) {
        FileUtil.downloadByPostUrl('/UAMS/user/manager/user/internal/export', [])
      } else {
        FileUtil.downloadByPostUrl('/UAMS/user/manager/user/cooperate/export', [])
      }
    },
    exportUser () {
      if (this.isInternalUser) {
        this.$emit('exportUser')
        return
      }
      // 协同公司导出用户信息时，需判断当前是否有协同公司
      userManagementApi.getCollaborativeCompany({ pageNum: 1, pageSize: 1 }).then(({ data }) => {
        if (data.page.total) {
          this.$emit('exportUser')
        } else {
          this.$message.warning('请先新增协同公司')
        }
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
    },
    handleUpload () {
      this.isUploading = true
      this.$refs.singleFileUpload.submitUpload()
    },
    handleClose () {
     
      this.dialogVisible = false
    },
    onChange (val) {
      this.isUploadSuccess = val
    },
    onSuccess (res, file, fileList) {
      this.isUploading = false
      this.dialogVisible = false
      if (res.successCount !== res.totalCount) {
        this.failedReportPathId = res.failedReportPathId || ''
        this.actionCount.failedCount = res.failedCount || 0
        this.actionCount.successCount = res.successCount || 0
        if (res.deptName?.length) {
          this.absentList = res.deptName
          this.$refs.relatedDataOptDialog.open(this.absentDialogTitle, this.absentList)
        } else if (res.failedCount) {
          this.$confirm(
            res.successCount + this.$t('人信息导入') + '<span style="color: rgb(64,143,101)">' + this.$t('成功') + '</span>' + '，' +
            res.failedCount + this.$t('人信息导入') + '<span style="color: #e44b47">' + this.$t('失败') + '</span>' + this.$t('请下载失败报告，修改后重新导入'),
            this.$t('提示'), {
              dangerouslyUseHTMLString: true,
              confirmButtonText: this.$t('下载报告'),
              cancelButtonText: this.$t('取消'),
              type: 'warning'
            }).then(() => {
            const val = this.isInternalUser ? 'internal' : 'cooperate'
            FileUtil.downloadByUrl(`/user/${val}/import/download/error-report/${this.failedReportPathId}`)
            this.$emit('uploaded', res.successCount < 1)
          }).catch((err) => {
            err.message === 'cancel' || this.$emit('uploaded', res.successCount < 1)
          })
        }
      } else {
        this.$emit('uploaded', true)
        this.$message.success(this.$t('importSuccess'))
      }
    },
    onError (err) {
      this.isUploading = false
      this.$message.error(JSON.parse(err.message).message)
    },
    handleAbsentSkip () {
      if (this.actionCount.failedCount > 0) {
        this._handleShowReport()
      } else {
        this.$emit('uploaded', true)
      }
      this.$refs.relatedDataOptDialog.handleAbsentSkip()
    },
    handleAutoAdd () {
      let action
      if (this.isInternalUser) {
        action = userManagementApi.autoAddNewDept(
          { errorReportId: this.failedReportPathId },
          this.absentList)
      } else {
        action = userManagementApi.autoAddNewComp(
          { errorReportId: this.failedReportPathId },
          this.absentList)
      }
      action.then(({ data }) => {
        this.actionCount.failedCount -= data
        this.actionCount.successCount += data
        if (this.actionCount.failedCount > 0) {
          this._handleShowReport()
        } else {
          this.$emit('uploaded')
          this.$message.success(this.$t('importSuccess'))
        }
        this.$refs.relatedDataOptDialog.handleAbsentSkip()
      }).catch(err => {
        err.message === 'cancel' || this.$refs.relatedDataOptDialog.handleAbsentSkip()
        ErrorHandler.formatError(err)
      })
    },
    _handleShowReport () {
      this.$confirm(
        this.actionCount.successCount + this.$t('人信息导入') +
        '<span style="color: rgb(64,143,101)">' + this.$t('成功') + '</span>' + '，' +
        this.actionCount.failedCount + this.$t('人信息导入') + '<span style="color: #e44b47">' + this.$t('失败') + '</span>' +
        this.$t('请下载失败报告，修改后重新导入'),
        this.$t('提示'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('下载报告'),
          cancelButtonText: this.$t('取消'),
          type: 'warning'
        }).then(() => {
        const val = this.isInternalUser ? 'internal' : 'cooperate'
        FileUtil.downloadByUrl(`/user/${val}/import/download/error-report/${this.failedReportPathId}`)
        this.$emit('uploaded', this.actionCount.successCount < 1)
      }).catch((err) => {
        err.message === 'cancel' || this.$emit('uploaded', this.actionCount.successCount < 1)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.action-entry {
  margin-bottom: 24px;
  > div {
    margin-top: 8px;
    height: 22px;
    color: #182A4E;
    line-height: 22px;
    > span {
      line-height: inherit;
      display: inline-block;
      vertical-align: bottom;
    }
  }
}
.single-file-upload,
::v-deep .single-file-upload .file-uploader,
::v-deep .single-file-upload .el-upload,
::v-deep .single-file-upload .el-upload-dragger {
  width: 100%;
}
</style>
