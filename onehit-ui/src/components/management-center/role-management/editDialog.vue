<template>
  <el-dialog
    width="680px"
    append-to-body
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :before-close="handleClose">
    <el-form ref="form" :model="form" size="small" :rules="rules" label-width="98px">
      <el-form-item prop="name" :label="$t('角色名称') + '：'">
        <el-input style="width: 500px" v-model="form.name" maxlength="50" :placeholder="$t('pleaseEnter')"></el-input>
      </el-form-item>
      <el-form-item prop="remark" :label="$t('描述') + '：'">
        <el-input rows="3"  style="width: 500px;font-size: 14px" type="textarea" v-model="form.remark" maxlength="200" :placeholder="$t('pleaseEnter')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('权限') + '：'" prop="featureIds">
        <el-checkbox-group style="display: none" v-model="form.featureIds"></el-checkbox-group>
       <div style="max-height: calc((70vh - 96px) - 173px); overflow: auto; width: 500px; border: 1px solid #89919A; borderRadius: 2px">
         <el-tree
           highlight-current
           show-checkbox
           ref="permissionTree"
           node-key="id"
           :check-strictly="true"
           :default-checked-keys = "this.form.featureIds || []"
           :expand-on-click-node="false"
           :data="menuTree"
           :props="{label: 'name', children: 'children'}"
           @node-click="handleNodeClick"
           @check="checkClick"
           @check-change="checkChange"
           @current-change="currentChange('permissionTree')">
           <template slot-scope="{ node, data }">
              <span>
                {{data.name}}
                  <el-popover
                    :open-delay="500"
                    popper-class="text_tooltip"
                    trigger="hover"
                    v-if="data.name === '目录导入/导出/新增/编辑/移动/删除'"
                    placement="top"
                    :content="$t('选中后该角色默认有所有目录及文档的管理权限')">
                    <i slot="reference" class="edc-icon-a-tishi" style="color: rgba(8, 84, 161, 1); font-size: 14px"></i>
                  </el-popover>
              </span>
           </template>
         </el-tree>
       </div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
          <el-button size="small"  style="color: rgba(8, 84, 161, 1)!important;" @click="handleClose">{{ $t('取消') }}</el-button>
          <el-button size="small" type="primary" @click="save" :disabled="saveIng">{{ $t('确定') }}</el-button>
      </span>
  </el-dialog>
</template>
<script>
import ErrorHandler from '@/common/ErrorHandler'
import { roleManagementApi } from '@/api/role'
import { configApi } from '@/api/onehit'

export default {
  data () {
    return {
      form: {
        name: null,
        remark: null,
        featureIds: []
      },
      saveIng: false,
      menuTree: null,
      dialogTitle: this.$t('add'),
      dialogVisible: false,
      rules: {
        name: [
          { required: true, message: this.$t('角色名称不能为空'), trigger: 'blur' }
        ],
        featureIds: [
          { required: true, validator: this.validatePermission, trigger: 'blur' }
        ]
      },
      onehitPermissionTree: [],
      onehitScenes: [],
      specialOptions: ['目录导入/导出/新增/编辑/移动/删除', '文档下载及打印', '相关文档下载及打印', '设计模型下载', '供应商模型下载', '数字图纸下载']
    }
  },
  computed: {
    treeMaxHeight () {
      return window.innerHeight - 50 - 44 - 32 - 44
    }
  },
  watch: {
    'form.featureIds' (val) {
      if (val?.length && this.$refs.form) {
        this.$refs.form.clearValidate('featureIds')
      }
    }
  },
  methods: {
    async getScenes (roleId) {
      const { data: scenes } = await configApi.getAllScene()
      this.onehitScenes = scenes
      let onehitPermission = []
      if (roleId) {
        const { data: permissions } = await configApi.getPermissions(roleId)
        if (permissions && permissions.sceneNameList) {
          onehitPermission = this.onehitScenes.filter(item => permissions.sceneNameList.includes(item))
        }
      }
      return onehitPermission
    },
    validatePermission (rule, value, callback) {
      if (this.form.featureIds.length === 0) {
        callback(new Error(this.$t('权限不能为空')))
      } else {
        callback()
      }
    },
    currentChange (tree) {
      this.$refs.permissionTree.setCurrentKey(null)
    },
    checkClick (data, checked) {
      const check = checked.checkedKeys.findIndex(item => item === data.id) > -1
      data.children && this.setChildrenChecked(data.children, check)
      if (data.parentId) {
        if (check) {
          this.setParentChecked(this.$refs.permissionTree.getNode(data.id), check)
        } else if (this.$refs.permissionTree.getNode(data.parentId)?.data.children?.findIndex(item => checked.checkedKeys.includes(item.id)) < 0 && !this.specialOptions.includes(data.name)) {
          this.setParentChecked(this.$refs.permissionTree.getNode(data.id), check)
        }
      }
    },
    checkChange () {
      // 节点未展开 直接获取只能获取到当前选中的父节点
      this.$nextTick(() => {
        this.form.featureIds = this.$refs.permissionTree.getCheckedKeys()
      })
    },
    setParentChecked (node, check) {
      this.$refs.permissionTree.setChecked(node.data.id, check)
      if (node.data.parentId) {
        this.setParentChecked(node.parent, check)
      }
    },
    setChildrenChecked (data, check) {
      data.forEach(item => {
        this.$refs.permissionTree.setChecked(item.id, check)
        if (item.children) {
          this.setChildrenChecked(item.children, check)
        }
      })
    },
    handleClose () {
      this.dialogVisible = false
      this.$refs.form.clearValidate()
      this.form = {
        name: null,
        remark: null,
        featureIds: []
      }
      this.$nextTick(() => {
        for (let i = 0; i < this.$refs.permissionTree.store._getAllNodes().length; i++) {
          this.$refs.permissionTree.store._getAllNodes()[i].expanded = false
        }
      })
    },
    save () {
      this.$refs.form.validate(async (valid) => {
        if (!valid) {
          return
        }
        this.saveIng = true
        console.log(this.dialogTitle)
        console.log(this.$t('add'))
        if (this.dialogTitle.includes(this.$t('add'))) {
          roleManagementApi.addRole({ name: this.form.name.trim(), remark: this.form.remark?.trim(), featureIds: this.form.featureIds.filter(item => item !== 2 && !this.onehitScenes.includes(item)) }).then((response) => {
            // 存储在菜单权限中没有定义的场景
            const params = { roleId: response.data.id, sceneNameList: [] }
            this.onehitPermissionTree.children.forEach(item => {
              if (this.form.featureIds.includes(item.id)) {
                params.sceneNameList.push(item.name)
              }
            })
            if (params.sceneNameList.length > 0) {
              configApi.saveSceneAuth(params)
            }

            this.$emit('loadRoleList')
            this.handleClose()
            this.$message.success(this.$t('新增成功'))
            this.saveIng = false
          }).catch(err => {
            ErrorHandler.formatError(err)
            this.saveIng = err.message === 'cancel'
          })
        } else {
          const params = { roleId: this.form.id, sceneNameList: [] }
          this.onehitPermissionTree.children.forEach(item => {
            if (this.form.featureIds.includes(item.id)) {
              params.sceneNameList.push(item.name)
            }
          })
          try {
            configApi.saveSceneAuth(params)

            await roleManagementApi.updateRole(this.form.id, { name: this.form.name.trim(), remark: this.form.remark?.trim(), featureIds: this.form.featureIds.filter(item => item !== 2 && !this.onehitScenes.includes(item)) })
            this.$emit('loadRoleList')
            this.handleClose()
            this.$message.success(this.$t('修改成功'))
            this.saveIng = false
          } catch (e) {
            ErrorHandler.formatError(e)
            this.saveIng = e.message === 'cancel'
          }
        }
      })
    },
    async open (title, tree, row) {
      if (row) {
        const { name, id, featureIds, remark } = row
        this.form = { name, id, featureIds, remark }
        this.onehitPermissionTree = []
      }
      const onehitPermission = await this.getScenes(row && row.id)
      this.onehitPermissionTree = tree.find(node => node.name === this.$t('业务工作台'))
      this.onehitScenes.forEach(item => {
        const treeNode = this.onehitPermissionTree.children.find(node => node.name === item)
        if (!treeNode) {
          this.onehitPermissionTree.children.push({ id: item, name: item, path: '/#业务工作台/#' + item, type: 'sceneAuth' })
          if (onehitPermission.includes(item)) {
            this.form.featureIds.push(item)
          }
        } else if (onehitPermission.includes(item)) {
          this.form.featureIds.push(treeNode.id)
        }
      })

      this.$refs.permissionTree && this.$refs.permissionTree.setCheckedKeys([])
      this.form.featureIds?.length && this.$refs.permissionTree &&
      this.$refs.permissionTree.setCheckedKeys(this.form.featureIds)
      if (this.form.featureIds.length > 0) {
        this.form.featureIds.push(2)
        this.$refs.permissionTree && this.$refs.permissionTree.setCheckedKeys(this.form.featureIds)
      }
      tree.forEach(item => {
        item.parentId = 2
      })
      this.menuTree = tree
      this.dialogTitle = title
      this.dialogVisible = true
    },
    handleNodeClick (treeNode) {
      if (treeNode.id === this.form.id || treeNode.parentId === this.form.id) {
        return
      }
      this.form.parentId = treeNode.id
      this.form.parentName = treeNode.name
    }
  }
}
</script>
<style scoped lang="scss">
* {
  color:rgba(24, 42, 78, 1) !important;
}
.tree{
  max-height: 296px;
  //padding-left: 16px;
  overflow-y: auto;
 ::v-deep .el-tree-node__expand-icon{
    padding: 0;
   margin-left: 16px;
  }
}
.disbaled_select{
  background-color: rgba(24, 42, 78, 0.06);
}
::v-deep .el-form-item--small.el-form-item{
  margin-bottom: 16px!important;
}
::v-deep .el-dialog__body{
  max-height: 690px;
  overflow-y: auto;
  padding: 24px 57px 0px 45px!important;
}
::v-deep .el-form-item__label{
  padding: 0 16px 0 0!important;
  color:rgba(24, 42, 78, 1) !important;
}
::v-deep .el-tree-node__content > * {
  line-height: 28px;
}
::v-deep .el-tree-node__content:hover{
  background-color: rgba(239, 242, 244, 1) !important;
}
::v-deep .is-current > .el-tree-node__content:hover{
    background-color: rgba(229, 240, 250, 1)!important;
  }
::v-deep .el-tree-node:focus > .el-tree-node__content{
    background-color: transparent;
}
::v-deep .el-input.is-disabled {
  .el-input__inner{
    background-color: rgba(24, 42, 78, 0.06);
    color: #182A4E!important;
  }
  .el-icon-arrow-down{
    color: #182A4E!important;
  }
}
.check_disabled{
  opacity: 0.4;
  pointer-events: none;
}
</style>
