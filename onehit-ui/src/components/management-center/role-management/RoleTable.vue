<template>
  <div>
    <div class="add-button">
      <el-button
        type="primary"
        size="small"
        @click="addRole">
        {{ $t('add') }}
      </el-button>
      <el-input
        v-model.trim="searchText"
        style="width: 240px;float: right"
        :placeholder="$t('搜索')"
        prefix-icon="el-icon-search"
        size="small"
        clearable>
      </el-input>
    </div>
    <div
      :style="{height: tableIsLoading ? parseInt(tableMaxHeight) + 'px' : 'fit-content'}"
      style="position: relative; border-bottom: 1px solid #d9d9d9;">
      <table-loading :table-height="tableMaxHeight" v-show="tableIsLoading"/>
      <el-table
        stripe
        border
        ref="table"
        highlight-current-row
        row-key="id"
        :header-cell-style="{ background: '#EFEFEF' }"
        :data="roleList"
        :row-class-name="rowClassName"
        :max-height="tableMaxHeight">
        <el-table-column :label="$t('角色名称')" show-overflow-tooltip min-width="25%">
          <template slot-scope="scope">
            <div v-if="measureText(scope.row.name.substr(0, scope.row.name.indexOf(searchText) > -1 ? scope.row.name.indexOf(searchText) - 1 : 0 ), 14) > scope.column.realWidth">
              <span :style="{ width: scope.column.realWidth - measureText(searchText, 14) - 104  + 'px'}" style="display:inline-block;font-size:14px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">{{scope.row.name.substr(0, scope.row.name.indexOf(searchText) - 3)}}</span>
              <span :style="{width: measureText(searchText, 14) + 70  + 'px'}" style="display: inline-block;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;" v-html="filterHighLight(scope.row.name.substr(scope.row.name.indexOf(searchText)-3, scope.row.name.length - 1))"></span>
              <el-tag type="info" v-if="scope.row.default || (defaultRole && defaultRole.id === scope.row.id)" style="margin-left: 4px; padding: 0 5px">默认</el-tag>
            </div>
            <div v-else style="width: 100%;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;" >
              <span  v-html="filterHighLight(scope.row.name)" @click="click(scope)"></span>
              <el-tag type="info" v-if="scope.row.default || (defaultRole && defaultRole.id === scope.row.id)" style="margin-left: 4px; padding: 0 5px">默认</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('描述')" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="scope.row.remark" style="width: 100%;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">
                <div v-if="measureText(scope.row.remark.substr(0, scope.row.remark.indexOf(searchText) > -1 ? scope.row.remark.indexOf(searchText) - 1 : 0 ), 14) > scope.column.realWidth">
                  <span :style="{ width: scope.column.realWidth - measureText(searchText, 14) - 104  + 'px'}" style="display:inline-block;font-size:14px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">{{scope.row.remark.substr(0, scope.row.remark.indexOf(searchText) - 3)}}</span>
                  <span :style="{width: measureText(searchText, 14) + 70  + 'px'}" style="display: inline-block;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;" v-html="filterHighLight(scope.row.remark.substr(scope.row.remark.indexOf(searchText)-3, scope.row.remark.length - 1))"></span>
                </div>
              <span v-else v-html="filterHighLight(scope.row.remark)" @click="click(scope)"></span>
            </div>
               <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          :label="$t('')"
          width="106">
          <template slot-scope="{ row }">
      <span>
        <el-link
          v-if="row.default"
          type="primary"
          :underline="false"
          @click="viewRole(row)">
          {{ $t('查看') }}
        </el-link>
        <el-link
          v-if="!row.default"
          type="primary"
          :underline="false"
          @click="updateRole(row)">
          {{ $t('edit') }}
        </el-link>
        <el-link
          v-if="!row.default && (defaultRole && defaultRole.id !== row.id)"
          type="primary"
          :underline="false"
          style="margin-left: 24px"
          @click="deleteRole(row.id)">
          {{ $t('delete') }}
        </el-link>
      </span>
          </template>
        </el-table-column>
        <template slot="empty">
          <table-empty :table-height="tableMaxHeight" v-show="!tableIsLoading"/>
        </template>
      </el-table>
    </div>
    <edit-dialog ref="editDialog" @loadRoleList="loadRoleList"></edit-dialog>
    <detalis ref="details"></detalis>
</div>
</template>

<script>
import ErrorHandler from '@/common/ErrorHandler'
import TextTool from '@/utils/TextTool'
import EditDialog from './editDialog'
import Detalis from './details'
import TableEmpty from '@/components/base/TableEmpty'
import TableLoading from '@/components/base/TableLoading.vue'
import { debounce } from 'lodash'
import { roleManagementApi } from '@/api/role'
export default {
  name: 'company-organization',
  components: { TableLoading, TableEmpty, EditDialog, Detalis },
  data () {
    return {
      filterData: [],
      searchText: '',
      roleList: [],
      menuTree: [],
      sysTree: [],
      dialogVisible: false,
      tableIsLoading: true,
      defaultRole: null
    }
  },
  computed: {
    tableMaxHeight () {
      return window.innerHeight - 50 - 32 - 48
    },
    isPause () {
      return this.$root.isPause
    }
  },
  watch: {
    async isPause (val) {
      if (!val) {
        this.searchText = ''
        await this.getMenu()
        this.debounce()
      }
    },
    searchText () {
      this.debounce()
    }
  },
  async mounted () {
    await this.getDefaultRole()
    await this.loadRoleList()
    await this.getMenu()
    !this.debounce && (this.debounce = debounce(this.loadRoleList, 500))
  },
  methods: {
    measureText (pText, pFontSize, pStyle) {
      return TextTool.measureText(pText, pFontSize, pStyle)
    },
    click (data) {
      console.log(data)
    },
    observe (dom, row) {
      const observe = new IntersectionObserver(([change]) => {
        row.text = change.isIntersecting || (change.intersectionRect.height > 0 && (change.intersectionRect.height < change.boundingClientRect.height))
      }, {
        threshold: [1.0],
        root: document.getElementsByClassName('select_parent')[0]
      })
      observe.observe(dom)
    },
    getMenu () {
      roleManagementApi.getMenu().then(response => {
        this.menuTree = response.data
        const targetObject = this.menuTree.find(obj => obj.name === this.$t('managementCenter'))
        if (targetObject) {
          targetObject.children = []
        }
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
    },
    addRole () {
      this.$refs.editDialog.open(this.$t('add'), this.menuTree)
    },
    filterHighLight (value) {
      return TextTool.highlightFilters(value, this.searchText)
    },
    loadRoleList () {
      this.tableIsLoading = false
      roleManagementApi.loadRoles(this.searchText).then(response => {
        console.log(response)
        const ir = response.data.find(item => this.defaultRole && this.defaultRole.id === item.id)
        const dr = response.data.filter(item => item.default)
        const or = response.data.filter(item => !item.default && (this.defaultRole.id !== item.id))
        if (ir) {
          this.roleList = [ir, ...dr, ...or]
        } else {
          this.roleList = [...dr, ...or]
        }
        this.tableIsLoading = false
      }).catch(err => {
        ErrorHandler.formatError(err)
        this.tableIsLoading = err.message === 'cancel'
      })
    },
    viewRole (row) {
      roleManagementApi.getRolesInfo(row.id).then(({ data }) => {
        this.$refs.details.open(this.$t('查看'), data, this.menuTree)
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
    },
    updateRole (row) {
      roleManagementApi.getRolesInfo(row.id).then(({ data }) => {
        this.$refs.editDialog.open(this.$t('edit'), this.menuTree, data)
      }).catch(err => {
        ErrorHandler.formatError(err)
      })
    },
    deleteRole (roleId) {
      this.$confirm(this.$t('删除角色后用户将不再拥有对应权限'), this.$t('提示'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'error'
      }).then(({ value }) => {
        roleManagementApi.deleteRole(roleId).then(() => {
          this.loadRoleList()
          this.$message.success(this.$t('删除成功'))
        }).catch(err => {
          ErrorHandler.formatError(err)
        })
      })
    },
    rowClassName ({ row }) {
      return 'rowId' + row.id
    },
    async getDefaultRole() {
      await roleManagementApi.getDefaultRole().then(({data}) => {
        this.defaultRole = data
      })
    },
  }
}
</script>

<style scoped lang="scss">

.add-button{
  margin-bottom: 16px;
}
.el-form > div:nth-last-child(1) {
  margin-bottom: 0;
}
</style>
