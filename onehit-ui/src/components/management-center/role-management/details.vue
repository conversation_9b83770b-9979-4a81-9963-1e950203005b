<template>
  <el-dialog
    width="560px"
    append-to-body
    :title="dialogTitle"
    v-if="dialogVisible"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false">
    <div style="padding-top: 24px; overflow: auto; height: calc(50vh - 90px)">
      <el-form ref="form" size="small"  label-width="100px" >
        <el-form-item prop="name" :label="$t('角色名称') + '：'">
          <span>{{form.name}}</span>
        </el-form-item>
        <el-form-item prop="describe" :label="$t('描述') + '：'">
          <span>{{form.remark}}</span>
        </el-form-item>
        <el-form-item :label="$t('权限') + '：'" prop="permissions">
          <el-tree
            v-if="onehitScenes.length > 0"
            highlight-current
            ref="permissionTree"
            node-key="name"
            :data="onehitScenes"
            :props="{label: 'name', children: 'children'}">
            <template slot-scope="{ node, data }">
              <span>{{data.name}}</span>
            </template>
          </el-tree>
          <el-tree
            v-if="permissionTree.length > 0"
            highlight-current
            ref="permissionTree"
            node-key="id"
            :data="permissionTree"
            :default-expanded-keys="[1]"
            :props="{label: 'name', children: 'children'}">
            <template slot-scope="{ node, data }">
              <span>
                {{data.name}}
                <el-popover
                  :open-delay="500"
                   popper-class="text_tooltip"
                   trigger="hover"
                   v-if="data.name === '目录导入/导出/新增/编辑/移动/删除'"
                   placement="top"
                   :content="$t('选中后该角色默认有所有目录及文档的管理权限')">
                   <i  slot="reference" class="edc-icon-a-tishi" style="color: rgba(8, 84, 161, 1); font-size: 14px"></i>
                </el-popover>
              </span>
            </template>
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
          <el-button style="color: rgba(8, 84, 161, 1)!important;" size="small" @click="dialogVisible = false">{{ $t('关闭') }}</el-button>
      </span>
  </el-dialog>
</template>
<script>

import { configApi } from '@/api/onehit'

export default {
  data () {
    return {
      form: {
        name: null,
        describe: null
      },
      onehitScenes: [],
      permissionTree: [],
      featureIds: [],
      dialogTitle: this.$t('角色详情'),
      dialogVisible: false
    }
  },
  computed: {
    treeMaxHeight () {
      return window.innerHeight - 48 - 48 - 44 - 24 - 48
    }
  },
  methods: {
    async open (title, row, tree) {
      this.dialogTitle = title
      this.form = row
      this.featureIds = row.featureIds
      const permissionTree = this.travelTree(tree)
      this.permissionTree = permissionTree
      await this.getRoleScenes(row.id)
      this.dialogVisible = true
    },
    travelTree (data) {
      const tree = []
      data?.length && data.forEach(item => {
        if (!this.featureIds.includes(item.id)) return
        const newNode = {
          ...item,
          children: this.travelTree(item.children?.filter(e => this.featureIds.includes(e.id)) || [])
        }
        tree.push(newNode)
      })
      return tree
    },
    async getRoleScenes (roleId) {
      let roleScenes = []
      const { data: scenes } = await configApi.getAllScene()
      if (roleId) {
        const { data: permissions } = await configApi.getPermissions(roleId)
        if (permissions) {
          roleScenes = scenes.filter(item => permissions.sceneNameList.includes(item))
        }
      }
      this.onehitScenes = []
      if (roleScenes.length > 0) {
        this.onehitScenes = [{
          name: this.$t('业务工作台'),
          children: roleScenes.map(role => ({ name: role }))
        }]
      }
    }
  }
}
</script>
<style scoped lang="scss">
* {
  color:rgba(24, 42, 78, 1) !important;
}
.tree{
  ::v-deep .el-tree-node__expand-icon{
    padding: 0;
    margin-left: 16px;
  }
}
.el-tree{
  margin-top: 2px;
}
::v-deep .el-form-item__content{
  overflow: unset;
}
::v-deep .el-form-item--small.el-form-item{
  margin-bottom: 8px!important;
  margin-right: 0!important;
}
::v-deep .el-dialog__body{
  padding: 0px!important;
  .el-form{
    padding: 0 60px;
  }
}
::v-deep .el-form-item__label{
  padding: 0!important;
  color:rgba(24, 42, 78, 1) !important;
}
::v-deep .el-tree-node__content:hover{
  background-color: transparent !important;
}
::v-deep .is-current > .el-tree-node__content:hover{
  background-color: transparent!important;
}
::v-deep .is-current > .el-tree-node__content{
  background-color: transparent!important;
}
::v-deep .el-tree-node:focus > .el-tree-node__content{
  background-color: transparent;
}
</style>
