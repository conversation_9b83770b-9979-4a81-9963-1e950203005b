<template>
  <div class="add-or-edit-label">
    <el-dialog
      :title="dialogTitle"
      :visible.sync="addOrEditDialog"
      width="486px"
      @opened="dialog_Opened"
      @closed="dialog_Closed"
    >
      <div class="dialog-content">
        <el-form
          ref="dataForm"
          :model="formData"
          :rules="dataRules"
          size="small"
          label-position="left"
        >
          <el-form-item :label="$t('labelModel.标注类型') + '：'" prop="type">
            <el-input
              :maxlength="30"
              v-model="formData.type"
              @blur="formData.type = $event.target.value.trim()"
              :placeholder="$t('labelModel.请输入标注类型')"
              class="scenatorStyle"
              style="width: 80%"
            ></el-input>
          </el-form-item>
          <div class="item-splite"></div>
          <el-form-item :label="$t('labelModel.labelLegend') + '：'">
            <div class="label-img-upload">
              <span class="img-label">{{ $t('labelModel.三维标注文件格式Tip') }}</span>
              <div class="img-group">
                <div class="select-img">
                  <span class="img-type">{{ $t('labelModel.三维') }}:</span>
                  <div class="img-wraper-out">
                    <div
                      class="img-wraper"
                      @click.stop="open_SelectImgDialog(1)"
                    >
                      <div class="hover-img" v-if="formData.defaultSymbol">
                        <img :src="previewPath(formData.defaultSymbolPath)" class="avatar" />
                        <div class="avatar-shadow">
                          <i
                            class="el-icon-edit"
                            @click.stop="open_SelectImgDialog(1)"
                          ></i>
                          <i
                            class="el-icon-delete"
                            @click.stop="remove_Img(1)"
                          ></i>
                        </div>
                      </div>
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>

                      <div class="img-description">{{ $t('labelModel.默认状态') }}</div>
                    </div>
                    <div class="img-wraper" @click="open_SelectImgDialog(2)">
                      <div class="hover-img" v-if="formData.selectedSymbol">
                        <img
                          :src="previewPath(formData.selectedSymbolPath)"
                          class="avatar"
                        />
                        <div class="avatar-shadow">
                          <i
                            class="el-icon-edit"
                            @click.stop="open_SelectImgDialog(2)"
                          ></i>
                          <i
                            class="el-icon-delete"
                            @click.stop="remove_Img(2)"
                          ></i>
                        </div>
                      </div>
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                      <div class="img-description">{{ $t('labelModel.选中状态') }}</div>
                    </div>
                  </div>
                </div>
                <div class="select-img">
                  <span class="img-type">P&ID:</span>
                  <div class="img-wraper-out">
                    <div class="img-wraper" @click="open_SelectImgDialog(3)">
                      <div class="hover-img" v-if="formData.pidDefaultSymbol">
                        <img
                          v-if="formData.pidDefaultSymbol.indexOf('svg') < 0"
                          :src="previewPath(formData.pidDefaultSymbolPath)"
                          class="avatar"
                        />
                        <img v-else :src="previewPath(formData.pidDefaultSymbolPath)" />
                        <div class="avatar-shadow">
                          <i
                            class="el-icon-edit"
                            @click.stop="open_SelectImgDialog(3)"
                          ></i>
                          <i
                            class="el-icon-delete"
                            @click.stop="remove_Img(3)"
                          ></i>
                        </div>
                      </div>
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                      <div class="img-description">{{ $t('labelModel.默认状态') }}</div>
                    </div>
                    <div class="img-wraper" @click="open_SelectImgDialog(4)">
                      <div class="hover-img" v-if="formData.pidSelectedSymbol">
                        <img
                          v-if="formData.pidSelectedSymbol.indexOf('svg') < 0"
                          :src="previewPath(formData.pidSelectedSymbolPath)"
                          class="avatar"
                        />
                        <img v-else :src="previewPath(formData.pidSelectedSymbolPath)" />
                        <div class="avatar-shadow">
                          <i
                            class="el-icon-edit"
                            @click.stop="open_SelectImgDialog(4)"
                          ></i>
                          <i
                            class="el-icon-delete"
                            @click.stop="remove_Img(4)"
                          ></i>
                        </div>
                      </div>
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                      <div class="img-description">{{ $t('labelModel.选中状态') }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <el-dialog
              class="scenatorStyle"
              custom-class="inner-dialog"
              :title="$t('为您推荐')"
              :visible.sync="imgDialog"
              width="346px"
              @opened="img_DialogOpened"
              @closed="img_DialogClosed"
              append-to-body
            >
              <template slot="title">
                <span class="img-recommend">{{ $t('为您推荐') }}</span>
              </template>
              <div class="default-img-list" v-if="imgDialogType === 1">
                <div
                  class="img-item"
                  v-for="(item, idx) in defaultImgList"
                  :key="idx"
                  @click="select_Img(imgDialogType, item)"
                >
                  <img :src="previewPath(item.path)" alt="" />
                </div>
              </div>
              <div class="default-img-list" v-if="imgDialogType === 2">
                <div
                  class="img-item"
                  v-for="(item, idx) in selectedImgList"
                  :key="idx"
                  @click="select_Img(imgDialogType, item)"
                >
                  <img :src="previewPath(item.path)" alt="" />
                </div>
              </div>
              <div class="default-img-list" v-if="imgDialogType === 3">
                <div
                  class="img-item"
                  v-for="(item, idx) in pidDefaultImgList"
                  :key="idx"
                  @click="select_Img(imgDialogType, item)"
                >
                  <img :src="previewPath(item.path)" alt="" />
                </div>
              </div>
              <div class="default-img-list" v-if="imgDialogType === 4">
                <div
                  class="img-item"
                  v-for="(item, idx) in pidSelectedImgList"
                  :key="idx"
                  @click="select_Img(imgDialogType, item)"
                >
                  <img :src="previewPath(item.path)" alt="" />
                </div>
              </div>
              <div class="dilalog-footer-split"></div>
              <div class="img-select-footer">
                <el-upload
                  class="avatar-uploader"
                  action=""
                  :show-file-list="false"
                  :auto-upload="false"
                  :on-change="default_HandleAvatarSuccess"
                >
                  <el-button
                    type="secondary--button"
                    class="scenatorStyle upload-btn"
                    >{{ $t('labelModel.本地上传') }}</el-button
                  >
                </el-upload>
              </div>
            </el-dialog>
          </el-form-item>
          <div class="item-splite"></div>
          <el-form-item label="" class="property-form-item">
            <template slot="label">
              <el-tooltip
                class="item"
                content=""
                placement="top"
                effect="light"
                popper-class="labelManager-tooltip"
              >
                <template slot="content">
                  <div class="description-text">
                    <div>
                      {{ $t('labelModel.属性注释') }}
                    </div>
                    <div class="description-img">
                      <el-image :src="labelDesc"></el-image>
                    </div>
                  </div>
                </template>
                <span
                  class="onehit-icon onehit-icon-tipIcon"
                  style="color: #687173; padding-right: 4px"
                ></span>
              </el-tooltip>
              <span>{{ $t('properties') }}：</span>
            </template>
            <div class="propetory-content">
              <el-button
                type="secondary--button"
                class="scenatorStyle"
                @click="add_PropertyName"
                icon="el-icon-plus"
                >{{ $t('labelModel.新增属性') }}</el-button
              >
              <div class="empty-tips" v-if="propertiesList.length === 0">
                {{ $t('labelModel.属性为空Tip') }}
              </div>
            </div>
          </el-form-item>
          <div class="property-list">
            <div
              class="property-list-item"
              v-for="item in propertiesList"
              :key="item.propertyName"
            >
              <span class="item-radio">
                <el-radio
                  v-model="formData.showColumn"
                  :label="item.propertyName"
                  @click="select_Radio(item)"
                ></el-radio>
              </span>
              <el-form-item
                label=""
                :prop="item.propertyName"
                :rules="[
                  {
                    required: true,
                    message: $t('labelModel.属性名称不能为空'),
                    trigger: 'blur',
                  },
                  {
                    max: 30,
                    message: $t('labelModel.内容太长，30个字符内'),
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input
                  v-model="formData[item.propertyName]"
                  @blur="
                    formData[item.propertyName] = $event.target.value.trim()
                  "
                  :maxlength="30"
                  :placeholder="$t('请输入属性名称')"
                  class="scenatorStyle"
                ></el-input>
              </el-form-item>
              <span class="item-delete">
                <i
                  class="el-icon-delete onehit-icon onehit-icon-delete_property_icon"
                  style="cursor: pointer; color: #0854a1"
                  @click="delete_Property(item)"
                ></i>
              </span>
            </div>
          </div>
          <div class="item-splite"></div>
          <el-form-item :label="$t('附件上传') + '：'" prop="enableAttachment">
            <div class="uploadable">
              <el-switch
                v-model="formData.enableAttachment"
                :active-text="formData.enableAttachment ? $t('labelModel.开启') : $t('关闭')"
                active-color="#EBF5FE"
                inactive-color="#EDEDED"
              >
              </el-switch>
              <span class="img-label"
                >{{ $t('labelModel.标注附件格式') }}</span
              >
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="main--button"
          class="scenatorStyle"
          @click="submitForm('dataForm')"
          >{{ $t('保存')}}</el-button
        >
        <el-button
          type="secondary--button"
          class="scenatorStyle"
          @click="addOrEditDialog = false"
          >{{ $t('取消') }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { labelDesc } from '@/utils/Base64ImgUtil'
import { labelApi } from '@/api/onehit'
export default {
  props: {
    status: {
      default: true,
      type: Boolean
    },
    currentLabel: {
      default: () => {
        return {}
      },
      type: Object
    }
  },

  data () {
    return {
      labelDesc,
      dialogTitle: '',
      addOrEditDialog: false,
      imgDialogType: '',
      imgDialog: false,
      formData: {
        type: '',
        defaultSymbol: '',
        defaultSymbolPath: '',
        selectedSymbol: '',
        selectedSymbolPath: '',
        pidDefaultSymbol: '',
        pidDefaultSymbolPath: '',
        pidSelectedSymbol: '',
        pidSelectedSymbolPath: '',
        columns: '',
        showColumn: '',
        enableAttachment: true
      },
      dataRules: {
        type: [
          {
            required: true,
            message: this.$t('labelModel.请输入标注类型'),
            trigger: 'blur'
          },
          {
            max: 30,
            message: this.$t('labelModel.内容太长，30个字符内'),
            trigger: 'blur'
          }
        ],
        defaultImg: {
          required: true,
          message: this.$t('请选择图片'),
          trigger: 'change'
        },
        selectedImg: {
          required: true,
          message: this.$t('请选择图片'),
          trigger: 'change'
        }
      },
      propertiesList: [],
      defaultImgList: [
        {
          path: '3DDefault/3d_default_icon1.png',
          parentNode: '3DDefault',
          node: '3d_default_icon1.png'
        },
        {
          path: '3DDefault/3d_default_icon2.png',
          parentNode: '3DDefault',
          node: '3d_default_icon2.png'
        },
        {
          path: '3DDefault/3d_default_icon3.png',
          parentNode: '3DDefault',
          node: '3d_default_icon3.png'
        },
        {
          path: '3DDefault/3d_default_icon4.png',
          parentNode: '3DDefault',
          node: '3d_default_icon4.png'
        },
        {
          path: '3DDefault/3d_default_icon5.png',
          parentNode: '3DDefault',
          node: '3d_default_icon5.png'
        },
        {
          path: '3DDefault/3d_default_icon6.png',
          parentNode: '3DDefault',
          node: '3d_default_icon6.png'
        },
        {
          path: '3DDefault/3d_default_icon7.png',
          parentNode: '3DDefault',
          node: '3d_default_icon7.png'
        },
        {
          path: '3DDefault/3d_default_icon8.png',
          parentNode: '3DDefault',
          node: '3d_default_icon8.png'
        },
        {
          path: '3DDefault/3d_default_icon9.png',
          parentNode: '3DDefault',
          node: '3d_default_icon9.png'
        },
        {
          path: '3DDefault/3d_default_icon10.png',
          parentNode: '3DDefault',
          node: '3d_default_icon10.png'
        },
        {
          path: '3DDefault/3d_default_icon11.png',
          parentNode: '3DDefault',
          node: '3d_default_icon11.png'
        },
        {
          path: '3DDefault/3d_default_icon12.png',
          parentNode: '3DDefault',
          node: '3d_default_icon12.png'
        },
        {
          path: '3DDefault/3d_default_icon13.png',
          parentNode: '3DDefault',
          node: '3d_default_icon13.png'
        },
        {
          path: '3DDefault/3d_default_icon14.png',
          parentNode: '3DDefault',
          node: '3d_default_icon14.png'
        },
        {
          path: '3DDefault/3d_default_icon15.png',
          parentNode: '3DDefault',
          node: '3d_default_icon15.png'
        },
        {
          path: '3DDefault/3d_default_icon16.png',
          parentNode: '3DDefault',
          node: '3d_default_icon16.png'
        },
        {
          path: '3DDefault/3d_default_icon17.png',
          parentNode: '3DDefault',
          node: '3d_default_icon17.png'
        },
        {
          path: '3DDefault/3d_default_icon18.png',
          parentNode: '3DDefault',
          node: '3d_default_icon18.png'
        },
        {
          path: '3DDefault/3d_default_icon19.png',
          parentNode: '3DDefault',
          node: '3d_default_icon19.png'
        }
      ],
      selectedImgList: [
        {
          path: '3DSelected/3d_selected_icon1.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon1.png'
        },
        {
          path: '3DSelected/3d_selected_icon2.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon2.png'
        },
        {
          path: '3DSelected/3d_selected_icon3.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon3.png'
        },
        {
          path: '3DSelected/3d_selected_icon4.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon4.png'
        },
        {
          path: '3DSelected/3d_selected_icon5.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon5.png'
        },
        {
          path: '3DSelected/3d_selected_icon6.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon6.png'
        },
        {
          path: '3DSelected/3d_selected_icon7.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon7.png'
        },
        {
          path: '3DSelected/3d_selected_icon8.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon8.png'
        },
        {
          path: '3DSelected/3d_selected_icon9.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon9.png'
        },
        {
          path: '3DSelected/3d_selected_icon10.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon10.png'
        },
        {
          path: '3DSelected/3d_selected_icon11.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon11.png'
        },
        {
          path: '3DSelected/3d_selected_icon12.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon12.png'
        },
        {
          path: '3DSelected/3d_selected_icon13.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon13.png'
        },
        {
          path: '3DSelected/3d_selected_icon14.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon14.png'
        },
        {
          path: '3DSelected/3d_selected_icon15.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon15.png'
        },
        {
          path: '3DSelected/3d_selected_icon16.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon16.png'
        },
        {
          path: '3DSelected/3d_selected_icon17.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon17.png'
        },
        {
          path: '3DSelected/3d_selected_icon18.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon18.png'
        },
        {
          path: '3DSelected/3d_selected_icon19.png',
          parentNode: '3DSelected',
          node: '3d_selected_icon19.png'
        }
      ],
      pidDefaultImgList: [
        {
          path: 'pidDefault/pid_default_icon1.svg',
          parentNode: 'pidDefault',
          node: 'pid_default_icon1.svg'
        },
        {
          path: 'pidDefault/pid_default_icon2.svg',
          parentNode: 'pidDefault',
          node: 'pid_default_icon2.svg'
        },
        {
          path: 'pidDefault/pid_default_icon3.svg',
          parentNode: 'pidDefault',
          node: 'pid_default_icon3.svg'
        },
        {
          path: 'pidDefault/pid_default_icon4.svg',
          parentNode: 'pidDefault',
          node: 'pid_default_icon4.svg'
        }
      ],
      pidSelectedImgList: [
        {
          path: 'pidSelected/pid_selected_icon1.svg',
          parentNode: 'pidSelected',
          node: 'pid_selected_icon1.svg'
        },
        {
          path: 'pidSelected/pid_selected_icon2.svg',
          parentNode: 'pidSelected',
          node: 'pid_selected_icon2.svg'
        },
        {
          path: 'pidSelected/pid_selected_icon3.svg',
          parentNode: 'pidSelected',
          node: 'pid_selected_icon3.svg'
        },
        {
          path: 'pidSelected/pid_selected_icon4.svg',
          parentNode: 'pidSelected',
          node: 'pid_selected_icon4.svg'
        }
      ]
    }
  },
  computed: {
    previewPath () {
      return (path) => {
        if (typeof path !== 'string') {
          return
        }
        if (path.trim() === '' || path.startsWith('http') || path.startsWith('blob')) {
          return path
        }
        if (path.indexOf('3DDefault') < 0 && path.indexOf('3DSelected') < 0 && path.indexOf('pidDefault') < 0 && path.indexOf('pidSelected') < 0) {
          return window.location.origin + '/OneHit/label/common/symbol/preview?isBuiltIn=false&path=' + path
        }
        return window.location.origin + '/OneHit/label/common/symbol/preview?isBuiltIn=true&path=' + path
      }
    }
  },
  methods: {
    remove_Img (type) {
      if (type === 1) {
        this.formData.defaultSymbol = ''
        this.formData.defaultSymbolPath = ''
      } else if (type === 2) {
        this.formData.selectedSymbol = ''
        this.formData.selectedSymbolPath = ''
      } else if (type === 3) {
        this.formData.pidDefaultSymbol = ''
        this.formData.pidDefaultSymbolPath = ''
      } else if (type === 4) {
        this.formData.pidSelectedSymbol = ''
        this.formData.pidSelectedSymbolPath = ''
      }
    },
    submitForm (formName) {
      const that = this
      this.$refs[formName].validate(async function (valid) {
        if (valid) {
          // 检查属性名称是否重复
          const uniquePropertyName = that.unique_PropertyName()
          if (uniquePropertyName) {
            that.$message({
              dangerouslyUseHTMLString: true,
              duration: 3000,
              customClass: 'scenatorStyle scenator_briefMsg warn',
              message: that.$t('labelModel.属性名称重复')
            })
            return
          }

          if (that.status) {
            // 检查标注类型是否已存在
            const flag = await that.unique_labelTypeName(that.formData.type)
            if (!flag) {
              that.$message({
                dangerouslyUseHTMLString: true,
                duration: 3000,
                customClass: 'scenatorStyle scenator_briefMsg warn',
                message:  that.$t('labelModel.标注类型已存在')
              })
              return
            }
            // 检查上传的图例是否规范
            const result = that.unique_LabelImg()
            if (result) {
              if (that.propertiesList.length === 0) {
                that.$message({
                  dangerouslyUseHTMLString: true,
                  duration: 3000,
                  customClass: 'scenatorStyle scenator_briefMsg warn',
                  message: that.$t('labelModel.属性为空Tip')
                })
                return
              }
              that.add_NewLabelType()
            }
          } else {
            if (that.formData.type !== that.currentLabel.type) {
              // 检查标注类型是否已存在
              const flag = await that.unique_labelTypeName(that.formData.type)
              if (!flag) {
                that.$message({
                  dangerouslyUseHTMLString: true,
                  duration: 3000,
                  customClass: 'scenatorStyle scenator_briefMsg warn',
                  message: that.$t('labelModel.标注类型已存在')
                })
                return
              }
            }
            const result = that.unique_LabelImg()
            if (result) {
              if (that.propertiesList.length === 0) {
                that.$message({
                  dangerouslyUseHTMLString: true,
                  duration: 3000,
                  customClass: 'scenatorStyle scenator_briefMsg warn',
                  message: that.$t('labelModel.属性为空Tip')
                })
                return
              }
              that.edit_LabelInfo()
            }
          }
        } else {
          return false
        }
      })
    },
    unique_PropertyName () {
      let flag = false
      const newArr = [
        ...new Set(
          this.propertiesList.map((ele) => this.formData[ele.propertyName])
        )
      ]
      if (newArr.length < this.propertiesList.length) {
        flag = true
      }
      return flag
    },
    unique_LabelImg () {
      let flag = true
      if (
        !this.formData.defaultSymbol &&
        !this.formData.selectedSymbol &&
        !this.formData.pidDefaultSymbol &&
        !this.formData.pidSelectedSymbol
      ) {
        flag = false
        this.$message({
          dangerouslyUseHTMLString: true,
          duration: 3000,
          customClass: 'scenatorStyle scenator_briefMsg warn',
          message: this.$t('labelModel.请至少上传一组图例')
        })
        return flag
      }
      if (this.formData.defaultSymbol || this.formData.selectedSymbol) {
        if (this.formData.defaultSymbol && this.formData.selectedSymbol) {
          flag = true
        } else {
          flag = false
          this.$message({
            dangerouslyUseHTMLString: true,
            duration: 3000,
            customClass: 'scenatorStyle scenator_briefMsg warn',
            message: this.$('labelModel.三维缺少图例')
          })
        }
        return flag
      }
      if (this.formData.pidDefaultSymbol || this.formData.pidSelectedSymbol) {
        if (this.formData.pidDefaultSymbol && this.formData.pidSelectedSymbol) {
          flag = true
        } else {
          flag = false
          this.$message({
            dangerouslyUseHTMLString: true,
            duration: 3000,
            customClass: 'scenatorStyle scenator_briefMsg warn',
            message: this.$t('labelModel.PID缺少图例')
          })
        }
        return flag
      }
      return flag
    },
    unique_labelTypeName (labelTypeName) {
      return new Promise((resolve) => {
        const params = {
          type: encodeURIComponent(labelTypeName),
          queryChild: false
        }
        labelApi
          .getLabelTypeList(params)
          .then((res) => {
            if (res.data.result && res.data.result.records) {
              if (res.data.result.records.length > 0) {
                resolve(false)
              } else {
                resolve(true)
              }
            } else {
              resolve(false)
            }
          })
          .catch((err) => {
            console.log(err.data)
            this.$message({
              angerouslyUseHTMLString: true,
              duration: 3000,
              customClass: 'scenatorStyle scenator_briefMsg error',
              message: `${
                err?.data?.message.includes('标注分类')
                  ? err?.data?.message
                  : this.$t('labelModel.标注配置保存失败')
              }`
            })
          })
      })
    },
    add_NewLabelType () {
      const params = {
        type: encodeURIComponent(this.formData.type),
        defaultSymbol: this.formData.defaultSymbol,
        selectedSymbol: this.formData.selectedSymbol,
        pidDefaultSymbol: this.formData.pidDefaultSymbol,
        pidSelectedSymbol: this.formData.pidSelectedSymbol,
        showColumn: this.formData[this.formData.showColumn],
        columns: this.propertiesList
          .map((ele) => {
            return this.formData[ele.propertyName]
          })
          .toString(','),
        enableAttachment: this.formData.enableAttachment ? 1 : 0
      }
      labelApi
        .addLabelType(params)
        .then((res) => {
          if (res.data.success) {
            this.$message({
              angerouslyUseHTMLString: true,
              duration: 3000,
              customClass: 'scenatorStyle scenator_briefMsg success',
              message: this.$t('labelModel.标注配置已保存')
            })
            this.addOrEditDialog = false
            this.$emit('updateTableData', true)
          }
        })
        .catch((err) => {
          this.$message({
            angerouslyUseHTMLString: true,
            duration: 3000,
            customClass: 'scenatorStyle scenator_briefMsg error',
            message: `${
              err?.data?.message.includes('标注分类')
                ? err?.data?.message
                : this.$t('labelModel.标注配置保存失败')
            }`
          })
        })
    },
    checkStrLen (str) {
      let len = 0
      for (let i = 0; i < str.length; i++) {
        if (str.charCodeAt(i) > 255) {
          len += 3
        } else {
          len++
        }
      }
      return len
    },
    edit_LabelInfo () {
      const params = {
        id: this.formData.id,
        type: encodeURIComponent(this.formData.type),
        defaultSymbol: this.formData.defaultSymbol,
        selectedSymbol: this.formData.selectedSymbol,
        pidDefaultSymbol: this.formData.pidDefaultSymbol,
        pidSelectedSymbol: this.formData.pidSelectedSymbol,
        columns: this.propertiesList
          .map((ele) => {
            return this.formData[ele.propertyName]
          })
          .toString(','),
        showColumn: this.formData[this.formData.showColumn],
        enableAttachment: this.formData.enableAttachment ? 1 : 0
      }
      labelApi
        .editLabelType(params)
        .then((res) => {
          this.$message({
            angerouslyUseHTMLString: true,
            duration: 3000,
            customClass: 'scenatorStyle scenator_briefMsg success',
            message: this.$t('labelModel.标注配置已保存')
          })
          this.addOrEditDialog = false
          this.$emit('updateTableData')
        })
        .catch((err) => {
          this.$message({
            angerouslyUseHTMLString: true,
            duration: 3000,
            customClass: 'scenatorStyle scenator_briefMsg error',
            message: `${
              err?.data?.message.includes('标注分类')
                ? err?.data?.message
                : this.$t('labelModel.标注配置保存失败')
            }`
          })
          console.log(err)
        })
    },
    dialog_Opened () {
      if (this.status) {
        this.dialogTitle = this.$t('labelModel.createLabelConfig')
      } else {
        this.dialogTitle = this.$t('labelModel.编辑标注配置')
        this.formData = Object.assign(this.formData, this.currentLabel)
        this.formData.enableAttachment =
          this.formData.enableAttachment === 1
        this.get_ImgPreviewPath()
        if (this.currentLabel.columns) {
          this.propertiesList = this.currentLabel.columns
            .split(',')
            .map((ele) => {
              const uuid = this.create_UuidCode()
              // this.formData[uuid] = ele;
              this.$set(this.formData, uuid, ele)
              if (ele === this.formData.showColumn) {
                this.formData.showColumn = uuid
              }
              return {
                propertyName: uuid
              }
            })
        }
      }
    },
    get_ImgPreviewPath () {
      const baseUrl = window.location.origin + '/OneHit/label/common/symbol/preview'
      if (this.formData.defaultSymbol) {
        if (this.formData.defaultSymbol.indexOf('3DDefault') >= 0) {
          this.formData.defaultSymbolPath = baseUrl + '?isBuiltIn=true&path=' + this.formData.defaultSymbol
        } else {
          this.formData.defaultSymbolPath = baseUrl + '?isBuiltIn=false&path=' + this.formData.defaultSymbol
        }
      }
      if (this.formData.selectedSymbol) {
        if (this.formData.selectedSymbol.indexOf('3DSelected') >= 0) {
          this.formData.selectedSymbolPath = baseUrl + '?isBuiltIn=true&path=' + this.formData.selectedSymbol
        } else {
          this.formData.selectedSymbolPath = baseUrl + '?isBuiltIn=false&path=' + this.formData.selectedSymbol
        }
      }
      if (this.formData.pidDefaultSymbol) {
        if (this.formData.pidDefaultSymbol.indexOf('pidDefault') >= 0) {
          this.formData.pidDefaultSymbolPath = baseUrl + '?isBuiltIn=true&path=' + this.formData.pidDefaultSymbol
        } else {
          this.formData.pidDefaultSymbolPath =
            baseUrl + '?isBuiltIn=false&path=' + this.formData.pidDefaultSymbol.split('/')[0] + '/' +
            this.formData.pidDefaultSymbol.split('/')[1].split('.')[0] + '.' + 'svg'
        }
      }
      if (this.formData.pidSelectedSymbol) {
        if (this.formData.pidSelectedSymbol.indexOf('pidSelected') >= 0) {
          this.formData.pidSelectedSymbolPath = baseUrl + '?isBuiltIn=true&path=' + this.formData.pidSelectedSymbol
        } else {
          this.formData.pidSelectedSymbolPath =
            baseUrl + '?isBuiltIn=false&path=' + this.formData.pidSelectedSymbol.split('/')[0] + '/' +
            this.formData.pidSelectedSymbol.split('/')[1].split('/')[0] + '.' + 'svg'
        }
      }
    },
    dialog_Closed () {
      this.$refs.dataForm.resetFields()
      this.propertiesList = []
      this.formData.showColumn = ''
      this.formData.defaultSymbol = ''
      this.formData.defaultSymbolPath = ''
      this.formData.selectedSymbol = ''
      this.formData.selectedSymbolPath = ''
      this.formData.pidDefaultSymbol = ''
      this.formData.pidDefaultSymbolPath = ''
      this.formData.pidSelectedSymbol = ''
      this.formData.pidSelectedSymbolPath = ''
      this.formData.enableAttachment = true
    },
    async default_HandleAvatarSuccess (file) {
      const baseUrl = window.location.origin + '/OneHit/label/common/symbol/preview?isBuiltIn=false&path='
      const flag = this.beforeAvatarUpload(file)
      if (!flag) {
        return
      }
      if (this.imgDialogType === 1) {
        const { result, message } = await this.upload_LabelImg('3d', file)
        this.formData.defaultSymbol = message
        if (result) {
          this.formData.defaultSymbolPath = URL.createObjectURL(file.raw)
        }
      } else if (this.imgDialogType === 2) {
        const { result, message } = await this.upload_LabelImg('3d', file)
        this.formData.selectedSymbol = message
        if (result) {
          this.formData.selectedSymbolPath = URL.createObjectURL(file.raw)
        }
      } else if (this.imgDialogType === 3) {
        const { result, message } = await this.upload_LabelImg('pid', file)
        this.formData.pidDefaultSymbol = message
        if (result) {
          this.formData.pidDefaultSymbolPath = baseUrl + message
        }
      } else if (this.imgDialogType === 4) {
        const { result, message } = await this.upload_LabelImg('pid', file)
        this.formData.pidSelectedSymbol = message
        if (result) {
          this.formData.pidSelectedSymbolPath = baseUrl + message
        }
      }
      this.imgDialog = false
    },
    select_Img (type, item) {
      const baseUrl = window.location.origin + '/OneHit/label/common/symbol/preview?isBuiltIn=true&path='
      if (type === 1) {
        this.formData.defaultSymbol = item.parentNode + '/' + item.node
        this.formData.defaultSymbolPath = `${baseUrl}${item.parentNode}/${item.node}`
      } else if (type === 2) {
        this.formData.selectedSymbol = item.parentNode + '/' + item.node
        this.formData.selectedSymbolPath = `${baseUrl}${item.parentNode}/${item.node}`
      } else if (type === 3) {
        this.formData.pidDefaultSymbol = item.parentNode + '/' + item.node
        this.formData.pidDefaultSymbolPath = `${baseUrl}${item.parentNode}/${item.node}`
      } else if (type === 4) {
        this.formData.pidSelectedSymbol = item.parentNode + '/' + item.node
        this.formData.pidSelectedSymbolPath = `${baseUrl}${item.parentNode}/${item.node}`
      }
      this.imgDialog = false
    },
    upload_LabelImg (type, file) {
      return new Promise((resolve) => {
        const params = {
          type,
          file
        }
        labelApi
          .uploadImg(params)
          .then((res) => {
            if (res.data.success) {
              this.$message({
                dangerouslyUseHTMLString: true,
                duration: 3000,
                customClass: 'scenatorStyle scenator_briefMsg warn',
                message: this.$t('uploadSuccess')
              })
              resolve({
                result: true,
                message: res.data.message
              })
            } else {
              resolve({
                result: false,
                message: res.data.message
              })
            }
          })
          .catch(() => {
            resolve({
              result: false,
              message: this.$t('uploadError')
            })
          })
      })
    },

    beforeAvatarUpload (file) {
      console.log(file)
      let isJPG = false
      let isLt2M = false
      const type = file.name.split('.')
      if (this.imgDialogType === 1 || this.imgDialogType === 2) {
        if (type.length < 2) {
          this.$message.error(this.$t('labelModel.上传图例只能JPG/PNG格式'))
          return
        } else {
          const t = type[1].toLowerCase()
          isJPG = t === 'jpg' || t === 'png' || t === 'jpeg'
        }
        if (!isJPG) {
          this.$message.error(this.$t('labelModel.上传图例只能JPG/PNG格式'))
        }
      } else if (this.imgDialogType === 3 || this.imgDialogType === 4) {
        if (type.length < 2) {
          this.$message.error(this.$t('labelModel.上传图例只能dwg格式'))
          return
        } else {
          const t = type[1].toLowerCase()
          isJPG = t === 'dwg'
        }
        if (!isJPG) {
          this.$message.error(this.$t('labelModel.上传图例只能dwg格式'))
        }
      }
      isLt2M = file.size / 1024 <= 500
      if (!isLt2M) {
        this.$message.error(this.$t('labelModel.图例不能超过500k'))
      }
      return isJPG && isLt2M
    },

    create_UuidCode () {
      const len = 16
      let radix = 16
      var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
      var uuid = []; var i
      radix = radix || chars.length

      if (len) {
        // Compact form
        for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
      } else {
        var r
        uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
        uuid[14] = '4'

        for (i = 0; i < 36; i++) {
          if (!uuid[i]) {
            r = 0 | (Math.random() * 16)
            uuid[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r]
          }
        }
      }

      return uuid.join('')
    },
    add_PropertyName () {
      const name = this.create_UuidCode()
      if (this.propertiesList.length === 0) {
        this.formData.showColumn = name
      }
      this.$set(this.propertiesList, this.propertiesList.length, {
        propertyName: name
      })
      this.$set(this.formData, name, '')
    },
    delete_Property (row) {
      this.propertiesList.forEach((ele, idx) => {
        if (ele.propertyName === row.propertyName) {
          this.propertiesList.splice(idx, 1)
          this.$delete(this.formData, row.propertyName)
          if (this.formData.showColumn === row.propertyName) {
            this.formData.showColumn =
              this.propertiesList.length > 0
                ? this.propertiesList[0].propertyName
                : ''
          }
        }
      })
    },
    select_Radio (row) {
      this.showColumn = row.propertyName
    },
    img_DialogOpened () {},
    img_DialogClosed () {},
    open_SelectImgDialog (type) {
      this.imgDialogType = type
      this.imgDialog = true
    }
  }
}
</script>

<style lang="scss" scoped>
.add-or-edit-label {
  font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei;
  ::v-deep.el-dialog {
    border-radius: 4px;
    .el-dialog__header {
      border-radius: 4px 4px 0 0;
    }
  }
  .customer-item {
    ::v-deep.el-form-item__content {
      &::before {
        content: "" !important;
      }
    }
  }
  .uploadable {
    padding-top: 8px;
    display: flex;
    flex-direction: column;

    ::v-deep.el-switch {
      .el-switch__core {
        width: 48px !important;
        height: 24px;
        border: 1px solid;
        border-color: #89919a !important;
        border-radius: 12px;
        &::after {
          top: 2px;
          left: 4px;
          border: 1px solid;
          border-color: #89919a !important;
        }
      }
    }
    ::v-deep.el-switch.is-checked {
      .el-switch__core {
        border-color: #0854a0 !important;
        &::after {
          top: 2px !important;
          left: 42px !important;
          border: 1px solid;
          border-color: #0854a0 !important;
          background: #0854a0;
        }
      }
    }
    ::v-deep.el-switch__label.is-active {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #5d697a;
    }
    ::v-deep.el-radio__inner {
      border-color: #89919a !important;
      background: #fff !important;
      &::after {
        width: 8px;
        height: 8px;
        background-color: #0854a1;
      }
    }
    ::v-deep.is-checked {
      .el-radio__label {
        color: #0854a1;
      }
    }
    ::v-deep.el-radio__label {
      color: #182a4e;
    }
    .img-label {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #94999d;
      line-height: 17px;
      margin-top: 16px;
    }
    .el-radio-group {
      display: flex;
      align-items: center;
    }
  }
  .empty-tips {
    width: 258px;
    height: 32px;
    line-height: 32px;
    background: #f5faff;
    border-radius: 4px;
    border: 1px solid rgba(8, 84, 161, 0.45);
    padding: 0 16px;
    margin-top: 10px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei;
    font-weight: 400;
    color: #182a4e;
  }
  .propetory-content {
    width: 112px;
    display: flex;
    flex-direction: column;
    .el-button {
      &.el-button--secondary--button {
        &:focus {
          background: #fff;
          border-color: #0854a1;
        }
        &:hover {
          background: #ebf5fe;
        }
        &:active {
          color: #fff;
          background: #114171;
          border-color: #114171;
        }
      }
    }
  }
  ::v-deep.el-form-item__label {
    &::before {
      display: none;
    }
  }
  ::v-deep.el-form-item__content {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    &::before {
      content: "*";
      color: #bb0000;
      margin-right: 8px;
      line-height: 38px;
    }
  }
  ::v-deep.el-form-item {
    padding: 0 8px;
    margin-bottom: 0;
    .el-form-item__error {
      margin-left: 16px;
    }
    .el-form-item__label {
      padding: 0 0 2px 0;
      .el-tooltip {
        margin-right: 4px;
      }
      .el-tooltip {
        vertical-align: middle;
      }
    }
  }
  .label-img-upload {
    display: flex;
    flex-direction: column;
    &:last-child {
      margin-bottom: 0;
    }
    .img-label {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei;
      font-weight: 400;
      color: #94999d;
      line-height: 20px;
      margin-bottom: 8px;
      margin-top: 5px;
    }
    .img-tips {
      font-size: 12px;
      align-self: flex-end;
      margin-left: 10px;
    }
  }
  .tips-btn {
    border: none;
    background: rgba(0, 0, 0, 0);
    padding: 0;
  }
  .item-splite {
    height: 1px;
    background: #d9d9d9;
    margin: 16px 0 9px 0;
  }

  .property-form-item {
    ::v-deep.el-form-item__content {
      &::before {
        margin-right: 15px;
      }
    }
  }

  .property-list {
    width: 84%;
    margin-left: auto;
    max-height: 40px;
    overflow-y: auto;
    .property-list-item {
      margin-bottom: 8px;
      height: 54px;
      border-radius: 4px;
      position: relative;
      .item-radio {
        position: absolute;
        top: 20px;
        left: 15px;
        ::v-deep.el-radio__inner {
          border-color: #89919a;
          background: #fff;
          &::after {
            width: 8px;
            height: 8px;
            background-color: #0854a1;
          }
        }
        ::v-deep.el-radio__label {
          display: none;
        }
      }
      ::v-deep.el-form-item {
        position: absolute;
        top: 14px;
        left: 41px;
        padding: 0;
        right: 37px;
        .el-form-item__content {
          &::before {
            content: "";
          }
          .el-input {
            width: 92%;
          }
        }
        .el-input {
          .el-input__inner {
            border-color: #89919a;
          }
        }
      }

      .item-delete {
        position: absolute;
        top: 22px;
        right: 12px;
        .el-icon-delete {
          &::before {
            width: 20px;
            height: 20px;
            border-radius: 2px;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          &:hover {
            &::before {
              background-color: #ebf5fe;
              outline: 1px solid #0854a1;
            }
          }
          &:active {
            &::before {
              color: #ffffff;
              background-color: #114171;
              outline: 1px solid #114171;
            }
          }
        }
      }
    }
  }

  ::v-deep.el-table {
    .el-table__header {
      .has-gutter {
        tr {
          th:nth-child(1) {
            border-right: none;
          }
          th {
            .cell {
              padding-left: 0;
            }
          }
        }
      }
    }
    .el-table__body-wrapper {
      .el-table__body {
        .el-table__row {
          td:nth-child(1) {
            padding-left: 0;
            padding-right: 0;
            border-right: none !important;
            .cell {
              display: flex;
            }
          }
        }
      }
    }
    .el-radio__label {
      display: none;
    }
  }
  .img-group {
    display: flex;
    flex-direction: row;
    margin-bottom: 16px;
    .select-img {
      display: flex;
      flex-direction: column;
      .img-wraper-out {
        display: flex;
        flex-direction: row;
        margin-right: 24px;
      }
    }
  }

  .img-wraper {
    width: 48px;
    height: 48px;
    cursor: pointer;
    position: relative;
    margin-right: 16px !important;
    .hover-img {
      width: 50px;
      height: 50px;
      position: relative;
      padding: 4px;
      border: 1px solid #e5e5e5;
      border-radius: 2px;
      &:hover {
        .avatar-shadow {
          opacity: 1;
        }
      }
    }
    .avatar-shadow {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 9;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      opacity: 0;
      i {
        font-size: 16px;
        color: #fff;
        cursor: pointer;
        &:first-child {
          margin-right: 6px;
        }
      }
    }
    .img-description {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei;
      font-weight: 400;
      color: #182a4e;
      line-height: 14px;
      margin-top: 4px;
      width: 50px;
      left: 0;
      bottom: -20px;
    }
    &:first-child {
      margin-right: 8px;
    }
    .img-type {
      position: absolute;
      left: 0;
      top: 0;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei;
      font-weight: 400;
      color: #5d697a;
      line-height: 20px;
    }
    .avatar-uploader-icon {
      font-size: 16px;
      color: #8c939d;
      width: 48px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      border: 1px solid #0854a1;
      border-radius: 2px;
    }
    img {
      width: 100%;
      height: 100%;
    }
    svg {
      width: 100%;
      height: 100%;
    }
    &:hover {
      border-color: #409eff;
      background-color: rgba(8, 84, 161, 0.08);
    }
  }
}
.inner-dialog {
  position: relative;
  .img-recommend {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei;
    font-weight: 400;
    color: #182a4e;
    line-height: 22px;
  }
  .default-img-list {
    min-width: 346px;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    &::after {
      content: "";
      flex: auto;
    }
    .img-item {
      width: 50px;
      height: 50px;
      padding: 4px;
      cursor: pointer;
      margin: 0 16px 16px 0;
      border-radius: 2px;
      border: 1px solid #e5e5e5;
      &:nth-child(5n) {
        margin-right: 0;
      }
      &:last-child {
        margin: 0;
      }
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .img-select-footer {
    padding-top: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .upload-btn {
      border: none;
      &:hover {
        background: none;
      }
    }
  }
}
.dilalog-footer-split {
  width: 346px;
  height: 1px;
  background: #d9d9d9;
  position: absolute;
  left: 0;
}
</style>

<style lang="scss">
.inner-dialog {
  border-radius: 4px;
  overflow: hidden;
  .el-dialog__body {
    padding: 16px;
  }
}
.add-or-edit-label {
  .el-dialog__body {
    padding: 24px 16px;
  }
  .el-input {
    .el-input__inner {
      &:-moz-placeholder {
        color: #94999d;
        font-family: PingFangSC-Medium, PingFang SC, Microsoft YaHei;
      }
      &:-ms-input-placeholder {
        color: #94999d;
        font-family: PingFangSC-Medium, PingFang SC, Microsoft YaHei;
      }
      &::-moz-placeholder {
        color: #94999d;
        font-family: PingFangSC-Medium, PingFang SC, Microsoft YaHei;
      }
      &::-webkit-input-placeholder {
        color: #94999d;
        font-family: PingFangSC-Medium, PingFang SC, Microsoft YaHei;
      }
    }
  }
  .el-icon-plus {
    &.avatar-uploader-icon {
      &:before {
        width: 16px;
        height: 16px;
        font-family: "onehit-icon" !important;
        content: "\e64c";
        color: #0854a1;
        vertical-align: -2px;
      }
    }
  }
}
.el-tooltip__popper {
  &.is-light {
    &.labelManager-tooltip {
      border: none;
      box-shadow: 0px 10px 30px 2px rgba(0, 0, 0, 0.12),
        0px 0px 0px 1px rgba(0, 0, 0, 0.12);
      max-width: 50% !important;
      .popper__arrow {
        border-top-color: rgba(0, 0, 0, 0.12);
      }
    }
  }
}
</style>
