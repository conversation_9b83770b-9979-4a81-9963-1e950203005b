<template>
  <div>
    <div class="object-group-tool-button">
      <el-input v-model.trim="searchText" style="width: 240px;float: right" :placeholder="$t('搜索')" prefix-icon="el-icon-search" size="small" clearable>
      </el-input>
    </div>
    <div :style="{ height: tableIsLoading ? parseInt(tableMaxHeight) + 'px' : 'fit-content'}" style="position: relative">
      <table-loading :table-height="tableMaxHeight" v-show="tableIsLoading" />
      <el-table
        stripe
        border
        highlight-current-row
        row-key="id"
        :data="searchText ? objectGroupings.filter(item => item.groupName.toLowerCase().includes(searchText.toLowerCase()) || item.categories.toLowerCase().includes(searchText.toLowerCase())) : objectGroupings"
        :default-expand-all="true"
        :max-height="tableMaxHeight"
        :row-class-name="rowClassName"
        :header-cell-style="{ background: '#EFEFEF' }">
        <el-table-column show-overflow-tooltip :label="$t('组名')" min-width="3" width="200">
          <template slot-scope="{ row }">
            <span v-html="filterHighLight(row.groupName)"></span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip :label="$t('对象分类')" min-width="3">
          <template slot-scope="{ row }">
            <span v-html="filterHighLight(row.categories ? row.categories.replaceAll(',', '，') : '')"></span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('control')" width="145">
          <template slot-scope="{ row }">
            <el-link :underline="false" type="primary" @click="setingCategories(row)">{{ $t('设置对象分类') }}</el-link>
          </template>
        </el-table-column>
        <template slot="empty">
          <table-empty :table-height="tableMaxHeight" v-show="!tableIsLoading" :description="$t('noData')"/>
        </template>
      </el-table>
        <edit-object-grouping
        ref="editObjectGrouping"
        v-if="editObjectGrouping"
        @edited="loadObjectGroupings"
        @handleClose="editObjectGrouping=false">
    </edit-object-grouping>
    </div>
  </div>
</template>
<script>
import ErrorHandler from '@/common/ErrorHandler'
import { objectGroupingApi } from '@/api/objectGrouping'
import TableLoading from '@/components/base/TableLoading.vue'
import EditObjectGrouping from './EditObjectGrouping.vue'
import TableEmpty from '@/components/base/TableEmpty.vue'
import TextTool from '@/utils/TextTool'

export default {
  name: 'object-grouping-seting',
  components: { TableLoading, TableEmpty, EditObjectGrouping },
  data () {
    return {
      searchText: '',
      objectGroupings: [],
      tableIsLoading: true,
      editObjectGrouping: false
    }
  },
  computed: {
    tableMaxHeight () {
      return window.innerHeight - 50 - 44 - 32
    },
    isPause () {
      return this.$root.isPause
    }
  },
  watch: {
    isPause (val) {
      if (!val) {
        this.loadObjectGroupings()
      }
    }
  },
  mounted () {
    this.loadObjectGroupings()
  },
  methods: {
    filterHighLight (value) {
      return TextTool.highlightFilters(value, this.searchText)
    },
    setingCategories (row) {
      this.editObjectGrouping = true
      this.$nextTick(() => {
        this.$refs.editObjectGrouping.openDialog(this.$t('设置对象分类'), row)
      })
    },
    loadObjectGroupings () {
      this.tableIsLoading = true
      objectGroupingApi.getALL().then((response) => {
        this.tableIsLoading = false
        this.objectGroupings = response.data
      }).catch((err) => {
        this.tableIsLoading = err.message === 'cancel'
        ErrorHandler.formatError(err)
      })
    },
    rowClassName ({ row }) {
      return 'rowId' + row.id
    }
  }
}
</script>

<style scoped lang="scss">
  .object-group-tool-button{
    height: 32px;
    margin-bottom: 16px;
  }
</style>
