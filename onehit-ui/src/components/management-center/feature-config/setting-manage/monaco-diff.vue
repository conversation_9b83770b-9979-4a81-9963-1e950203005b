<template>
    <div class="monaco-container">
        <div ref="container" class="monaco-editor" style="height:750px;width:100%"></div>
    </div>
</template>

<script>
// import * as monaco from 'monaco-editor';
export default {
    name: 'MonacoDiff',
    data(){
        return{
            prevCodes: "",
            currentCodes: "",
            editorOptions: {
                originalEditable:true,
                selectOnLineNumbers: true,
                roundedSelection: false,
                readOnly: false,
                cursorStyle: 'line',
                automaticLayout: true,
                glyphMargin:true,// 字形边缘
                useTabStops:false,
                scrollBeyondLastLine: false,
                wordWrap: 'on',
                wordWrapColumn: 80,
                wrappingIndent: 'indent',
                autoIndent: true,
                formatOnPaste: true // 复制粘贴时格式化
            },
            theme: 'CodeSampleTheme',
            readOnly: false
        }
    },
    mounted() {
        this.monacoEditor=monaco.editor.createDiffEditor(this.$refs.container,{
            language:'json',
            theme:this.theme,
            automaticLayout:true,
            diffCodeLens: true,
            readOnly:"true",
            ...this.editorOptions
        })
    },
    beforeDestroy() {
        if(this.monacoEditor){
            this.monacoEditor.dispose();
        }
    },
    methods:{
        setValues(prevCodes, currentCodes){
            if(this.monacoEditor){
                const originModel=monaco.editor.createModel(prevCodes,'json');
                this.monacoEditor.getModifiedEditor().updateOptions({
                    readOnly: false
                });
                console.log(this.monacoEditor)
                const modifyModel=monaco.editor.createModel(currentCodes,'json');
                this.monacoEditor.setModel({
                    original:originModel,
                    modified:modifyModel
                })
                setTimeout(()=>{
                    this.monacoEditor.getOriginalEditor().getAction('editor.action.formatDocument').run();
                    this.monacoEditor.getModifiedEditor().getAction('editor.action.formatDocument').run().then(()=>{
                        this.monacoEditor.getModifiedEditor().updateOptions({
                            readOnly: true
                        });
                    });

                },1000)
            }
        }
    }
}
</script>

<style>
.monaco-container{
    border: 1px solid #ddd;
}
</style>
