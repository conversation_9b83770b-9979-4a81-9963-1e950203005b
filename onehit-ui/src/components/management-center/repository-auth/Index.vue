<template>
  <div id="repositoryAuth">
    <iframe id="repositoryAuth-iframe" src="" frameborder="0"></iframe>
  </div>
</template>

<script>
export default {
  name: 'RepositoryAuth',
  data () {
    return {}
  },
  computed: {
    isPause () {
      return this.$root.isPause
    }
  },
  watch: {
    isPause (val) {
      if (!val) {
        const iframe = document.getElementById('repositoryAuth-iframe')
        if (iframe) {
          iframe.contentWindow.location.reload(true)
          iframe.contentWindow.removeEventListener('click', this.listenEvent)
          this.init()
        }
      }
    }
  },
  mounted () {
    const iframe = document.getElementById('repositoryAuth-iframe')
    iframe.src = window.location.origin + '/AIMS/#/repositoryPrivilege?skipPrivilegeCheck=true'
    this.listenEvent = () => {
      window.top.document.querySelector('body').click()
      window.top.document.querySelector('#pc-dropdown-void').click()
    }
    this.init()
  },
  methods: {
    init () {
      const _this = this
      const url = window.location.origin + '/AIMS/#/repositoryPrivilege?skipPrivilegeCheck=true'
      const iframe = document.getElementById('repositoryAuth-iframe')
      iframe.onload = function () {
        const aside = iframe.contentWindow.document.getElementsByClassName('el-aside')
        const header = iframe.contentWindow.document.getElementsByClassName('el-header')
        aside && aside[0].parentNode.removeChild(aside[0])
        header && header[0].parentNode.removeChild(header[0])
        const timer = setInterval(() => {
          if (iframe.contentWindow.location.href !== url) {
            iframe.contentWindow.location.href = url
            clearInterval(timer)
          }
        }, 50)

        iframe.contentWindow.addEventListener('click', _this.listenEvent)
      }
    }
  }
}
</script>

<style scoped lang="scss">
#repositoryAuth, #repositoryAuth iframe {
  width: 100%;
  height: 100%;
}
</style>
