<template>
    <div class="displayModel">
        <div class="displayModel_list" v-show="isShowTable" :style="{width:width+'px',height:height+'px'}">
            <table-toolbar :filterLength="selectFilterData.length">
                <template v-slot:search>
                    <el-input
                    ref="inputOutSearch"
                    :placeholder="$t('equipmentManagement.searchPlaceholder')"
                    prefix-icon="el-icon-search"
                    size="small"
                    @input="search"
                    v-model.trim="searchText"
                    clearable>
                    </el-input>
                </template>
                <template v-slot:filter>
                    <select-filter-group
                    ref="selectFilterGroup"
                    :select-filter-data="selectFilterData"
                    @changeChecked="handleFilterChange">
                    </select-filter-group>
                </template>
            </table-toolbar>
            <div class="displayModel_table" >
                <el-table
                    :data="tableData"
                    stripe
                    border
                    highlight-current-row
                    scenatorStyle="indexType"
                    style="width: 100%;height:100%"
                    @cell-click ="cellClickEvent"
                    :max-height="tableData.length > 0 ? pageHeight + 'px' : pageHeight + 48 +'px'"
                    :header-cell-style="{ background: '#EFEFEF', color: '#182A4E' }">
                    <el-table-column
                        prop="device"
                        :label="$t('equipmentManagement.deviceName')"
                        show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column
                        prop="categoryName"
                        :label="$t('equipmentManagement.objectCategoryName')"
                        show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column
                        :label="$t('equipmentManagement.tagNumber')"
                        show-overflow-tooltip>
                        <template v-slot="{ row }">
                            <span v-html="filterHighLight(row.numbering)"></span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        :label="$t('equipmentManagement.purpose')"
                        show-overflow-tooltip>
                        <template v-slot="{ row }">
                            <span v-html="filterHighLight(row.usage)"></span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        :label="$t('equipmentManagement.operation')">
                        <template slot-scope="scope">
                            <el-link 
                                @click="handleClick(scope.row)" 
                                :underline="false" 
                                class="scenatorStyle">{{ $t('equipmentManagement.view') }}
                            </el-link>
                        </template>
                    </el-table-column>
                    <template slot="empty">
                        <table-empty :table-height="pageHeight+ 48 + 'px'" v-show="isShowTable && tableData.length === 0"/>
                    </template>
                </el-table>
            </div>
            <div class="displayModel_content_pagination"  v-show="tableData.length > 0">
                    <el-pagination
                        small
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-size.sync="pageSize"
                        :page-sizes="pageSizes"
                        layout="sizes, prev, pager, next, jumper, ->, total"
                        :total="total">
                    </el-pagination>
            </div>
        </div>
        <div v-show="!isShowTable">
            <div id="title"  class="go_back" :style="{width:width+'px'}">
                <div class="go_back_btn" @click="goBack">
                    <i class="edc-icon-left" style="margin-right: 8px;vertical-align: bottom;color: #0854A1"></i>{{ $t('equipmentManagement.back') }}
                </div>
            </div>
            <div id="modelScene_model" :style="{top:height-60 + 'px'}">
                <div class="model_tools"  @mouseout="toolMouseOut" :style="{marginLeft:width/2 - 154 +'px'}">
                    <div class="toolText" v-show="toolText" ref="toolText">{{hoverText}}</div>
                    <div 
                        class="viewPoint_image" 
                        ref="viewPoint_image" 
                        @click="viewPoint" 
                        @mouseover="toolMouseOver('视角')"></div>
                    <div 
                        class="saveViewPoint_image" 
                        ref="saveViewPoint_image" 
                        @click="saveViewPoint" 
                        @mouseover="toolMouseOver('保存视角')"></div>
                    <div 
                        class="transparent_image" 
                        ref="transparent_image" 
                        @click="transparent" 
                        @mouseover="toolMouseOver('透明')" ></div>
                    <div 
                        class="cancelTransparent_image"  
                        ref="cancelTransparent_image" 
                        @click="cancelTransparent" 
                        @mouseover="toolMouseOver('取消透明')">
                    </div>
                    <div 
                        class="section_image" 
                        ref="section_image"  
                        @click="section" 
                        @mouseover="toolMouseOver('剖切')" ></div>
                    <div 
                        class="hide_image" 
                        ref="hide_image"  
                        @click="hide" 
                        @mouseover="toolMouseOver('隐藏')" ></div>
                    <div 
                        class="cancelHide_image" 
                        ref="cancelHide_image"  
                        @click="cancelHide" 
                        @mouseover="toolMouseOver('取消隐藏')"></div>
                    <div
                        class="cancel_viewPoint_move"
                        ref="viewPoint_move"
                        @click="moveModel"
                        @mouseover="toolMouseOver('移动')"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {sceneManager,modelManager} from "finger"
import Matrix from "gl-matrix-double"
import $ from "jquery"
import _ from "lodash"

import { Api } from '@/api/index.js';
import TextTool from '@/utils/TextTool'
import SelectFilterGroup from '../base/SelectFilterGroup.vue'
import TableToolbar from '../base/TableToolbar.vue'
import TableEmpty from '../base/TableEmpty'
import VicManager from "../../app/equipmentManagement/utility/VicManager"
import {message,messageBox} from "../../app/equipmentManagement/utility/MessageCom"
import 	SectioningEffect from "../../app/equipmentManagement/utility/SectioningEffect"
import {draggerFactory} from "../../app/equipmentManagement/utility/DraggerFactory"
import MoveNodeModel from "../../app/equipmentManagement/utility/MoveNodeModel"

export default {
    components: {
       SelectFilterGroup, TableToolbar, TableEmpty
    },
    data () {
        return {
            tableData:[],
            pageHeight:"auto",
            isShowTable:true,
            width:1920,
            height:1010,
            view3D:null,
            vicManager:null,
            toolText:false,
            toolSelected: "",
            hoverText: "",
            sectioningEffect:null,
            searchText:"",
            oldInteration:null,
            moveNodeMap:new Map(), // 记录所有移动过得模型,
            pageNum:1,
            pageSizes : [10, 30, 50, 100],
            pageSize:30,
            total:0,
            pageCount:0,
            currentPage:1,
            viewPointInfos:null,
            repoModel: null,
            drawingRoot: null,
            detachNodes:[],
            selectFilterData: [
                {
                    title: this.$t('equipmentManagement.deviceName'),
                    paramKey: 'device',
                    checkboxData: []
                },
                {
                    title: this.$t('equipmentManagement.deviceCategory'),
                    paramKey: 'category',
                    checkboxData: []
                }
            ],
            sourceRepositoryIds: [],
            refinedRepositoies: [],
            config: null
        }
    },
    created(){
        this.init()
    },
    async mounted() {
        this.vicManager = new VicManager()
        const scene = sceneManager.getCurrentScene()

        this.width = scene.$content[0].offsetWidth
        this.height = scene.$content[0].offsetHeight
        this.pageHeight = scene.$content[0].offsetHeight - 200

        // 检测页面高度变化，从而调整el-table高度，做到高度自适应
        // 确保使用未绑定的函数，以获取最新的 this 指向
        window.addEventListener('resize', () => {
                setTimeout(()=>{
                    this.width =  scene.$content[0].offsetWidth
                    this.height = scene.$content[0].offsetHeight
                    this.pageHeight =  scene.$content[0].offsetHeight - 200
                },100)
        })
        $("#sceneFlex").on("click", () => {
            setTimeout(()=>{
                this.width =  scene.$content[0].offsetWidth
            },100)
        })
        this.sectioningEffect = new SectioningEffect(this.vicManager)
        this.debounce = _.debounce(this.getTableList, 500)

        sceneManager.watchCurrentScene(async (currentScene) => {
			if (currentScene.name === this.config.data.common.refinedModelManagementSceneName && !this.isShowTable) {
				this.goBack()
			}
		});
    },
    methods: {
        goBack(){
            this.isShowTable = true
            const currentScene = sceneManager.getCurrentScene()
            const views = currentScene.getViews()
            const navigatorView = views.find(view=>view.getType()==="NavigatorView")
            navigatorView.removeModels()
            const r3dView = views.find(view=>view.getType()==="Renderer3DView")
            r3dView.removeModels()
            this.viewPointInfos = null
            this.closeTools()
            this.width =  currentScene.$content[0].offsetWidth
            this.height = currentScene.$content[0].offsetHeight
        },
        async handleClick(row){
            $("#equipmentModel_Table").css({width:"auto",height:"auto"})
            
            this.repoModel = await this.getRepoModels(row.repositoryId)
            this.repoModel.setModelType("SUPPLIER_MODEL");
            this.repoModel.setDocumentId(row.documentId);
            this.isShowTable = false

            const scene = sceneManager.getCurrentScene()
            const views = scene.getViews()
            const navigatorView = views.find(view=>view.getType()==="NavigatorView")
            this.r3dView = views.find(view=>view.getType()==="Renderer3DView")

            // 组织节点原始结构导航
            await navigatorView.addModel(this.repoModel, {templateName : "原始结构"})
            // 渲染
            await this.r3dView.addModel(this.repoModel, {modelType : "PGF"})

            this.view3D = this.r3dView.vicWrapper.view3D
            this.vicManager.view3D = this.view3D 
            this.vicWrapper = this.r3dView.vicWrapper

            await this.getViewPoint()
            
            this.drawingRoot = this.r3dView.viewModelManager.viewModels[0].drawingRoot
        },
        async getViewPoint(){
            const documentId = this.repoModel.getDocumentId()
            if(this.viewPointInfos){
                this.vicManager.getInitialViewpoint(0, this.view3D, this.viewPointInfos, this.vicWrapper)
                return
            }
            const res = await Api.getBaseLabel(documentId)
            if(res.data.length > 0){
                const result = JSON.parse(res.data[0].contents)
                this.viewPointInfos = result 
                this.defaultViewpointId = res.data[0].id
                this.vicManager.getInitialViewpoint(0, this.view3D, result, this.vicWrapper)
            }else{
                this.vicManager.getInitialViewpoint(0, this.view3D)

            }
        },
        async getRepoModels(repoId){      
            const repoModel =await modelManager.request("RepoModel",{id: repoId})
            return repoModel
        },
        async init(){
            // 从AIMS获取有权限的仓库
            const result = await Api.getRepositories()
            const repositories = result.data
            const repositoryNames = _.map(repositories, "name")
           //  获取有精细化模型的仓库
            this.refinedRepositoies = [],  this.sourceRepositoryIds = []
            _.forEach(repositories, repositry => {
                if (repositry.repositoryType === "DETAIL_MODEL") {
                    this.sourceRepositoryIds.push(repositry.id)
                    const sources = _.filter(repositories, {id : repositry.parentId})
                   
                    const deviceName = sources[0].name
                    repositry.sources = sources
                    repositry.device = deviceName 
                    this.refinedRepositoies.push(repositry)
                    this.selectFilterData[0].checkboxData.push({value: repositry.id, text: deviceName, labelText: deviceName})
                }
            })
            if (this.sourceRepositoryIds.length > 0) {
                // 从EDC分页查询设备列表（条件：项目空间名称(集合)（精确）、设备分类（字符串）（精确）、位号（字符串）（模糊）、用途（字符串）（模糊））
                this.getTableList();

                // 从EDC获取设备分类信息
                const categories = await Api.selectObjectModelCategories(this.sourceRepositoryIds)
                if(categories.status===200){
                    categories.data.forEach(c => {
                        this.selectFilterData[1].checkboxData.push({value: c.number, text: c.name, labelText: c.name});
                    })
                }  
            }

            // 获取基础配置
            this.config = await Api.getCommonConfig();
        },
        async getTableList(){
            if (this.sourceRepositoryIds.length === 0) {
                return
            }
            const condition = {
                pageNum : this.pageNum,
                pageSize : this.pageSize,
                repositories : this.sourceRepositoryIds
            }
            if (this.categoryNumbers && this.categoryNumbers.length > 0) {
                condition.categoryNumbers = this.categoryNumbers
            }
            if (this.searchText.trim() && this.searchText.trim().length > 0) {
                condition.keyWord = this.searchText.trim()
            }
            const res = await Api.selectObjectModelList(condition)
            if(res.status===200){
                 res.data.results.forEach(r => {
                    r.device = this.refinedRepositoies.find(equiment => r.repositoryId === equiment.id).device
                 })

                this.tableData = res.data.results
                this.total = res.data.total
                this.pageSize = res.data.pageSize
                this.pageCount = res.data.pages;
                this.currentPage = res.data.pageNum
            }
        },

        // 页码改变
        inputChange(value){
            
            this.handleCurrentChange(value)
        },
        handleSizeChange(newSize){
            this.pageSize = newSize
            this.getTableList()
        },
        handleCurrentChange(value){
            this.pageNum = value
            this.getTableList()
        },

        // 筛选条件变化
        handleFilterChange (allFilter, title) {
            this.sourceRepositoryIds = []
            if (allFilter[0].checkedValues.length > 0) {
                this.refinedRepositoies.forEach(repo => {
                    if (allFilter[0].checkedValues.indexOf(repo.id) > -1) {
                        this.sourceRepositoryIds.push(repo.id)
                    }
                })
            } else {
                this.sourceRepositoryIds = _.map(this.refinedRepositoies, "id")
            }
            if (allFilter[1].checkedValues.length > 0) {
                this.categoryNumbers = allFilter[1].checkedValues
            } else {
                this.categoryNumbers = null;
            }
            this.pageNum = 1
            this.debounce()
        },
        search () {
            this.pageNum = 1
            this.debounce()
        },
        filterHighLight (value) {
            return TextTool.highlightFilters(value, this.searchText.trim())
        },
        /**
         * 左键点击回调
         *
         */
        async leftPointPickCallback(pickNode, msg) {
            if (pickNode === undefined || pickNode === null) {
                return;
            }
            this.currentPickNode = pickNode
            // this.vicManager.highLight(pickNode)
             // 剖切
            if (_.isObject(this.vicManager.sectioningObject)) {
                this.sectioningEffect.startSectioning(
                    null,
                    this.vicManager.view3D.rootNode,
                    this.vicManager.sectioningObject,
                    pickNode,
                    msg
                );
            }
            // 隐藏
            if (this.vicManager.hideIsStart) {
                this.vicManager.creatHideEffect(
                    pickNode,
                );
                
            }
            //透明
            if (this.vicManager.transparentIsStart) {
                this.vicManager.addTransparency(pickNode);
                this.vicManager.isTransparentNode = true;
            }
            // 拖拽
            if(this.vicManager.isMoveModel){
                // 先清除之前的拖拽器
                draggerFactory.clearDragger(this.view3D);
                // 获取中心点,top,bottom坐标
                const result = await this.vicManager.calcAABBCenterPos([pickNode])

                let originPoint = JSON.parse(JSON.stringify(result.center))
                let this_ = this
                // 点击新的模型
                if(!this.moveNodeMap.has(pickNode.name)){
                    this.moveNodeMap.set(pickNode.name,new MoveNodeModel(originPoint,pickNode))
                }
                this.vicManager.currentMoveNode = this.moveNodeMap.get(pickNode.name)
                console.log(this.moveNodeMap,"this.moveNodeMap");
                let pio = []
                // 创建拖拽器 draggerFactory
                draggerFactory.currentDragger = draggerFactory.createDragger(originPoint,this.view3D,async function (point){
                    let currentPointList = this_.vicManager.currentMoveNode.pointList
                    if(!this_.vicManager.draggerIndent){
                        this_.vicManager.draggerIndent = true;
                        if(currentPointList.length !== 1){ // 判断有没有移动过，移动过把数组最后一个复制给第一个并且清空剩下的
                            currentPointList[0] = currentPointList[currentPointList.length-1];
                            currentPointList = currentPointList.slice(0,1);
                        }
                    }
                    currentPointList.push(point);
                })
            }
        },

        /**
         * 视角定位
         */
        viewPoint(){
            this.getViewPoint()
        },
        toolMouseOut(){
            this.toolText = false
        },
        toolMouseOver(val){
            this.toolText = true
            this.hoverText = this.$t('equipmentManagement.' + val)
            const textDom = this.$refs.toolText
            switch (val) {
                case '视角':
                    textDom.style.top= '-70%'
                    textDom.style.left= '0'
                    textDom.style.width= '44px'
                    break;
                case '保存视角':
                    textDom.style.top= '-70%'
                    textDom.style.left= 'calc(50% - 146px)'
                    textDom.style.width= '72px'
                    break;
            
                case '透明':
                    textDom.style.top= '-70%'
                    textDom.style.left= 'calc(50% - 88px)'
                    textDom.style.width= '44px'
                    break;
            
                case '取消透明':
                    textDom.style.top= '-70%'
                    textDom.style.left= 'calc(50% - 58px)'
                    textDom.style.width= '72px'
                    break;
            
                case '剖切':
                    textDom.style.top= '-70%'
                    textDom.style.left= '50%'
                    textDom.style.width= '44px'
                    break;
            
                case '隐藏':
                    textDom.style.top= '-70%'
                    textDom.style.left= 'calc(50% + 44px)'
                    textDom.style.width= '44px'
                    break;
                case '取消隐藏':
                    textDom.style.top= '-70%'
                    textDom.style.left= 'calc(50% + 74px)'
                    textDom.style.width= '72px'
                    break;
            
                default:
                    textDom.style.top= '-70%'
                    textDom.style.left= 'calc(50% + 134px)'
                    textDom.style.width= '44px'
                    break;
            }
        },
        async saveViewPoint(){
            if(this.toolSelected && this.toolSelected !== "保存视角"){
                message('warn',this.$t('请先关闭其他功能'));
                return
            }
            const viewPoint = await new Promise((resolve, reject) => {
                this.view3D.acquireViewpoint(function (err, viewPoint) {
                    resolve(viewPoint);
                });
            })
            const content = {
                eye: viewPoint.eye,
                center: viewPoint.center,
                up: viewPoint.up
            }
            const documentId = this.repoModel.getDocumentId()
            const res = await Api.saveBaseLabel({
                type : documentId,
                contents : JSON.stringify(content)
            })
            if(res.status===200){
                message("success", this.$t("保存成功"))
                if (this.defaultViewpointId) {
                    Api.deleteBaseLabel([this.defaultViewpointId])
                    this.defaultViewpointId = res.data
                }
                this.viewPointInfos = content 
            }else{
                message("error", this.$t("保存失败"))

            }
        },
        /**
         * 透明
         */
        async transparent(){
            // this.vicManager.removeColorEffect()
            if(this.toolSelected === '透明'){
                this.$refs.transparent_image.className = "transparent_image"
                this.toolSelected = ''
                this.vicManager.transparentIsStart = false
		        this.vicManager.swapInteraction(this.oldInteration);
            }else if(this.toolSelected === ''){
                this.oldInteration = await this.vicManager.pointPickInteraction(
                    this.leftPointPickCallback,
                    function (pickNode, msg) {},
                    true
                );
                this.$refs.transparent_image.className = "transparent_image_Selected"
                this.toolSelected = '透明'
                this.vicManager.transparentIsStart = true
            }else{
                message('warn', this.$t('请先关闭其他功能'));
                return
            }
        },
        /**
         * 取消透明
         */
        cancelTransparent(){
            if(this.toolSelected === "透明"){
                this.vicManager.removeColorEffect();
                this.vicManager.transparentIsStart = false;
                this.$refs.transparent_image.className = "transparent_image";
                this.toolSelected = '';
                this.vicManager.removeTransparency();
                this.vicManager.swapInteraction(this.oldInteration);
            }
        },
        /**
         * 剖切
         */
        async section(){
            if(this.toolSelected === '剖切'){
                this.$refs.section_image.className = "section_image";
                this.toolSelected = '';
                this.vicManager.swapInteraction(this.oldInteration);
            }else if(this.toolSelected === ''){
                this.oldInteration = await this.vicManager.pointPickInteraction(
                    this.leftPointPickCallback,
                    function (pickNode, msg) {},
                    true
                );
                this.$refs.section_image.className = "section_image_Selected";
                this.toolSelected = '剖切';
            }else{
                message('warn', this.$t('请先关闭其他功能'));
                return;
            }
            this.isSection = !this.isSection;
            this.vicManager.section(this.isSection,this.sectioningEffect);
        },
        /**
         * 隐藏
         */
        async hide(){
            // this.vicManager.removeColorEffect()
            if(this.toolSelected === '隐藏'){
                this.$refs.hide_image.className = "hide_image";
                this.toolSelected = '';
                this.vicManager.hideIsStart = false;
                this.vicManager.swapInteraction(this.oldInteration);
            }else if(this.toolSelected === ''){
                this.oldInteration = await this.vicManager.pointPickInteraction(
                    this.leftPointPickCallback,
                    function (pickNode, msg) {},
                    true
                );
                this.$refs.hide_image.className = "hide_image_Selected";
                this.toolSelected = '隐藏';
                this.vicManager.hideIsStart = true;
            }else{
                message('warn', this.$t('请先关闭其他功能'));
                return;
            }
        },
        /**
         * 取消隐藏
         */
        cancelHide(){
            if(this.toolSelected === "隐藏"){
                this.vicManager.hideIsStart = false;
                this.$refs.hide_image.className = "hide_image";
                this.toolSelected = '';
                this.vicManager.clearHideEffects();
                this.vicManager.swapInteraction(this.oldInteration);
            }
        },
        /**
         * 移动模型
         */
        async moveModel(){
          if(this.toolSelected === '移动'){
            this.$refs.viewPoint_move.className = "cancel_viewPoint_move";
            this.toolSelected = '';
            this.restMoveModel();// 复原移动
            this.vicManager.isMoveModel = false
          }else if(this.toolSelected === ''){
            this.oldInteration = await this.vicManager.pointPickInteraction(
                this.leftPointPickCallback,
                function (pickNode, msg) {},
                true
            );
            this.$refs.viewPoint_move.className = "viewPoint_move";
            this.toolSelected = '移动';
            this.vicManager.isMoveModel = true
          }else{
            message('warn', this.$t('请先关闭其他功能'));
            return;
          }
        },
        async restMoveModel(close){

            // 关闭场景时需要清除移动造成的效果
            // if(close){
              this.vicManager.isMoveModel = false
              this.toolSelected = '';
              this.$refs.viewPoint_move.className = "cancel_viewPoint_move";
            // }
            await this.vicManager.restMoveModel(this.moveNodeMap)
		    this.vicManager.swapInteraction(this.oldInteration);
            this.moveNodeMap = new Map()
        },
        async closeTools(){
            if(this.toolSelected==="移动"){
                await this.moveModel()
            }else if(this.toolSelected==="透明"){
                this.transparent()
            }else if(this.toolSelected==="隐藏"){
                this.hide()
            }else if(this.toolSelected==="剖切"){
                this.section()
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.displayModel{
    width: 100%;
    height: 100%;
    .displayModel_list{
        width: 100%;
        padding: 20px;
        .displayModel_table{
            margin-top: 5px;
        }
    }
    .model{
        height: 100%;
    }
    #title{
        height: 40px;
       
        line-height: 40px;
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(255, 255, 255, 0.75);
    } 
    #modelScene_model{
        height: 44px;
        position: absolute;
        bottom: 12px;
        .model_tools{
            height: 44px;
            position: absolute;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1;
            background-color: rgba(255, 255, 255, 0.75);
            backdrop-filter: blur(4px);
            .toolText{
                width: 44px;
                height: 28px;
                background: rgba(0, 0, 0, 0.6);
                color:#FFFFFF;
                box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.35);
                border-radius: 2px;
                position: absolute;
                text-align: center;
                line-height: 28px;
                top: -73%;
                left: 0.5%;
            }
        }
    }
}
</style>
<style lang="scss">
    .go_back{
        position: fixed;
        width: 100%;
        line-height: 40px;
        background-color: rgba(255, 255, 255, 0.75);
        backdrop-filter: blur(4px);
        box-sizing: border-box;
        z-index: 5;
        top: 48px;
        .go_back_btn {
            width: fit-content;
            height: 40px;
            line-height: 46px;
            color: #182A4E;
            font-size: 14px;
            cursor: pointer;
            padding: 0 12px;
        }
    }

    .onehit .table-filter-data .el-checkbox {
        margin: 0px 0px; 
    }
</style>