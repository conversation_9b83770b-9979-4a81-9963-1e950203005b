<template>
  <div id="render3dFullScreenApp"></div>
</template>

<script>
export default {
  name: "render3dFullScreen",
  data() {
    return {
      show: true,
    };
  },
  mounted() {},
  methods: {}
}
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  min-height: 100% !important;
}
</style>
<style scoped>
#render3dFullScreenApp {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
