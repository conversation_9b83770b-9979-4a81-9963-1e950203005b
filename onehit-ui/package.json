{"name": "onehit-ui", "version": "5.14.0", "private": true, "scripts": {"lint": "vue-cli-service lint", "build-app": "vue-cli-service build --target lib --dest ../onehit-scenator/apps/OneHit --formats umd-min src/app/OneHitApp.js --name app ", "dev": "vue-cli-service build --watch --mode production --target lib --dest ../onehit-scenator/apps/OneHit --formats umd-min src/app/OneHitApp.js --name app"}, "dependencies": {"3d-force-graph": "^1.76.1", "@gaplin/vue-virtual-tree": "^1.0.2", "@ztree/ztree_v3": "3.5.46", "animate.css": "^4.1.1", "await-to-js": "^3.0.0", "axios": "^1.3.4", "condicio": "^2.0.0", "core-js": "^3.33.2", "crypto-js": "^4.1.1", "echarts": "5.4.3", "finger": "2.1.0", "gl-matrix-double": "^2.3.1", "jquery": "^3.6.3", "js-base64": "^3.7.5", "jsencrypt": "^3.3.2", "loadsh": "0.0.4", "lodash": "^4.17.21", "lodash-oneHit": "npm:lodash@^4.17.21", "moment": "2.26.0", "print-js": "^1.6.0", "qs": "^6.11.2", "spark-md5": "^3.0.2", "splitpanes": "2.4.1", "three-spritetext": "^1.9.4", "vue": "2.6.14", "vue-i18n": "^6.1.3", "vue-router": "^3.2.0", "vuex-oneHit": "npm:vuex@^3.4.0"}, "devDependencies": {"@babel/core": "7.12.16", "@babel/eslint-parser": "7.12.16", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/polyfill": "^7.12.1", "@vue/cli-plugin-babel": "5.0.0", "@vue/cli-plugin-eslint": "5.0.0", "@vue/cli-service": "5.0.0-onehit", "color": "0.11.0", "eslint": "7.32.0", "@ztree/ztree_v3": "^3.5.46", "eslint-plugin-vue": "8.0.3", "file-loader": "^6.2.0", "filemanager-webpack-plugin": "^8.0.0", "handlebars": "^4.7.7", "handlebars-loader": "^1.4.0", "i18next": "^23.4.1", "less": "^4.1.3", "element-ui": "2.15.6", "less-loader": "^11.1.0", "sass": "1.3.0", "sass-loader": "^8.0.2", "url-loader": "^4.1.1", "vue-eslint-parser": "9.1.0", "vue-simple-uploader": "^0.7.6", "vue-template-compiler": "2.6.14"}, "eslintConfig": {"env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false}, "rules": {"vue/multi-word-component-names": 0, "no-async-promise-executor": "off", "no-useless-catch": "off", "no-unused-vars": "off"}}, "browserslist": ["> 1%", "last 2 versions"]}