// 1_theme.scss
// 打包环境地址
$url: "../Scenator/resources/static/images/";
// 开发环境地址
// $url: "./assets/";

/*#region 基本色*/
// 主题1
$Color1: #0854A1; // Color-1 高亮文字/互动图标/按钮
$Color2: #114171; // Color-2 点击/选中/悬停
$Color3: rgba(0, 129, 255, 0.12); //Color-3 列表悬停（透明面板中）
$Color4: #E5F0FA; //Color-4 选中/按钮悬停（页面中）
$Color5: #0A2440; //Color-5 顶部导航栏背景

$Color6: #FFFFFF; //Color-6 基础容器颜色（内容层使用）
$Color7: #EFEFEF; //Color-7 背景层1(三维tab背景、表格表头背景）
$Color8: #FFFFFF; //Color-8 表格表体背景1使用
$Color9: #FAFAFA; //Color-9 表格表体背景2使用
$Color10: rgba(24, 42, 78, 0.06); //Color-10 手风琴背景使用、卡片背景使用

$Color11: #89919A; //Color-11 输入框边框颜色
$Color12: #D9D9D9; //Color-12 分隔线颜色1
$Color13: rgba(24, 42, 78, 0.12); //Color-13 分隔线颜色2 （透明面板中）
$Color14: #F7F7F7; //Color-14 背景层2使用
$Color15: #EFF2F4; //Color-15 列表悬停

$Color16: #182A4E; //Color-16 主要文字/反白图标
$Color17: #5D697A; //Color-17 次要文字
$Color18: #94999D; //Color-18 占位符文字
$Color19: rgba(24, 42, 78, 0.4); //Color-19 不可用文字
$Color20: #BB0000; //Color-20 错误提示文字

$Color21: #107E3E; //Color-21 成功提示文字
$Color22: #FFFFFF; //Color-22 反白文字/反黑文字
$Color23: #107E3E; // Color-23 积极
$Color24: #F1FDF6; //Color-24 积极背景
$Color25: #E9730C; // Color-25 警告

$Color26: #FEF7F1; //Color-26 警告背景
$Color27: #BB0000; // Color-27 消极
$Color28: #FFEBEB; //Color-28 消极背景
$Color29: #0A6ED1; //Color-29 信息
$Color30: #F5FAFF; //Color-30 信息背景

$Color31: #244F7D; // Color-31 顶栏悬停图标颜色
$Color32: #143F6C; // Color-32 顶栏点击图标颜色
$Color33: #B8BDC5; // Color-33 滚动条颜色
// 主题2
// $Color1: #00F7F6; 
// $Color2: #0AC0BF; 
// $Color3: rgba(0, 247, 246, 0.3500);
// $Color4: rgba(0, 247, 246, 0.3500);
// $Color5: #01243F;

// $Color6: #003C6B;
// $Color7: #00345E;
// $Color8: #00457B;
// $Color9: #003E6E;
// $Color10: rgba(5, 153, 184, 0.1500);

// $Color11: #0599B8;
// $Color12: #00597D;
// $Color13: #0B739D;
// $Color14: rgba(0, 247, 246, 0.1500);
// $Color15: rgba(0, 247, 246, 0.1500);


// $Color16: #FFFFFF;
// $Color17: #ADBBCC;
// $Color18: #6684A8;
// $Color19: rgba(255, 255, 255, 0.4000);
// $Color20: #FF0606;

// $Color21: #00F750;
// $Color22: #003C6B;
// $Color23: #00F750; // #00F750
// $Color24: #2D4535;
// $Color25: #FFD504; // #FFD504

// $Color26: #42382E;
// $Color27: #FF0606; // #FF0606
// $Color28: #412C2C;
// $Color29: #00F7F6;
// $Color30: #2C4242;

// $Color31: #244F7D; // 顶部图标悬停颜色
// $Color32: #143F6C; // 顶部点击悬停颜色
// $Color33: #0599B8; // Color-33 滚动条颜色

// 透明色
$transparent: transparent;
// 白色
$white: white;
// 黑色
$black: black;
/*#endregion*/

/*#region 公用class*/
.scenatorStyle {

	// 主要背景颜色
	.scenatorMainBackgroundColor {
		background-color: $Color6;
	}


	// 透明面板背景颜色
	.scenatorOpacityBackgroundColor {
		background-color: rgba($Color6, 0.75);
		backdrop-filter: blur(4px);
	}

	// 透明面板头部背景颜色
	.scenatorOpacityHeaderBackgroundColor {
		background-color: rgba($Color6, 0.4);
	}

	// 透明面板边框(分割线)
	.scenatorOpacityBorderColor {
		border-color: $Color13;
	}

	// 页面容器边框(分割线)--不透明
	.scenatorBorderColor {
		border-color: $Color12;
	}


	/*#region 产品色*/ 
	// Color-1 高亮文字、高亮图标、高亮按钮、高亮边框
	.text-color-active{
		color: $Color1;
	}
	.background-color-active{
		background-color: $Color1;
	}
	.border-color-active{
		border-color: $Color1;
	}

	// Color-2 点击、选中、悬停 （按钮、文字）
	.text-color-clickOrSelectOrHover{
		color: $Color2;
	}

	// 列表悬停（透明面板）
	.list-opacity-color-hover{
		background-color: $Color3;
	}
	// 列表悬停
	.list-primary-color-hover{
		background-color: $Color4;
	}
	/*#endregion*/

	/*#region 顶部区域*/
	// 顶部导航背景色（头部背景颜色）
	.scenatorHeaderBackgroundColor{
		background-color: $Color5;
	}
	// 顶栏悬停图标颜色
	.scenatorHeaderBtnHoverColor{
		background-color: $Color31;
	}
	// 顶栏点击图标颜色
	.scenatorHeaderBtnClickColor{
		background-color: $Color32;
	}
	/*#endregion*/

	/*#region 容器背景色*/
	// 主要背景色
	.background-primary{
		background-color: $Color6;
	}
	// 背景层1-灰色、表格表头背景
	.background-table-header{
		background-color: $Color7;
	}
	// 表格表体背景1使用
	.background-table-body{
		background-color: $Color8;
	}
	// 表格表体背景2使用
	.background-table-body-secondary{
		background-color: $Color9;
	}
	// 手风琴背景使用、卡片背景使用
	.background-table-cardAndCollapse{
		background-color: $Color10;
	}
	// 背景层2使用
	.background-table-secondary{
		background-color: $Color14;
	}
	// 列表悬停
	.background-list-hover{
		background-color: $Color15;
	}
	// 列表项悬浮、选中、禁用状态
	.list-item {
		&:hover {
			background-color: $Color15;
		}
		&.is-checked {
			background-color: $Color4;
			box-shadow: inset 0px -1px 0px 0px $Color1;
		}
		&.is-disabled {
			opacity: 0.4;
			cursor: not-allowed;
			&:hover {
				background-color: $Color8;
			}
		}
	}
	/*#endregion*/

	/*#region 边框和分割线*/
	// 输入框边框颜色
	.border-color-input{
		border-color: $Color11;
	}
	// 分隔线颜色
	.border-color-primary{
		border-color: $Color12;
	}
	// 分隔线颜色（透明面板）
	.border-color-opacity{
		border-color: $Color13;
	}
	/*#endregion*/

	/*#region 文字颜色*/
	// 主要文字颜色
	.scenatorMainFontColor {
		color: $Color16
	}

	// 次要文字颜色
	.scenatorSecondFontColor {
		color: $Color17
	}

	// 占位符文字
	.scenatorCoverFontColor {
		color: $Color18
	}

	// 不可用文字颜色
	.scenatorDisaledFontColor {
		color: $Color19
	}

	// 错误提示文字
	.scenatorErrorFontColor {
		color: $Color20
	}
	// 成功提示文字
	.scenatorSuccessFontColor{
		color:$Color21
	}
	// 反白文字
	.scenatorWhiteOrBlackFontColor{
		color:$Color22
	}
	/*#endregion*/


	/*#region 语义颜色*/
	// 积极（Positive）
	.color-positive{
		color: $Color23;
	}
	// 警告（Alert）
	.color-alert{
		color: $Color25;
	}
	// 消极（Negative）
	.color-negative{
		color: $Color27;
	}
	// 信息（Information）
	.color-information{
		color: $Color29;
	}

	// 积极背景（Positive）
	.background-positive{
		background-color: $Color24;
	}
	// 警告背景（Alert）
	.background-alert{
		background-color: $Color26;
	}
	// 消极背景（Negative）
	.background-negative{
		background-color: $Color28;
	}
	// 信息背景（Information）
	.background-information{
		background-color: $Color30;
	}
	/*#endregion*/

	// 工具栏悬浮背景色
	.scenatorToolbox:hover {
		background-color: $Color3;
	}

    // 属性面板表头颜色
    .jqGridHeadBackgroundColor{
        background-color: rgba($Color16, 0.03);
    }
    // 属性面板表格主体颜色
    .jqGridBodyBackgroundColor{
        background-color: rgba($Color6, 0.4);
    }
}
/*#endregion*/

/*#region 图标*/
$askIcon: $url+"scenator_askIcon.svg";
$successInconImg: $url+"scenator_successIcon.svg";
$infoInconImg: $url+"scenator_infoIcon.svg";
$warnInconImg: $url+"scenator_warnIcon.svg";
$errorInconImg: $url+"scenator_errorIncon.svg";
$treeRightIcon: $url+"scenator_rightIncon.svg";
$halfSelectedIcon: $url+"scenator_halfSelected.svg";
$treeOpen: $url+"scenator_treeOpen.svg";
$treeFolderOpen: $url+"scenator_tree_folderOpen.svg";
$treeFolderClose: $url+"scenator_tree_folderClose.svg";
$treeFile: $url+"scenator_tree_file.svg";

// 日历图标
$dateIcon: $url+"scenator_dateIcon.svg";
$dateIconActive: $url+"scenator_dateIconActive.svg";

// 删除图标
$removeIcon: $url+"scenator_removeIcon.svg";
// 删除图标Active图标
$removeActiveIcon: $url+"scenator_removeActiveIcon.svg";

// 向上箭头
$arrowUpIcon: $url+"scenator_arrowUp.svg";
$arrowUpActiveIcon: $url+"scenator_arrowUpActive.svg";

// 编辑
$editIcon: $url+"scenator_edit.svg";
$editActiveIcon: $url+"scenator_editActive.svg";
// 删除
$deleteIcon: $url+"scenator_delete.svg";
$deleteActiveIcon: $url+"scenator_deleteActive.svg";
// 设置
$setingIcon: $url+"scenator_seting.svg";
$setingActiveIcon: $url+"scenator_setingActive.svg";
// 放大
$magnifyIcon: $url+"scenator_magnify.svg";
$magnifyActiveIcon: $url+"scenator_magnifyActive.svg";
// 缩小
$reduceIcon: $url+"scenator_reduce.svg";
$reduceActiveIcon: $url+"scenator_reduceActive.svg";
// 更多
$moreIcon: $url+"scenator_more.svg";
$moreActiveIcon: $url+"scenator_moreActive.svg";

// 圆形箭头(右)
$circulArarrowRightIcon: $url+"scenator_circulArarrowRight.svg";
// 圆形箭头active(右)
$circulArarrowRightActiveIcon: $url+"scenator_circulArarrowRight_active.svg";
// 圆形箭头(左)
$circulArarrowLeftIcon: $url+"scenator_circulArarrowLeft.svg";
// 圆形箭头active(左)
$circulArarrowLeftActiveIcon: $url+"scenator_circulArarrowLeft_active.svg";

// 圆形添加图标
$circulAddIcon: $url+"scenator_circulAdd.svg";
// 圆形添加Active图标
$circulAddActiveIcon: $url+"scenator_circulAdd_active.svg";
//圆形减少
$circulMinusIcon: $url+"scenator_circulMinus.svg";
$circulMinussActiveIcon: $url+"scenator_circulMinusActive.svg";
// 展开
$expendIcon: $url+"scenator_expend.svg";
$expendActiveIcon: $url+"scenator_expendActive.svg";
// 收缩
$shrinkIcon: $url+"scenator_shrink.svg";
$shrinkActiveIcon: $url+"scenator_shrinkActive.svg";

// 保存
$saveIcon: $url+"scenator_save.svg";
$saveActiveIcon: $url+"scenator_saveActive.svg";

// 位置
$positionIcon: $url+"scenator_position.svg";
$positionActiveIcon: $url+"scenator_positionActive.svg";

// 重复
$repeatIcon: $url+"scenator_repeatIcon.svg";

// 搜索图标
$secrchIcon: $url+"scenator_searchIcon.svg";
// 绘制图标
$drawIcon: $url+"scenator_drawIcon.svg";
// 悬浮区域收起图标
$packUpIcon: $url+"scenator_packUpIcon.svg";
$packUpActiveIcon: $url+"scenator_packUpActiveIcon.svg";

// 场景菜单选中后的可操作按钮
$navItemHoverIcon: $url+"scenator_navItemHover.svg";

// 未分组场景前的默认图标
$nomalNavItemIcon: $url+"scenator_nomalNavItem.svg";

// 分组标题前的默认图标
$structureIcon: $url+"scenator_structureIcon.svg";

// 区域分割线
$areaLineIcon: $url+"scenator_line.svg";

// 目录树收起图标
// $stowIcon: $url + 'scenator_stowIcon.png';

// 标签图标
$ggbIcon: $url+'scenator_ggb.png';

.iconBtn_noCirculArarrowRight {
	position: relative;
	top: 14px;
	float: right;
	width: 16px;
	height: 16px;
	background: url($arrowUpIcon) no-repeat center;
	transform: rotate(90deg);
	background-size: 100% 100%;
}

// 圆形箭头图标(右)
.circulArarrowRightIcon {
	background: url($circulArarrowRightIcon) no-repeat center;
	background-size: 100% 100%;
}

// 圆形箭头图标:active(右)
.circulArarrowRightActiveIcon {
	background: url($circulArarrowRightActiveIcon) no-repeat center;
	background-size: 100% 100%;
}

// 圆形箭头图标(左)
.circulArarrowLeftIcon {
	background: url($circulArarrowLeftIcon) no-repeat center;
	background-size: 100% 100%;
}

// 圆形箭头图标:active(左)
.circulArarrowLeftActiveIcon {
	background: url($circulArarrowLeftActiveIcon) no-repeat center;
	background-size: 100% 100%;
}

// 圆形添加图标
.circulAddIcon {
	background: url($circulAddIcon) no-repeat center;
	background-size: 100% 100%;
}

// 圆形添加图标
.circulAddActiveIcon {
	background: url($circulAddActiveIcon) no-repeat center;
	background-size: 100% 100%;
}

// 删除图标
.removeIcon {
	background: url($removeIcon) no-repeat center;
	background-size: 100% 100%;
}

// 删除图标Active图标
.removeActiveIcon {
	background: url($removeActiveIcon) no-repeat center;
	background-size: 100% 100%;
}

// 日历图标
.dateIcon {
	background: url($dateIcon) no-repeat center;
	background-size: 100% 100%;
}

// 日历图标Active图标
.dateIconActive {
	background: url($dateIconActive) no-repeat center;
	background-size: 100% 100%;
}

// 搜索图标
.secrchIcon {
	background: url($secrchIcon) no-repeat center;
	background-size: 100% 100%;
}

.scenator_askIcon {
	background: url($askIcon) no-repeat center;
	background-size: 100% 100%;
}

.scenator_successIcon {
	background: url($successInconImg) no-repeat center;
	background-size: 100% 100%;
}

.scenator_warnIcon {
	background: url($warnInconImg) no-repeat center;
	background-size: 100% 100%;
}

.scenator_infoIcon {
	background: url($infoInconImg) no-repeat center;
	background-size: 100% 100%;
}

.scenator_errorIcon {
	background: url($errorInconImg) no-repeat center;
	background-size: 100% 100%;
}

/*#endregion*/

/*#region 自定义颜色变量*/

// Placeholder背景颜色
$primaryPlaceholderColor: $Color4;
$primaryDisabledColor: rgba($Color1, 0.4);
// 禁用时的边框颜色
$borderDisableBase: rgba($Color11, 0.4);
// 单选框只读时颜色
$radioInnercolor: rgba($Color1, 0.4);
// 单选框只读时背景
$radioInnerBackgroundColor: rgba($Color6, 0.4);
// 成功
$successBorderColor: rgba($Color23, 0.45);
$successBackground: $Color24;

$infoBorderColor: rgba($Color29, 0.45);
$infoBackground: $Color30;

$warnBackground: $Color26;
$warnBorderColor: rgba($Color25, 0.45);

$errorBackground: $Color28;
$errorBorderColor: rgba($Color27, 0.45);

// 表格边框颜色
$tableBorderColor: $Color12;
// 表格行背景色
$tableRowBackgroundColor: $Color8;
// 表格斑马线背景色
$tableStripebackgroundColor: $Color9;
// 表头背景色
$tableHeaderBackgroundColor: $Color7;
// hover表格行背景色
$tableHoverRow: $Color15;
// 当前选中行背景色
$currentRowBackgroundColor: $Color4;
// 当前选中行边框颜色
$currentRowBorderColor: $Color1;
/*#endregion*/

/*#region 自定义宽度或大小边距*/
// 左侧场景菜单宽度
$leftMenuWidth: 216px;
// 字体默认大小
$font-size: 14px;
// 字体默认行高
$font-height: 16px;
// 标题字体默认大小
$font-title-size: 16px;
$font-weight: 400;
$font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";

// 单选框大小
$radioWidth: 16px;
$radioheight: 16px;
// 按钮
$--button-border-radius: 2px;
$--button-padding-vertical: 8px;
$--button-padding-horizontal: 16px;
// 输入框
$--input--padding-vertical: 6px;
$--input--padding-horizontal: 8px;
/*#endregion*/
/* -------------------------------------------样式------------------------------------------------------------*/

.scenatorStyle {

	// 滚动条样式
	::-webkit-scrollbar-thumb {
		// 滚动条颜色
		background-color: $Color33 !important;
		border-radius: 0;
	}

	/*#region 按钮组件*/
	.el-button {
		padding: $--button-padding-vertical $--button-padding-horizontal;
		background: $transparent;
		border: 1px solid $transparent;
		color: $Color1;
		border-radius: $--button-border-radius;
		font-size: $font-size;
		font-weight: normal;

		+.el-button {
			margin-left: $--button-padding-horizontal;
		}

		&.less3word {
			width: 62px;

			&.iconBtn {
				width: 80px;
			}
		}

		// 带图标状态
		&.iconBtn {
			padding: 7px 16px 7px 12px;

			.icon {
				width: 16px;
				height: 16px;
			}

			span {
				margin-left: 8px;
			}
		}

		// 设置小按钮内边距
		// &.smallButton{
		//   	padding: 7px 12px;
		// }
		.el-message-box__btns button:nth-child(2) {
			margin-left: $--button-padding-horizontal;
		}

		// 主要按钮--自定义
		// 默认状态
		&.el-button--main--button {
			background: $Color1;
			border-color: $Color1;
			color: $Color8;

			span {
				color: $Color8;
			}
		}

		// 按下悬停状态
		&.el-button--main--button:hover {
			background: $Color2;
			border-color: $Color2;
			color: $Color8;

			span {
				color: $Color8;
			}
		}

		// 按下状态
		&.el-button--main--button:active {
			background: $Color2;
			border-color: $Color2;
			color: $Color8;

			span {
				color: $Color8;
			}
		}

		// 禁用状态
		&.el-button--main--button.is-disabled,
		&.el-button--main--button.is-disabled:hover {
			background-color: $Color1;
			border-color: $Color1;
			color: $Color8;
			opacity: 0.4;

			span {
				color: $Color8;
			}
		}


		// 次要按钮--自定义
		// 默认状态
		&.el-button--secondary--button {
			background: $Color6;
			border-color: $Color1;
			color: $Color1;

			span {
				color: $Color1;
			}
		}

		&.el-button--secondary--button.transparentBackground {
			background-color: $transparent;
		}

		// 按下悬停状态
		&.el-button--secondary--button:hover {
			background: $primaryPlaceholderColor;
			border-color: $Color2;
			color: $Color1;
		}

		// 按下状态
		&.el-button--secondary--button:active {
			background: $Color2;
			border-color: $Color2;
			color: $Color8;

			span {
				color: $Color8;
			}
		}

		// 禁用状态
		&.el-button--secondary--button.is-disabled,
		&.el-button--secondary--button.is-disabled:hover {
			color: $Color1;
			border-color: $Color1;
			opacity: 0.4;
			background: $Color6;

			&.transparentBackground {
				background-color: $transparent;
			}

			span {
				color: $Color1;
			}
		}

		// 第三重按钮样式:
		// 默认状态
		&.el-button--third--button:hover,
		&.el-button--third--button:active {
			color: $Color2;

			span {
				color: $Color2;
			}
		}

		&.el-button--third--button.is-disabled,
		&.el-button--third--button.is-disabled:hover,
		&.el-button--third--button.is-disabled:active {
			background: transparent;
			color: $Color2;
			opacity: 0.4;
			border-color: $transparent;

			span {
				color: $Color1;
			}
		}
	}

	// 链接字
	.el-link.el-link--default {
		color: $Color1;
		text-decoration: none;

		&:hover {
			color: $Color2;
			text-decoration: none;
		}

		&.is-disabled {
			color: $Color1;
			opacity: 0.4;
		}
	}

	/*#endregion*/

	/*#region 单选框/多选框*/
	// 单选框
	.el-radio {
		.el-radio__inner {
			border: 1px solid $Color11;
			width: $radioWidth;
			height: $radioheight;
			background-color: $Color6;
		}

		.el-radio__inner::after {
			background-color: $Color1;
			width: 6px;
			height: 6px;
		}

		.el-radio__label {
			color: $Color16;
			padding-left: 8px;
		}

		// 选中状态
		&.is-checked {
			.el-radio__label {
				color: $Color16;
			}

			.el-radio__inner {
				border: 1px solid $Color11;
				background-color: $Color6;
			}
		}

		// 选中+禁用状态
		&.is-disabled {
			.el-radio__label {
				color: $Color16;
				opacity: 40%;
			}

			.el-radio__input.is-disabled .el-radio__inner,
			.el-radio__input.is-disabled.is-checked .el-radio__inner {
				background-color: $radioInnerBackgroundColor;
				border-color: $borderDisableBase;
			}

			.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
				background-color: $primaryDisabledColor;
			}
		}

		// 只读状态
		&[scenatorType='readOnly'] {

			.el-radio__input.is-disabled .el-radio__inner,
			.el-radio__input.is-disabled.is-checked .el-radio__inner {
				background-color: $radioInnerBackgroundColor;
				border-color: $Color11;
			}

			.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
				background-color: $radioInnercolor;
			}
		}

		// 悬停状态
		&:hover .el-radio__inner {
			border-color: $Color1;
		}

		border-color: $Color11;
	}

	// 原生单选框
	.scenator_radio {
		font-size: $font-size;
		color: $Color16;

		&:hover {
			input:before {
				border: 1px solid $Color1;
			}

			input:checked:before {
				border: 1px solid $Color1;
			}
		}

		input {
			margin-right: 8px;
			height: $radioheight;
			width: $radioWidth;
			line-height: $radioheight;
			vertical-align: middle;
			margin-top: -5px;
			appearance: none;
			position: relative;
			outline: none;

			&:before {
				content: "";
				width: $radioWidth;
				height: $radioheight;
				border: 1px solid $Color11;
				display: inline-block;
				box-sizing: border-box;
				border-radius: 50%;
				vertical-align: middle;
			}

			&:checked:before {
				content: "";
				width: $radioWidth;
				height: $radioheight;
				border: 1px solid $Color11;
				display: inline-block;
				box-sizing: border-box;
				border-radius: 100%;
				vertical-align: middle;
			}

			&:checked:after {
				content: "";
				width: 6px;
				height: 6px;
				text-align: center;
				background: $Color1;
				border-radius: 100%;
				display: block;
				position: absolute;
				top: calc(50% - 2px);
				left: calc(50% - 3px);
			}
		}
	}

	// 多选框
	.el-checkbox {
		color: $Color16;
		font-size: $font-size;

		// 默认样式
		.el-checkbox__label {
			padding-left: 8px;
		}

		.el-checkbox__inner {
			border: 1px solid $Color11;
			width: $radioWidth;
			height: $radioheight;
			background-color: $Color6;

			&:hover {
				border: 1px solid $Color1;
			}
		}

		.el-checkbox__input.is-checked+.el-checkbox__label {
			color: $Color16;
		}

		.el-checkbox__input.is-checked .el-checkbox__inner,
		.el-checkbox__input.is-indeterminate .el-checkbox__inner,
		.el-checkbox__input.is-focus .el-checkbox__inner {
			background: $Color6;
			border-color: $Color11;

			&:hover {
				border: 1px solid $Color1;
			}
		}

		.el-checkbox__inner::after {
			border-color: $Color1;
			top: 2px;
			left: 5px;
		}

		// 多选框按钮内部（方框）样式
		.el-checkbox__input.is-indeterminate .el-checkbox__inner {
			background: $Color6;
		}

		.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
			background-color: $Color1;
			top: 3px;
			left: 3px;
			height: 8px;
			width: 8px;
			transform: rotate(0) scaleY(1);
		}

		// hover状态
		&:hover .el-checkbox__inner,
		&.is-checked:hover .el-checkbox__inner,
		&:hover .el-checkbox__input.is-indeterminate .el-checkbox__inner,
		&:hover .el-checkbox__input.is-focus .el-checkbox__inner {
			border: 1px solid $Color1;
		}

		&[scenatortype="readOnly"]:hover .el-checkbox__inner {
			border-color: $Color11;
		}

		&.is-disabled:hover .el-checkbox__inner {
			border-color: $Color11;
		}

		&.is-disabled {
			.el-checkbox__input.is-disabled+span.el-checkbox__label {
				color: $Color16;
				opacity: 40%;
			}

			.el-checkbox__input.is-disabled .el-checkbox__inner {
				background: $radioInnerBackgroundColor;
				border-color: $borderDisableBase;
			}

			.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
				border-color: $borderDisableBase;
			}

			.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
				border-color: $primaryDisabledColor;
			}

			.el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner {
				border-color: $borderDisableBase;
			}

			.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
				background-color: $primaryDisabledColor;
			}
		}

		// 只读状态
		&[scenatorType='readOnly'].is-disabled {
			.el-checkbox__input.is-disabled+span.el-checkbox__label {
				color: $Color16;
			}

			.el-checkbox__input.is-disabled .el-checkbox__inner {
				background: $radioInnerBackgroundColor;
				border-color: $Color11;
			}

			.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
				border-color: $Color11;
			}

			.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
				border-color: $radioInnercolor;
			}

			.el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner {
				border-color: $Color11;
			}

			.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
				background-color: $radioInnercolor;
			}
		}
	}

	/*#endregion*/

	/*#region 输入框*/
	// 输入框(elementui)
	.el-input,
	.el-picker-panel {
		overflow: hidden;

		// 搜索框
		&.el-input--prefix {
			.el-input__inner {
				padding-left: 36px;
			}

			.el-input__prefix {
				left: 1px;
				width: 32px;
				cursor: pointer;
				transition: background-color 0.3s;

				.el-input__icon {
					width: 100%;
				}
				.el-icon-search {
					transition: none;
				}

				.el-icon-search:before {
					content: " ";
					width: calc(100% - 2px);
					height: calc(100% - 2px);
					@extend .secrchIcon;
					position: absolute;
					top: 1px;
					left: 1px;
				}
			}
		}

		&.el-input--suffix {
			.el-input__inner {
				padding-right: 36px;
			}

			.el-input__suffix {
				width: 32px;
				height: 30px;
				right: 0px;
				margin-top: 1px;
				pointer-events: auto;
				cursor: pointer;
				// display: none;

				.el-input__icon {
					width: 100%;
				}

				&:hover {
					background-color: $Color3;
				}

				&:active {
					background-color: $Color1;

					.el-input__clear:before {
						@extend .removeActiveIcon
					}
				}

				.el-input__clear:before {
					content: " ";
					width: 16px;
					height: 16px;
					@extend .removeIcon;
					position: absolute;
					top: calc(50% - 8px);
					left: 50%;
					transform: translateX(-50%);
					cursor: pointer;
				}

				.el-input__clear:hover::before {
					border-radius: 2px;
					cursor: pointer;
				}
			}
		}

		&:hover,
		&:focus {
			.el-input__suffix {
				display: block;
			}
		}

		.el-input__inner:focus~.el-input__suffix {
			display: block;
		}

		.el-input__inner {
			background: transparent;
			border-radius: 2px;
			border: 1px solid $Color11;
			height: 32px;
			color: $Color16;
			line-height: 100%;
			padding: $--input--padding-vertical $--input--padding-horizontal;

			&:hover {
				border: 1px solid $Color1;
			}

			&::-webkit-input-placeholder {
				color: $Color18;
			}

			&:-ms-input-placeholder {
				color: $Color18;
			}

			&[disabled='disabled'] {
				border: 1px solid $Color11;
				opacity: 0.4;
			}

			&[disabled='disabled']:hover {
				border: 1px solid $Color11;
			}

			&[disabled='disabled'][scenatorType='readOnly'] {
				background: $Color10;
				border: 1px solid $Color11;
				opacity: 1;
			}

			&[disabled='disabled'][scenatorType='readOnly']:hover {
				border: 1px solid $Color11;
			}
		}

		&.is-disabled .el-input__inner {
			background: transparent;
			color: $Color19;
		}

		&.is-disabled .el-input__inner[scenatorType='readOnly'] {
			color: $Color16;
		}

		&.is-active .el-input__inner,
		.el-input__inner:focus {
			border-color: $Color1;
		}

		.el-input__suffix {
			.el-input__count {
				.el-input__count-inner {
					line-height: normal;
				}
			}
		}

		.el-input__inner[scenatorStyle="bgColor"] {
			background: $Color6;
		}
	}

	// 原生输入框
	.el-form {

		.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before,
		.el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
			content: ''
		}

		.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:after,
		.el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:after {
			content: '*';
			color: $Color20;
		}

		// 输入错误
		.el-form-item.is-error {

			.el-input__inner,
			.el-input__inner:focus,
			.el-textarea__inner,
			.el-textarea__inner:focus {
				border: 1px solid $Color20;
			}

			.el-form-item__error {
				color: $Color20;
				padding-top: 0;
			}

			.el-form-item__content {
				line-height: inherit;
			}
		}
	}

	// 原生input输入框
	.input {
		background: transparent;
		border-radius: 2px;
		border: 1px solid $Color11;
		height: 32px;
		color: $Color16;
		line-height: 100%;
		padding: 6px 8px;
		width: 100%;

		&:hover,
		&:focus,
		&:focus-visible {
			border: 1px solid $Color1;
			outline: $Color1;
		}

		&:-ms-input-placeholder {
			color: $Color18;
		}

		&::-webkit-input-placeholder {
			color: $Color18;
		}

		&.error {
			border-color: $Color27;
			// 去掉错误输入框外阴影
			// &:focus {
			// 	box-shadow: 0 0 0 2px rgba($Color27, 0.2);
			// }
		}

		&::-ms-clear {
			display: none;
		}
	}

	// easyui 输入框
	.textbox {
		border: 1px solid $Color11;
		border-radius: 2px;
		background: transparent;

		&:hover,
		&.textbox-focused {
			border-color: $Color1;
			box-shadow: none;
		}

		.textbox-text {
			padding: $--input--padding-vertical $--input--padding-horizontal;
			width: 100%;
			background: transparent;
		}
	}

	// easyui 下拉框
	.textbox.combo {
		.textbox-text {
			margin-right: 32px;
			padding: 0 4px 0 8px !important;
			// width: auto !important;
			width: calc(100% - 32px) !important;
			color: $Color16;
		}
	}
	.panel.combo-p {
		margin-top: 2px;
	}

	/*#endregion*/

	/*#region 下拉框*/
	// 下拉框
	.el-select {
		.el-input .el-select__caret {
			transform: none;
			transition: none;
		}

		.el-input__inner:focus {
			border-color: $Color1;

			+.el-input__suffix {
				background: $Color1;

				.el-icon-arrow-up:before {
					background: url($arrowUpActiveIcon) no-repeat center;
					transform: rotateZ(180deg);
				}
			}
		}

		.el-input__inner {
			background-color: transparent;
			border-radius: 2px;
			border: 1px solid $Color11;
			height: 32px;
			line-height: 100%;
			padding: $--input--padding-vertical $--input--padding-horizontal;
			padding-right: 0px;
			color: $Color16;

			&:-ms-input-placeholder {
				color: $Color18;
			}

			&::-webkit-input-placeholder {
				color: $Color18;
			}
		}

		&[scenatorStyle="bgColor"] .el-input__inner {
			background: $Color6;
		}

		.el-input__inner:hover {
			border: 1px solid $Color1;
		}

		.el-input__icon {
			line-height: 100%;
			height: 100%;
			color: $Color1;
			font-weight: 900;
			font-weight: 900;
			width: 32px;
		}

		.el-input .el-input__suffix {
			right: 0;
			top: 0px;
			box-sizing: border-box;
			height: 30px;
			padding: 0px 0px 0px 0px;
			margin: 1px 1px 0px 0px;
			transform: scale(1);

			&:active {
				background-color: $Color3;
			}
		}

		&:hover,
		&:active {
			.el-input__suffix {
				background: $Color3;
				height: 30px;
			}
		}

		.el-input.is-focus .el-input__inner {
			border: 1px solid $Color1;
		}

		.el-icon-arrow-up:before {
			content: " ";
			width: 16px;
			height: 16px;
			background: url($treeOpen) no-repeat center;
			position: absolute;
			top: calc(50% - 8px);
			left: calc(50% - 8px);
		}

		.el-input.is-focus .el-icon-arrow-up:before {
			background: url($arrowUpActiveIcon) no-repeat center;
			transform: rotateZ(180deg);
		}

		.el-input.is-focus .el-input__suffix {
			background: $Color1;
		}

		// 禁用
		.el-input__inner[disabled='disabled'] {
			border: 1px solid $Color11;
			opacity: 0.4;
		}

		.el-input.is-disabled .el-input__inner:hover {
			border-color: $Color11;
		}

		.el-input.is-disabled:hover .el-input__suffix {
			background-color: transparent;
		}

		.el-input.is-disabled .el-input__icon {
			opacity: 0.4;
		}
	}

	// 多选下拉框
	.select-checked {
		.el-input {
			// display: inline-block;
			// position: relative;
			// border-radius: 5px;
			width: 200px;
		}

		.el-select-dropdown.is-multiple {
			.el-select-dropdown__item {
				// padding: 0;
				padding: 0 16px 0 0;

				.el-checkbox__inner::after {
					display: none;
				}

				&.hover,
				&:hover {
					background-color: $Color4;

					.el-checkbox {
						.el-checkbox__inner {
							border: 1px solid $Color1;
						}
					}
				}

				&.selected {
					.el-checkbox__inner::after {
						display: block;
						-webkit-transform: rotate(45deg) scaleY(1);
						transform: rotate(45deg) scaleY(1);
					}
				}

				&.selected::after {
					content: "";
				}
			}
		}

		.el-checkbox {
			width: 100%;

			.el-checkbox__input {
				margin-left: 8px;
			}

			.el-checkbox__label {
				margin-left: 0;
			}
		}
	}

	/*#endregion*/

	/*#region textArea*/
	.el-textarea {
		.el-textarea__inner {
			background: transparent;
			border-color: $Color11;
			border-radius: 2px;
			color: $Color16;
			padding: $--input--padding-vertical $--input--padding-horizontal;
			overflow: auto;

			&:-ms-input-placeholder,
			textarea:-ms-input-placeholder {
				color: $Color18;
			}

			&::-webkit-input-placeholder,
			textarea::-webkit-input-placeholder {
				color: $Color18;
			}
		}

		&.error {
			.el-textarea__inner {
				border-color: $Color27;
			}
		}

		.el-textarea__inner[scenatorStyle="bgColor"] {
			background: $Color6;
		}

		.el-textarea__inner:hover {
			border-color: $Color1;
		}

		.el-textarea__inner:focus {
			border-color: $Color1;
		}

		.el-input__count {
			color: $Color18;
			background: transparent;
			position: relative;
			float: right;
			margin-top: 6px;
		}
	}

	/*#endregion*/

	/*#region 反馈提示*/

	// 简短的提示
	.scenator_briefMsg {
		min-width: 192px;
		max-width: 536px;
		background: $Color6;
		padding: 14px 16px;
		// height: 48px;
		// line-height: 48px;
		// padding: 0 16px;
		box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(0, 0, 0, 0.1);
		border-radius: 4px;
		border: 0px;

		article {
			color: $Color16;
		}

		.el-message__icon {
			width: 16px;
			height: 16px;
			margin-right: 8px;
			position: absolute;
			top: 16px;
		}

		.el-message__content {
			line-height: 20px;
			width: calc(100% - 24px);
			margin-left: 24px;
			color: $Color16;
		}

		.el-icon-info:before {
			content: "";
		}

		&.success .el-message__icon {
			@extend .scenator_successIcon;
		}

		&.warn .el-message__icon {
			@extend .scenator_warnIcon;
		}

		&.info .el-message__icon {
			@extend .scenator_infoIcon;
		}

		&.error {
			// background: $errorBackground;
			// border: 1px solid $errorBorderColor;

			.el-message__icon {
				@extend .scenator_errorIcon;
			}
		}
	}

	/* 复杂的提示 */
	.scenator_complexMsg {
		padding: 14px 16px;
		border: 0;
		border-radius: 4px;
		width: 720px;
		color: $Color16;

		article {
			color: $Color16;
			display: inline-block;
			width: calc(100% - 190px);
		}

		.el-icon-info {
			width: 16px;
			height: 16px;
			margin-right: 8px;
			position: absolute;
			top: 16px;
		}

		.el-message__content {
			line-height: 20px;
			width: calc(100% - 24px);
			margin-left: 24px;
			color: $Color16;

			.btns {
				position: absolute;
				right: 16px;
				top: 50%;
				transform: translateY(-50%);
				font-size: 0; // 消除两个button间的空隙
			}
		}

		.el-icon-info:before {
			content: "";
		}

		&.success {
			background: $successBackground;
			box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px $successBorderColor;
		}

		&.success .el-icon-info {
			@extend .scenator_successIcon;
		}

		&.warn {
			background: $warnBackground;
			box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px $warnBorderColor;
		}

		&.warn .el-icon-info {
			@extend .scenator_warnIcon;
		}

		&.info {
			background: $infoBackground;
			box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px $infoBorderColor;
		}

		&.info .el-icon-info {
			@extend .scenator_infoIcon;
		}

		&.error {
			background: $errorBackground;
			box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px $errorBorderColor;
		}

		&.error .el-icon-info {
			@extend .scenator_errorIcon;
		}
	}

	// 顶部通栏样式
	.scenator_topMsg {
		box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.12);
		border-radius: 0px;
		width: 100%;
		color: $Color16;
		padding: 14px 16px;
		top: 0px !important;
		border: 0;
		display: flex;
		align-items: center;
		justify-content: center;

		article {
			color: $Color16;
		}

		.el-message__content {
			line-height: 20px;
			color: $Color16;
		}

		.el-icon-info {
			width: 16px;
			height: 16px;
			margin-right: 8px;
		}

		.el-icon-info:before {
			content: "";
		}

		&.success {
			background: $Color24;
		}

		&.success .el-icon-info {
			@extend .scenator_successIcon;
		}

		&.warn {
			background: $warnBackground;
		}

		&.warn .el-icon-info {
			@extend .scenator_warnIcon;
		}

		&.info {
			background: $Color30;
		}

		&.info .el-icon-info {
			@extend .scenator_infoIcon;
		}

		&.error {
			background: $errorBackground;
		}

		&.error .el-icon-info {
			@extend .scenator_errorIcon;
		}
	}

	/*#endregion*/

	/*#region 弹窗*/
	// 弹出确认框
	.el-message-box.scenator_confirmPrompt {
		box-shadow: inset 0px 1px 0px 0px $Color12;
		background-color: $Color6;
		border: 0;
		border-radius: 4px;
		padding: 0px;
		width: 480px;

		.el-message-box__header {
			padding: 16px;
			font-size: $font-size;
			background-color: $Color6;
			height: 44px;

			.el-message-box__title {
				color: $Color16;
				font-size: $font-title-size;
				line-height: 100%;
				overflow: hidden;

				span {
					font-size: $font-title-size;
				}
			}

			.el-message-box__title::before {
				content: ' ';
				display: inline-block;
				width: $radioWidth;
				height: $radioheight;
				margin-right: 8px;
				vertical-align: middle;
				margin-top: -2px;
			}
		}

		.el-message-box__content {
			padding: 24px 16px;
			background-color: $Color6;
			color: $Color16;
		}

		.el-message-box__btns {
			padding: 12px 16px;
			-webkit-box-shadow: inset 0px 1px 0px 0px $Color12;
			box-shadow: inset 0px 1px 0px 0px $Color12;
			overflow: hidden;
		}

		.el-message-box__btns .el-button:nth-child(1) {
			margin-left: 16px;
		}

		&.ask {
			.el-message-box__title::before {
				@extend .scenator_askIcon;
			}

			.el-message-box__header {
				box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px $Color29;
			}
		}

		&.warn {
			.el-message-box__title::before {
				@extend .scenator_warnIcon;
			}

			.el-message-box__header {
				box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px $Color25;
			}
		}

		&.info {
			.el-message-box__title::before {
				@extend .scenator_infoIcon;
			}

			.el-message-box__header {
				box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px $Color29;
			}
		}

		&.error {
			.el-message-box__title::before {
				@extend .scenator_errorIcon;
			}

			.el-message-box__header {
				box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px $Color20;
			}
		}

		.scenator_confirmPrompt_article article {
			line-height: 28px;
			color: $Color16;
		}

		.scenator_confirmPrompt_showMore {
			margin-top: 16px;
			color: $Color1;
			cursor: pointer;
		}
	}

	// 弹出模态框
	.el-dialog__wrapper {
		justify-content: center;
		align-items: center;
		display: flex;

		.el-dialog__header {
			height: 44px;
			padding: 13px 16px 15px 16px;
			background-color: $Color6;
			line-height: 20px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			box-shadow: 0px 1px 0px 0px $Color12, 0px 0px 4px 0px rgba(0, 0, 0, 0.15);

			.el-dialog__title {
				font-size: $font-title-size;
				color: $Color16;
				font-weight: $font-weight;
				font-family: $font-family;
			}

			.el-dialog__headerbtn .el-dialog__close {
				color: $Color1;
				font-weight: 900;
				margin-top: 4px;
			}

			.el-dialog__close {
				width: 20px;
				height: 20px;
				border: 1px solid transparent;
				border-radius: 2px;
				@extend .removeIcon;
			}

			.el-dialog__close:hover {
				background-color: $primaryPlaceholderColor;
				width: 20px;
				height: 20px;
				border: 1px solid $Color1;
			}

			.el-dialog__close:active {
				background-color: $Color1;
				width: 20px;
				height: 20px;
				border: 1px solid $Color1;
				@extend .removeActiveIcon;
			}

			.el-icon-close:before {
				content: " "
			}
		}

		.el-dialog__headerbtn {
			position: initial;
			top: 10px;
		}

		.el-dialog {
			margin: 0;
			@extend .scenatorMainBackgroundColor;
		}

		.el-dialog__footer {
			padding: 12px 16px;
			box-shadow: inset 0px 1px 0px 0px $Color12;

			.dialog-footer {
				font-size: 0px;

				button:nth-of-type(1) {
					margin-left: 0px;
				}

				button:nth-last-child(1) {
					margin-left: 16px;
				}
			}

			.el-button:nth-child(1) {
				margin-right: 0px;
			}
		}
	}

	/*#endregion*/

	/*#region loading*/
	.scenator_loading {
		display: flex;
		justify-content: space-around;
		align-items: center;

		span {
			display: block;
			width: 10%;
			height: 100%;
			margin-right: 0px;
			border-radius: 50%;
			background: $Color1;
			-webkit-animation: scenatorLoading 1.5s ease infinite;
		}

		span:nth-child(1) {
			animation-delay: 0.5s;
		}

		span:nth-child(2) {
			animation-delay: 0.9s;
		}

		span:nth-child(3) {
			animation-delay: 1.2s;
		}

		// span:nth-child(4){
		// 	animation-delay: 0.52s;
		// }
		// span:nth-child(5){
		// 	animation-delay: 0.65s;
		// }
	}

	@keyframes scenatorLoading {
		0% {
			opacity: 1;
			transform: scale(2.5);
		}

		100% {
			opacity: 0.2;
			transform: scale(.3);
		}
	}

	.scenator_loading_shadow {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
			.loading-item {
				width: 6px;
				height: 6px;
				margin-left: 20px;
				border-radius: 50%;
				box-shadow: -30px 0px $Color1, -10px 0px $Color1, 10px 0px $Color1;
				&.loading-active {
					animation: shadowScaleForLoading 1.2s linear infinite;
				}
			}
			.loading-text {
				margin-top: 24px;
				font-size: 14px;
				font-family: $font-family;
				font-weight: 400;
				color: $Color16;
				line-height: 20px;
			}
	}
	
	@-webkit-keyframes shadowScaleForLoading {
		0% {
			box-shadow: -30px 0px 0px 4px $Color1, -10px 0px $Color1, 10px 0px $Color1;
		}
	
		33% {
			box-shadow: -30px 0px $Color1, -10px 0px 0px 4px $Color1, 10px 0px $Color1;
		}
	
		66% {
			box-shadow: -30px 0px $Color1, -10px 0px $Color1, 10px 0px 0px 4px $Color1;
		}
	
		100% {
			box-shadow: -30px 0px $Color1, -10px 0px $Color1, 10px 0px $Color1;
		}
	}
	@keyframes shadowScaleForLoading {
		0% {
			box-shadow: -30px 0px 0px 4px $Color1, -10px 0px $Color1, 10px 0px $Color1;
		}
	
		33% {
			box-shadow: -30px 0px $Color1, -10px 0px 0px 4px $Color1, 10px 0px $Color1;
		}
	
		66% {
			box-shadow: -30px 0px $Color1, -10px 0px $Color1, 10px 0px 0px 4px $Color1;
		}
	
		100% {
			box-shadow: -30px 0px $Color1, -10px 0px $Color1, 10px 0px $Color1;
		}
	}

	/*#endregion*/

	/*#region Title*/
	.scenatorTitle {
		box-sizing: content-box;
		padding: 4px 8px;
		color: $white;
		background: $black;
		box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.35);
		opacity: 0.6;
		position: absolute;
		top: 0px;
		left: 0px;
		z-index: 99999999;
		display: none;
		border-radius: 2px;
		// word-break: break-all;
		font-size: $font-size;
		line-height: 20px;
		max-width: 320px;

		p {
			line-height: 20px;
		}

		span {
			font-size: 12px;
			line-height: 17px;
			margin-top: 4px;
			display: inline-block;
		}
	}

	/*#endregion*/

	/*#region 日期选择框*/

	// 日期输入框
	.el-date-editor {
		width: 200px;
		height: 32px;
		line-height: 32px;

		&.el-input {
			width: 200px;
		}

		table {
			display: table;
			border: 0;
			box-shadow: none;
		}

		;

		th,
		td {
			border: 0px;
		}

		tr:nth-child(2n) {
			background-color: transparent;
		}

		tr {
			border-top: 0px;
		}

		.el-input__inner {
			background-color: $Color6;
			border-radius: 2px;
			border: 1px solid $Color11;
			height: 32px;
			color: $Color16;
			padding-left: 35px;
		}

		.el-input__inner:focus~.el-input__prefix {
			background: $Color1;
		}

		.el-input__inner:focus~.el-input__prefix .el-input__icon::before {
			background: url($dateIconActive) no-repeat center;
		}

		.el-input__icon {
			line-height: 100%;
			font-weight: 550;
			transition: none;
		}

		.el-input__icon::before {
			content: "";
			width: 16px;
			height: 16px;
			display: inline-block;
			margin-top: 7px;
		}

		.el-icon-date::before,
		.el-icon-time::before {
			background: url($dateIcon) no-repeat center;
			background-size: 100% 100%;
		}

		.el-input__prefix {
			width: 30px;
			height: 30px;
			background: $Color6;
			top: 1px;
			right: 1px;

			.el-icon-time:before {
				width: 14px;
				height: 16px;
			}
		}

		.el-input__prefix:active {
			.el-icon-date {
				background-color: $Color1;
				&::before {
					background: url($dateIconActive) no-repeat center;
				}
			}
		}

		&.el-input {
			.el-input__prefix {
				left: auto;
				top: 1px;
				right: 1px;
			}

			.el-input__suffix {
				display: none;

				&:hover,
				&:focus {
					background-color: transparent;
				}
			}

			&:hover .el-input__suffix,
			&:focus .el-input__suffix {
				display: none;
			}

			.el-input__inner {
				padding: $--input--padding-vertical $--input--padding-horizontal;
				padding-right: 36px;
			}
		}

		.el-icon-circle-close:before {
			content: " ";
			background: url($removeIcon) no-repeat center;
			width: 16px;
			height: 16px;
			position: absolute;
			margin: 0;
			right: 5px;
			top: calc(50% - 8px);
		}

		.el-input__suffix {
			display: none;
			width: 25px;
			right: 5px;
		}

		&:hover .el-input__inner {
			border: 1px solid $Color1;
		}

		&:hover .el-input__prefix {
			background-color: $Color3;

			.el-icon-time:before {
				background: $Color27;
			}
		}

		&.el-range-editor.el-input__inner {
			padding: $--input--padding-vertical $--input--padding-horizontal;
		}

		&.el-range-editor.el-date-editor--daterange,
		&.el-range-editor.el-date-editor--datetimerange {
			width: 280px;
			height: 32px;
			border-color: $Color11;
			border-radius: 2px;
			background: $Color6;

			&:hover {
				border: 1px solid $Color1;

				.el-range__icon {
					background-color: $primaryPlaceholderColor;
				}
			}

			&:focus {
				border: 1px solid $Color1;

				.el-icon-date:before,
				.el-icon-time:before {
					background: url($dateIconActive) no-repeat center;
				}
			}

			&.is-active {
				border: 1px solid $Color1;

				.el-icon-date:before,
				.el-icon-time:before {
					background: url($dateIconActive) no-repeat center;
				}
			}

			&.is-active .el-icon-date,
			&.is-active .el-icon-time {
				background-color: $Color1;

				&:before {
					background: url($dateIconActive) no-repeat center;
				}
			}

			&.is-active:hover .el-icon-date,
			&.is-active .el-icon-time {
				background-color: $Color1;

				&:before {
					background: url($dateIconActive) no-repeat center;
				}
			}

			.el-range__icon {
				width: 32px;
				height: 30px;
				padding: 0 2px;
				position: absolute;
				right: 0;
				top: 0;
			}

			.el-input__icon {
				line-height: 100%;
				color: $Color1;
			}

			.el-input__prefix {
				width: 30px;
				height: 30px;
				left: 1px;
				background: $Color6;
				top: 1px;

				.el-icon-time:before {
					width: 14px;
					height: 16px;
				}
			}

			.el-input__suffix {
				width: 25px;
				right: 0px;
			}

			.el-range-separator {
				line-height: 18px;
				color: $Color16;
				width: auto;
				padding: 0 8px;
			}

			.el-date-table td.end-date span,
			.el-date-table td.start-date span {
				background-color: $Color1;
				color: $Color22;
			}

			.el-range-input {
				background: $Color6;
				color: $Color16;
				text-align: left;
				width: calc((100% - 50px) / 2);
			}

			.el-range-input::-webkit-input-placeholder {
				color: $Color18;
			}

			&:-ms-input-placeholder {
				color: $Color18;
			}

			.el-range__close-icon {
				// width: 15px;
				// margin-left: 28px;
				display: none;
			}
		}

		&.el-range-editor.el-date-editor--datetimerange {
			width: 400px;
		}
	}

	// 日期框下拉
	.el-picker-panel {
		background-color: $Color6;
		// border-color: $Color12;
		border: 0;
		box-shadow: 0px 10px 30px 2px rgba(0, 0, 0, 0.12), 0px 0px 0px 1px $Color12;
		border-radius: 4px;

		table {
			display: table;
			border: 0px;
			box-shadow: none;
		}

		th,
		td {
			border: 0px;
			text-align: center;
			color: $Color16;
		}

		tr:nth-child(2n) {
			background-color: transparent;
		}

		tr {
			border-top: 0px;
		}

		.el-date-table {
			font-size: $font-size;

			tr,
			th,
			td {
				background: transparent;
			}

			td div {
				height: 34px;
				padding: 0;
			}

			span {
				width: 34px;
				height: 34px;
				line-height: 34px;
			}

			td.end-date span,
			td.start-date span {
				background-color: $Color1;
				color: $Color6;
			}

			td.prev-month span,
			td.next-month span {
				color: $Color17;
			}

			td.today span {
				color: $Color1;
				font-weight: 400;
			}

			td.in-range div {
				background-color: $Color3;
			}

			// td.today.in-range span {
			// 	color: $Color6;
			// }

			td.current:not(.disabled) span {
				background-color: $Color1;
				color: $Color8;
			}

			td.available:hover {
				color: $Color1;
			}
		}

		.el-date-picker__header,
		.el-date-range-picker__header {
			margin-top: 15px;
			margin-bottom: 15px;

			.el-picker-panel__icon-btn {
				border: none;
				color: $Color1;
			}

			.el-picker-panel__icon-btn:hover {
				border: none;
				color: $Color1;
			}

			.el-date-picker__header-label,
			div {
				color: $Color16;
				font-size: $font-size;
				font-weight: 500;
			}
		}

		.el-picker-panel__content {
			border: none;
			margin-top: 0;

			.el-date-range-picker__header {
				margin-top: 0;
			}
		}

		.el-date-range-picker__time-header {
			border-bottom: 1px solid $Color12;

			.el-input__inner {
				background: $Color6;
				border: 1px solid $Color11;
				border-radius: 2px;
				padding: 0 $--input--padding-vertical;
				color: $Color16;

				&:hover {
					border: 1px solid $Color1;
				}

				&::-webkit-input-placeholder {
					color: $Color18;
				}

				&:-ms-input-placeholder {
					color: $Color18;
				}
			}

			.el-icon-arrow-right {
				color: $Color16;
			}
		}

		.el-picker-panel__footer {
			background: $Color6;
			border-top: 1px solid $Color12;

			.el-button {
				padding: $--button-padding-vertical $--button-padding-horizontal;
			}

			.el-button--default {
				border-radius: 2px;
				background-color: $transparent;
				border-color: $Color1;
				color: $Color1;

				&:hover,
				&:focus {
					background: $primaryPlaceholderColor;
					border-color: $Color2;
					color: $Color1;
				}

				&:active {
					background: $Color1;
					border-color: $Color2;
					color: $Color8;
				}

				&.is-disabled {
					color: $Color1;
					border-color: $Color1;
					opacity: 0.4;
				}
			}

			.el-button--text {
				color: $Color1;

				&:hover {
					color: $Color2;
				}
			}
		}

		.el-date-range-picker__content.is-left {
			border-right: 0;
		}

		&.el-popper[x-placement^=top] .popper__arrow,
		&.el-popper[x-placement^=top] .popper__arrow::after {
			border-top-color: $Color6;
			display: none;
		}

		&.el-popper[x-placement^=bottom] .popper__arrow,
		&.el-popper[x-placement^=bottom] .popper__arrow::after {
			border-bottom-color: $Color6;
			display: none;
		}

		.el-picker-panel__icon-btn {
			color: $Color1;
		}

		// 带时间的日期选择框
		.el-picker-panel__body {
			.el-date-range-picker__time-picker-wrap {
				.el-time-panel {
					left: -34px;
					background-color: $Color6;
					border: 0;
					box-shadow: 0px 10px 30px 2px rgba(0, 0, 0, 0.12), 0px 0px 0px 1px $Color12;
					border-radius: 4px;

					.el-time-spinner__item {
						color: $Color17;

						&.active {
							color: $Color1;
						}

						&:hover:not(.disabled):not(.active) {
							background: transparent;
							color: $Color1;
						}
					}

					.el-time-panel__content::before,
					.el-time-panel__content::after {
						border-bottom: 1px solid $Color12;
						border-top: 1px solid $Color12;
					}

					.el-time-panel__footer {
						border-top: 1px solid $Color12;

						.el-time-panel__btn {
							&.confirm {
								color: $Color1;
							}

							&.cancel {
								color: $Color1;
							}
						}
					}

				}
			}
		}
	}

	/*#endregion*/

	/*#region 树结构（tree）*/

	// ZTree 默认（显示不下出现...）
	.scenator_ztree {
		padding: 0 0 0 16px;
		overflow-x: hidden;
		.ztree *{
			font-family: $font-family;
		}
		.ztree {
			padding: 0px;

			ul,
			li {
				list-style: none;
			}

			li a {
				width: 100%;
				padding: 0px;
				display: inline-block;
			}

			li ul {
				padding: 0 0 0 22px;
			}

			span {
				display: inline-block;
				vertical-align: middle;
			}

			.node_name {
				font-size: $font-size;
				$font-family: $font-family;
				font-weight: 400;
				color: $Color16;
				height: 28px;
				line-height: 28px;
				overflow: hidden;
				white-space: nowrap;
				width: calc(100% - 42px);
				text-overflow: ellipsis;
			}

			.node_name::before {
				left: -100%;
			}

			li {
				background: $transparent;
				white-space: nowrap;
			}

			li span.button.switch {
				height: 26px;
				width: $radioWidth;
				padding-right: 4px;
				position: relative;
				z-index: 2;
				transition: transform .3s ease-in-out;

				&.bottom_close,
				&.roots_close,
				&.center_close,
				&.root_close,
				&.bottom_open,
				&.roots_open,
				&.center_open,
				&.root_open,
				&.noline_open,
				&.noline_close {
					background: none;
					background-color: $transparent;
					border: 1px solid $transparent;
					box-sizing: content-box;
					overflow: hidden;
				}

				&.noline_docu {
					width: 16px;
					box-sizing: content-box;
					border: 1px solid $transparent;
				}

				&.bottom_close::after,
				&.roots_close::after,
				&.center_close::after,
				&.root_close::after,
				&.noline_close::after {
					content: ' ';
					width: $radioWidth;
					height: $radioheight;
					border: 1px solid $transparent;
					box-sizing: content-box;
					margin-top: calc(50% - 6px);
					display: inline-block;
					background: url($treeOpen) no-repeat center;
					background-size: 100% 100%;
					transform: rotate(-90deg);
				}

				// noline_open
				&.bottom_open::after,
				&.roots_open::after,
				&.center_open::after,
				&.root_open::after,
				&.noline_open::after {
					content: ' ';
					width: $radioWidth;
					height: $radioheight;
					border: 1px solid $transparent;
					;
					display: inline-block;
					box-sizing: content-box;
					margin-top: calc(50% - 6px);
					background: url($treeOpen) no-repeat center;
					background-size: 100% 100%;
					transform: rotate(0deg);
				}
			}

			li span.button.chk {
				height: 26px;
				width: 16px;
				margin: 0;
				padding-right: 4px;
				border-radius: 2px;
				background: none;
				cursor: pointer;
				position: relative;
				box-sizing: content-box;
				z-index: 2;
				border: 1px solid $transparent;
				overflow: hidden;
				&::after {
					content: ' ';
					width: $radioWidth;
					height: $radioheight;
					border: 1px solid $Color11;
					display: inline-block;
					border-radius: 2px;
					box-sizing: border-box;
					margin-top: calc(50% - 4px);
				}

				&.checkbox_true_part_focus::after {
					background: url($halfSelectedIcon) no-repeat center;
					border-radius: 2px;
					border: 1px solid $Color1;
				}

				&.checkbox_true_part::after {
					background: url($halfSelectedIcon) no-repeat center;
					border-radius: 2px;
					border: 1px solid $Color11;
				}

				&.checkbox_true_full::after {
					background: url($treeRightIcon) no-repeat center;
					border-radius: 2px;
					border: 1px solid $Color11;
				}

				&.checkbox_true_full_focus::after {
					background: url($treeRightIcon) no-repeat center;
					border-radius: 2px;
					border: 1px solid $Color1;
				}

				&.checkbox_false_full_focus::after {
					background: none;
					background-size: 100% 100%;
					border-color: $Color1;
				}

				&.checkbox_false_full::after {
					background: none;
					background-size: 100% 100%;
					border-radius: 2px;
					// border-color: $Color1;
				}
			}

			li a {
				height: 28px;
			}

			li a.curSelectedNode {
				background-color: $Color3;
				border: 0px;
				opacity: 1;
				height: 28px;
				border-left: 0px;

				.node_name {
					color: $Color1;
				}
			}

			li a:hover {
				text-decoration: none;
				background-color: $Color3;
				position: relative;

				&::before {
					background-color: $Color3;
				}
			}

			// 父节点选中
			li a.selectedParentNode {
				text-decoration: none;
				position: relative;
				background-color: $Color3;
				&::before {
					background-color: $Color3;
				}
			}

			// 父节点选中 淡入
			li a.selectedParentNodeFadeIn {
				text-decoration: none;
				position: relative;
				animation: fadeIn 1.5s 1;

				&:before {
					animation: fadeIn 1.5s 1;
				}
			}

			// 父节点选中 淡出
			li a.selectedParentNodeFadeOut {
				text-decoration: none;
				position: relative;
				animation: fadeOut 1.5s 1;

				&:before {
					animation: fadeOut 1.5s 1;
				}
			}

			li a {
				position: relative;
				&:before {
					content: ' ';
					margin-top: 0px;
					width: 1000px;
					height: 100%;
					position: absolute;
					z-index: 0;
					left: -1000px;
				}
			}
			// 节点hover状态
			li a.hoverNode {
				text-decoration: none;
				background-color: $Color3;
				position: relative;

				&:before {
					background-color: $Color3;
				}
			}

			li span.button.ico_open,
			li span.button.ico_docu,
			li span.button.ico_close,
			li span.button.bright_folder_ico_open,
			li span.button.bright_folder_ico_docu,
			li span.button.bright_node_ico_docu,
			li span.button.bright_folder_ico_close {
				background: none;
				width: $radioWidth;
				height: 28px;
				vertical-align: middle;
				position: relative;
				z-index: 2;
				margin-right: 5px;
				display: block;
				float: left;
			}

			li span.button.ico_close::after,
			li span.button.bright_folder_ico_close::after,
			li span.button.bright_folder_ico_docu::after {
				content: ' ';
				width: $radioWidth;
				height: $radioheight;
				background: url($treeFolderClose) no-repeat center;
				border: 1px solid $transparent;
				position: absolute;
				left: 0px;
				top: calc(50% - 10px);
			}

			li span.button.ico_open::after,
			li span.button.bright_folder_ico_open::after {
				content: ' ';
				width: $radioWidth;
				height: $radioheight;
				background: url($treeFolderOpen) no-repeat center;
				border: 1px solid $transparent;
				position: absolute;
				left: 0px;
				top: calc(50% - 10px);
			}
            li span.button.ico_docu{
                display: none;
            }
			li span.button.ico_docu::after,
			li span.button.bright_node_ico_docu::after {
				content: ' ';
				width: $radioWidth;
				height: $radioheight;
				background: url($treeFile) no-repeat center;
				border: 1px solid $transparent;
				position: absolute;
				left: 0px;
				top: calc(50% - 10px);
			}
			.curSelectedNode::before {
				background-color: $Color3;
			}
		}

		// 没有勾选框没有图标
		&[scenatorstyle="main"] {
			.ztree {
				li span.button.chk {
					display: none;
				}

				li span.button.ico_open,
				li span.button.ico_close,
				li span.button.ico_docu {
					display: none;
				}

				a {
					width: calc(100% - 22px);
				}

				.node_name {
					width: calc(100% - 16px);
				}
			}

			// 带激活图标
			&.hoverIcon {
				.ztree {
					position: relative;

					a {
						.scenator-tree-hover-icon {
							height: 20px;
							width: 20px;
							border-radius: 2px;
							position: absolute;
							margin-top: 4px;

							&.hoverIconId1 {
								right: 16px;
							}

							&.hoverIconId2 {
								right: 48px;
							}

							&.hoverIconId3 {
								right: 90px;
							}

							&.hoverIconId4 {
								right: 112px;
							}

							&.hoverIconId5 {
								right: 144px;
							}

							&:hover {
								background-color: $primaryPlaceholderColor;
								border: 1px solid $Color1;
							}

							&:active {
								background-color: $Color1;
								border: 1px solid $Color1;
							}
						}

						&[hoverIconNum="1"] .node_name {
							width: calc(100% - 16px - 36px);
						}

						&[hoverIconNum="2"] .node_name {
							width: calc(100% - 16px - 72px);
						}

						&[hoverIconNum="3"] .node_name {
							width: calc(100% - 16px - 108px);
						}

						&[hoverIconNum="4"] .node_name {
							width: calc(100% - 16px - 144px);
						}

						&[hoverIconNum="5"] .node_name {
							width: calc(100% - 16px - 180px);
						}
					}
				}
			}
		}

		// 有图标
		&[scenatorstyle="icon"] {
			.ztree {
				li span.button.chk {
					display: none;
				}

				a {
					min-width: calc(100% - 22px);
					width: max-content;
				}

				.node_name {
					width: calc(100% - 36px);
				}
			}

			// 带激活图标
			&.hoverIcon {
				.ztree {
					position: relative;

					a {
						.scenator-tree-hover-icon {
							height: 20px;
							width: 20px;
							border-radius: 2px;
							position: absolute;
							margin-top: 4px;

							&.hoverIconId1 {
								right: 16px;
							}

							&.hoverIconId2 {
								right: 48px;
							}

							&.hoverIconId3 {
								right: 90px;
							}

							&.hoverIconId4 {
								right: 112px;
							}

							&.hoverIconId5 {
								right: 144px;
							}

							&:hover {
								background-color: $primaryPlaceholderColor;
								border: 1px solid $Color1;
							}

							&:active {
								background-color: $Color1;
								border: 1px solid $Color1;
							}
						}

						&[hoverIconNum="1"] .node_name {
							width: calc(100% - 36px - 36px);
						}

						&[hoverIconNum="2"] .node_name {
							width: calc(100% - 36px - 72px);
						}

						&[hoverIconNum="3"] .node_name {
							width: calc(100% - 36px - 108px);
						}

						&[hoverIconNum="4"] .node_name {
							width: calc(100% - 36px - 144px);
						}

						&[hoverIconNum="5"] .node_name {
							width: calc(100% - 36px - 180px);
						}
					}
				}
			}
		}

		// 有勾选框
		&[scenatorstyle="checkBox"] {
			.ztree {

				li span.button.ico_open,
				li span.button.ico_close,
				li span.button.ico_docu {
					display: none;
				}

				a {
					width: calc(100% - 44px);
				}

				.node_name {
					width: calc(100% - 16px);
				}
			}

			// 带激活图标
			&.hoverIcon {
				.ztree {
					position: relative;

					a {
						.scenator-tree-hover-icon {
							height: 20px;
							width: 20px;
							border-radius: 2px;
							position: absolute;
							margin-top: 4px;

							&.hoverIconId1 {
								right: 16px;
							}

							&.hoverIconId2 {
								right: 48px;
							}

							&.hoverIconId3 {
								right: 90px;
							}

							&.hoverIconId4 {
								right: 112px;
							}

							&.hoverIconId5 {
								right: 144px;
							}

							&:hover {
								background-color: $primaryPlaceholderColor;
								border: 1px solid $Color1;
							}

							&:active {
								background-color: $Color1;
								border: 1px solid $Color1;
							}
						}

						&[hoverIconNum="1"] .node_name {
							width: calc(100% - 16px - 36px);
						}

						&[hoverIconNum="2"] .node_name {
							width: calc(100% - 16px - 72px);
						}

						&[hoverIconNum="3"] .node_name {
							width: calc(100% - 16px - 108px);
						}

						&[hoverIconNum="4"] .node_name {
							width: calc(100% - 16px - 144px);
						}

						&[hoverIconNum="5"] .node_name {
							width: calc(100% - 16px - 180px);
						}
					}
				}
			}
		}


		// 有图标有勾选框
		&[scenatorstyle="check+Icon"] {
			.ztree {
				a {
					width: calc(100% - 44px);
				}

				.node_name {
					width: calc(100% - 36px);
				}
			}

			// 带激活图标
			&.hoverIcon {
				.ztree {
					position: relative;

					a {
						.scenator-tree-hover-icon {
							height: 20px;
							width: 20px;
							border-radius: 2px;
							position: absolute;
							margin-top: 4px;

							&.hoverIconId1 {
								right: 16px;
							}

							&.hoverIconId2 {
								right: 48px;
							}

							&.hoverIconId3 {
								right: 80px;
							}

							&.hoverIconId4 {
								right: 112px;
							}

							&.hoverIconId5 {
								right: 144px;
							}

							&:hover {
								background-color: $primaryPlaceholderColor;
								border: 1px solid $Color1;
							}

							&:active {
								background-color: $Color1;
								border: 1px solid $Color1;
							}
						}

						&[hoverIconNum="1"] .node_name {
							width: calc(100% - 36px - 36px);
						}

						&[hoverIconNum="2"] .node_name {
							width: calc(100% - 36px - 72px);
						}

						&[hoverIconNum="3"] .node_name {
							width: calc(100% - 36px - 108px);
						}

						&[hoverIconNum="4"] .node_name {
							width: calc(100% - 36px - 144px);
						}

						&[hoverIconNum="5"] .node_name {
							width: calc(100% - 36px - 180px);
						}
					}
				}
			}
		}
	}

	// ZTree  超出显示滚动条
	.scenator_ztree.overflow_scroll {
		overflow: auto;
		overflow-y: overlay;

		.ztree {
			display: inline-block;
			min-width: 100%;

			a {
				overflow: initial;
			}

			.node_name {
				overflow: initial;
				width: auto !important;
			}

			.scenator-tree-hover-icon {
				position: absolute;
				margin-top: 4px;
				&.hoverIconId1 {
					right: 16px;
				}
				&.hoverIconId2 {
					right: 48px;
				}
			}
		}
	}

	// element-tree
	.el-tree {
		background: transparent;

		.el-tree-node__content {
			height: 28px;
		}

		.el-tree-node.is-current>.el-tree-node__content {
			background-color: $Color3;

			.el-tree-node__label {
				color: $Color1;
			}
		}

		.el-tree-node__content:hover {
			background-color: $Color3;

			.el-tree-node__label {
				color: $Color1;
			}
		}

		.is-current .el-tree-node__content:hover {
			background-color: $Color3;

			.el-tree-node__label {
				color: $Color1;
			}
		}

		.el-icon-caret-right:before {
			content: " ";
			width: $radioWidth;
			width: $radioheight;
			position: absolute;
			background-size: 100% 100%;
		}

		.el-tree-node__content>.el-tree-node__expand-icon {
			background: url($treeOpen) no-repeat center;
			transform: rotate(-90deg);
			height: 28px;
			width: 16px;
			margin-right: 4px;
		}

		.el-tree-node__expand-icon.expanded {
			transform: rotate(0deg);
		}

		.el-tree-node__expand-icon.is-leaf {
			color: transparent;
			cursor: default;
			background: none;
		}

		.el-tree-node__label {
			color: $Color16;
			position: relative;
		}

		.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content .el-tree-node__label {
			font-size: $font-size;
			color: $Color1;
		}

		.el-checkbox__inner {
			border: 1px solid $Color11;
			width: $radioWidth;
			height: $radioheight;
			background-color: $transparent;
		}

		.el-checkbox__inner::after {
			left: 5px;
			top: 2px;
		}

		.el-checkbox__inner:hover {
			border: 1px solid $Color1;
		}

		.el-checkbox__input.is-checked .el-checkbox__inner,
		.el-checkbox__input.is-indeterminate .el-checkbox__inner {
			background-color: $Color6;
		}

		.el-checkbox__inner::after {
			border-color: $Color1;
		}

		&[scenatorStyle="icon"] {
			.el-tree-node__label {
				text-indent: 20px;
				display: inline-block;
			}

			.el-tree-node__expand-icon.expanded~.el-tree-node__label:before {
				background: url($treeFolderOpen) no-repeat center;
			}

			// treeFolderOpen
			.el-tree-node__label:before {
				content: " ";
				width: $radioWidth;
				height: $radioheight;
				position: absolute;
				background: url($treeFolderClose) no-repeat center;
				// background: red;
				left: 0px;
				top: 2px;
				background-size: 100% 100%;
			}

			.el-tree-node__expand-icon.is-leaf~.el-tree-node__label:before,
			.el-tree-node__expand-icon.is-leaf~.custom-tree-node .el-tree-node__label:before {
				background: url($treeFile) no-repeat center;
			}
		}

		// 修改勾选框选中样式
		.el-checkbox__input .el-checkbox__inner::before,
		.el-checkbox__input .el-checkbox__inner::after {
			width: 16px;
			height: 16px;
			left: -1px;
			top: -1px;
			border-radius: 2px;
			border: none;
			-webkit-transform: rotate(0deg) scaleY(1);
			transform: rotate(0deg) scaleY(1)
		}

		.el-checkbox__input.is-checked .el-checkbox__inner::after {
			background: url($treeRightIcon) no-repeat center;
		}

		.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
			background: url($halfSelectedIcon) no-repeat center;
		}

		.el-tree-node__content>label.el-checkbox {
			margin-right: 5px;
		}

		// 带激活图标
		&.hoverIcon {
			.el-tree-node__content:hover>.custom-tree-node>.scenator-tree-hover-icon {
				visibility: visible;
			}

			// 激活状态有图标
			.is-current {
				&[role="treeitem"]>.el-tree-node__content>.custom-tree-node>.scenator-tree-hover-icon {
					visibility: visible;
				}
			}

			.el-tree-node__content~.is-current .el-tree-node.is-current>.el-tree-node__content {
				.el-tree-node__expand-icon.is-leaf~.custom-tree-node>.el-tree-node__label~.scenator-tree-hover-icon {
					visibility: visible;
				}
			}

			.scenator-tree-hover-icon {
				height: 20px;
				width: 20px;
				border-radius: 2px;
				position: absolute;
				visibility: hidden;

				&.hoverIconId1 {
					right: 16px;
				}

				&.hoverIconId2 {
					right: 48px;
				}

				&.hoverIconId3 {
					right: 80px;
				}

				&.hoverIconId4 {
					right: 112px;
				}

				&.hoverIconId5 {
					right: 144px;
				}

				&:hover {
					background-color: $primaryPlaceholderColor;
					border: 1px solid $Color1;
				}

				&:active {
					background-color: $Color1;
					border: 1px solid $Color1;
				}
			}
		}
	}

	/*#endregion*/

	/*#region 表格*/
	// 原生table
	.table {
		border: 1px solid $Color12;
		box-shadow: none;
		overflow: auto;
		width: 100%;
		table-layout: fixed;

		tr {
			height: 40px;
			// line-height: 40px;
			background: $tableRowBackgroundColor;
			box-shadow: inset 0px -1px 0px 0px $tableBorderColor;

			th,
			td {
				padding: 0 12px;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
				text-align: left;
				border: 1px solid $Color12;
				min-width: 56px;
				max-width: 400px;
				color: $Color16;
			}

			th {
				background: $Color7;
			}

			td {
				&:hover {
					background: $tableHoverRow;
				}
			}

			&:hover,
			&:hover td,
			&.table__row--striped:hover,
			&.table__row--striped:hover td {
				background: $tableHoverRow;
			}

			&.table__row--striped,
			&.table__row--striped td {
				background: $tableStripebackgroundColor;
			}

			&.current-row,
			&.current-row:hover,
			&.current-row td,
			&.current-row:hover td {
				background: $currentRowBackgroundColor;
				box-shadow: inset 0px -1px 0px 0px $currentRowBorderColor;
			}
		}
	}

	table {
		margin: 0px;
		border: 0px;
		display: table;

		tr,
		th,
		td {
			background: $tableRowBackgroundColor;
			height: 40px;
		}
	}

	.el-table {
		background: $Color6;
		box-shadow: inset -1px -1px 0px 0px $tableBorderColor;

		tr,
		th,
		td {
			background: $tableRowBackgroundColor;
			height: 40px;
		}

		&.el-table--border,
		&.el-table--group {
			border-color: $tableBorderColor;

			th.el-table__cell.gutter:last-of-type {
				border-color: $tableBorderColor;
			}
		}

		td.el-table__cell,
		th.el-table__cell.is-leaf {
			border-bottom-color: $Color12;
		}

		.el-table__cell,
		.el-table__row td,
		.el-table__header th {
			padding: 0 12px;
		}

		.el-table__header {

			tr,
			th {
				background: $tableHeaderBackgroundColor;
			}
		}

		.el-table__body {
			.el-table__row {
				border: 0;

				&:hover td.el-table__cell,
				&.el-table__row--striped:hover td.el-table__cell,
				&.hover-row>td.el-table__cell,
				&.el-table__row--striped.hover-row td.el-table__cell {
					background: $tableHoverRow;
				}

				&.el-table__row--striped td.el-table__cell {
					background: $tableStripebackgroundColor;
				}

				&.current-row {
					box-shadow: inset 0px -1px 0px 0px $currentRowBorderColor;
				}

				&.current-row td.el-table__cell,
				&.el-table__row--striped.current-row td.el-table__cell {
					background: $currentRowBackgroundColor;
					// border-bottom: 1px solid $currentRowBorderColor;
					box-shadow: inset 0px -1px 0px 0px $currentRowBorderColor;
				}
			}
		}

		tr {
			box-shadow: inset 0px -1px 0px 0px $tableBorderColor;
			// border-bottom: $tableBorderColor;
		}

		th,
		td {
			border: 0;
			border-right: 1px solid $tableBorderColor;
			border-bottom: 1px solid $tableBorderColor;
			padding: 0 12px;
			color: $Color16;
			min-width: 56px;
			max-width: 400px;

			&.el-table__cell>.cell {
				padding-left: 0;
				padding-right: 0;
				white-space: nowrap;
				// min-width: 60px;
				// max-width: 200px;
			}

			.cell.el-tooltip {
				width: auto !important;
			}
		}

		&.el-table__cell:first-child .cell {
			padding-left: 0;
			padding-right: 0;
		}

		.el-table__empty-block {
			border-right: 1px solid $tableBorderColor;
			border-bottom: 1px solid $tableBorderColor;

			.el-table__empty-text {
				color: $Color16;
			}
		}

		&::before,
		&::after {
			display: none;
		}

		.el-table__cell {
			&.el-table-column--selection {
				.cell {
					text-align: center;
				}
				.el-checkbox {
					color: $Color16;
					font-size: $font-size;

					.el-checkbox__input {
						.el-checkbox__inner {
							border: 1px solid $Color11;
							width: $radioWidth;
							height: $radioheight;
							background-color: $Color6;

							&::after {
								border-color: $Color1;
								top: 2px;
								left: 5px;
							}
						}

						&.is-checked .el-checkbox__inner,
						&.is-indeterminate .el-checkbox__inner {
							background: $Color6;
							border-color: $Color11;
						}

						// 半选状态
						&.is-indeterminate .el-checkbox__inner::before {
							background-color: $Color1;
							top: 3px;
							left: 3px;
							height: 8px;
							width: 8px;
							transform: rotate(0) scaleY(1);
						}
					}

					// hover状态
					&:hover .el-checkbox__inner,
					&.is-checked:hover .el-checkbox__inner,
					&:hover .el-checkbox__input.is-indeterminate .el-checkbox__inner {
						border: 1px solid $Color1;
					}
				}
			}

			&.el-table__expand-column {
				.el-table__expand-icon {
					.el-icon-arrow-right {
						&::before {
							content: " ";
							width: 16px;
							height: 16px;
							position: absolute;
							// top: -3px;
							// left: -3px;
							background: url($treeOpen) no-repeat center;
							transform: rotate(-90deg);
							background-size: 100% 100%;
						}
					}
				}

				.el-table__expand-icon--expanded .el-icon-arrow-right:before {
					transform: rotate(270deg);
				}

				.el-table__expand-icon>.el-icon {
					// left: 6px;
					// right: 4px;
					left: 50%;
					top: 50%;
					transform: translate(-8px, -8px);
					margin-left: 0;
					margin-top: 0;
				}
			}
		}

		.el-table__fixed-right-patch {
			background: $tableHeaderBackgroundColor;
			border-bottom: 0;
			box-shadow: inset -1px -1px 0px 0px $tableBorderColor;
		}

		.el-table__footer-wrapper td.el-table__cell {
			border-color: $tableBorderColor;
		}
	}

	.el-table__fixed,
	.el-table__fixed-right {
		&::before {
			display: none;
		}
	}

	.el-table[scenatorstyle="scenator_expandNoBorder"] {
		border: 1px solid $tableBorderColor;
		border-bottom: 0px;
		border-right: 0px;

		.el-table--border::after,
		.el-table--group::after,
		.el-table::before {
			background-color: transparent;
			width: 0px;
		}

		.el-table--border::after,
		.el-table--group::after,
		.el-table::before {
			content: none;
		}
	}

	.el-table[scenatorStyle="scenator_smallTable"] {

		tr,
		th,
		td {
			height: 32px;
			// line-height: 32px;
		}

		.el-table__row td,
		.el-table__header th {
			padding: 0 12px;
		}
	}

	.el-table[scenatorstyle="scenator_expandNoBorder"] {
		.el-icon-arrow-right:before {
			content: " ";
			width: 16px;
			height: 16px;
			position: absolute;
			// top: -3px;
			// left: -3px;
			background: url($treeOpen) no-repeat center;
			transform: rotate(-90deg);
			background-size: 100% 100%;
		}

		.el-table__expand-icon--expanded .el-icon-arrow-right:before {
			transform: rotate(270deg);
		}

		.el-table__expand-icon>.el-icon {
			// left: 6px;
			// right: 4px;
			left: 50%;
			top: 50%;
			transform: translate(-8px, -8px);
			margin-left: 0;
			margin-top: 0;
		}

		.el-table__expanded-cell {
			padding: 8px 16px;
		}
	}

	/*#endregion*/

	/*#region 卡片*/

	// card组件
	.scenatorStyleNavCard {
		width: 280px;
		height: 60px;
		display: flex;
		flex-direction: column;
		padding: 8px;
		background-color: rgba($Color16, 0.06);
		border: 1px solid rgba($Color16, 0.12);
		border-radius: 2px;
		cursor: pointer;

		&.active {
			background-color: rgba($Color3, 0.12);
			border: 1px solid $Color1;
		}

		.nav-card-name {
			color: $Color16;
			margin-bottom: 4px;
			font-size: $font-size;
			font-family: $font-family;
			font-weight: 400;
			width: 100%;
			overflow: hidden;

			.nav-card-icon {
				height: 16px;
				width: 16px;
				display: inline-block;
				border-radius: 4px;
				background-position-y: 2px;
				margin-right: 4px;
			}
		}

		.nav-card-text {
			color: $Color17;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			font-size: $font-size;
			margin-left: 20px;
		}

		&:hover {
			background-color: rgba($Color3, 0.12);
			border: 1px solid $Color1;
		}
	}
	.scenatorStyleCardSecondary{
		width: 100%;
		height: 40px;
		border: 1px solid $Color11;
		box-sizing: border-box;
		padding: 10px 12px;
		margin: 8px 0;
		background-color: $Color10;
		cursor: pointer;
		&:hover{
			color: $Color1;
			border-color: $Color1;
			background-color: rgba($Color11, 0.30);
		}
		&.active{
				color: $Color1;
				border-color: $Color1;
				background-color: rgba($Color11, 0.30);
			}
	}

	/*#endregion*/

	// 列表组件
	/*#region 列表*/
	.scenatorStyleListItem {
		.list-item-wrapper {
			display: flex;
			flex-direction: column;
			border-bottom: 1px solid $Color13;
			font-size: 14px;
			padding: 13px 0 16px;
			margin: 0 16px;
			.list-item-name {
				color: $Color16;
				height: 20px;
				line-height: 20px;
				white-space: nowrap;
			}
			.list-item-text {
				margin-top: 4px;
				color: $Color17;
				height: 20px;
				line-height: 20px;
				white-space: nowrap;
			}
		}
		&:hover {
			background-color: $Color3;
		}
		&.active {
			background-color: $Color3;
			.list-item-name {
				color: $Color29;
			}
		}
	}
	/*endregion*/

	/*#region 下拉菜单dropdown*/

	.el-select-dropdown {
		background: $Color6;
		box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px $Color12;
		border-radius: 4px;
		border: 0;
		overflow: hidden;

		&.el-popper[x-placement^=bottom] {
			margin-top: 3px;
		}
		&.el-popper[x-placement^=top] {
			margin-bottom: 3px;
		}

		.el-select-dropdown__list {
			padding: 0;
			max-height: 192px;
			overflow-y: auto;

			.el-select-dropdown__item {
				color: $Color16;
				background: $Color6;
				height: 32px;
				line-height: 32px;
				padding: 0 16px;

				&.hover,
				&:hover {
					background-color: $Color15;

					.el-checkbox__input {
						.el-checkbox__inner {
							border: 1px solid $Color1;
						}
					}
				}

				&.selected {
					color: $Color1;
					box-shadow: inset 0px -1px 0px 0px $Color1;
					background-color: $Color3;

					&.hover {
						background-color: $Color15;
					}

					.el-checkbox__input {
						.el-checkbox__inner {
							border: 1px solid $Color11;
						}
					}
				}

				.el-checkbox__input {
					.el-checkbox__inner {
						border: 1px solid $Color11;
						background: $transparent;
					}
				}
			}
		}

		&.is-multiple {
			.el-select-dropdown__item {
				&.selected {
					color: $Color16;
					box-shadow: inset 0px -1px 0px 0px $Color1;
					background-color: $Color4;

					&.hover {
						background-color: $Color4;
					}
				}
			}
		}

		.popper__arrow {
			display: none;
		}
	}

	/*#endregion*/

	/*#region tooltip*/
	.el-tooltip__popper {
		max-width: 320px !important;
		padding: 4px 8px;
		color: $white;
		font-size: $font-size;
		line-height: 24px;
		border-radius: 2px;
		background: $black;
		box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.35);
		opacity: 1;

		&.is-dark {
			background: $black
		}

		&[x-placement="top"] {
			margin-top: 15px;
		}

		.popper__arrow {
			display: none;
		}
	}

	/*#endregion*/

	/*#region 分页*/
	.el-pagination {
		height: 48px;

		// 页码样式
		.el-pager {
			margin: 0 8px;

			li {
				border-radius: 2px;
				border: 1px solid transparent;
				background: transparent;
				width: 28px;
				height: 28px !important;
				line-height: 28px !important;
				font-size: $font-size !important;
				color: $Color17;
				font-weight: normal;
				margin-right: 8px;

				&:nth-last-child(1) {
					margin: 0;
				}

				&:hover {
					background: $Color15;
				}

				&.active {
					border-color: $Color1;
					color: $Color1;
				}
			}
		}

		.el-pagination__sizes {
			.el-input .el-input__inner:hover {
				border-color: $Color1;
			}

			.el-select {
				height: 100%;

				.el-input__inner {
					color: $Color16;
					height: 100%;
					border-color: $Color11;
					border-radius: 2px;
				}

				.el-input {
					height: 100%;

					.el-input__inner {
						border-radius: 2px;
						padding: 0 5px;
						padding-right: 32px;
					}

					.el-input__suffix {
						height: 28px;
					}

					&.is-focus {
						background: $primaryPlaceholderColor;

						.el-input__inner {
							border-color: $Color1;
						}

						.el-input__icon {
							margin-top: -1px;
						}

						.el-input__suffix-inner {
							display: flex;
							justify-content: center;
							align-items: center;
						}
					}

					.el-input__icon {
						line-height: inherit;
						width: 20px;
						margin-top: -2px;
						line-height: 32px;
					}
				}

				.el-select:hover .el-input__suffix {
					background: $primaryPlaceholderColor;
					height: 28px;
				}

				.el-select:hover .el-input__icon {
					color: $Color1;
				}
			}

		}

		.el-pagination__rightwrapper {
			margin-right: auto;

			span {
				font-size: $font-size;
				color: $Color17;
			}

			&+span.el-pagination__total {
				font-size: $font-size;
			}

			button {
				background: transparent;
			}
		}

		button {
			background: transparent;
			position: relative;

			&:disabled {
				background: transparent;
			}
		}

		.el-pagination__jump {
			color: $Color17;
		}

		.el-icon {
			&.el-icon-arrow-left:before {
				content: " ";
				width: $radioWidth;
				height: $radioheight;
				background: url($treeOpen) no-repeat center;
				transform: rotate(90deg);
				background-size: 100% 100%;
				position: absolute;
				top: 5px;
				left: 2px;
			}

			&.el-icon-arrow-right:before {
				content: " ";
				width: $radioWidth;
				height: $radioheight;
				background: url($treeOpen) no-repeat center;
				transform: rotate(-90deg);
				background-size: 100% 100%;
				position: absolute;
				top: 5px;
				left: 2px;
			}
		}
		.btn-prev[disabled="disabled"] {
			.el-icon-arrow-left:before {
				opacity: 0.4;
			}
		}

		.btn-next[disabled="disabled"] {
			.el-icon-arrow-right:before {
				opacity: 0.4;
			}
		}
	}

	.el-pagination[scenatorStyle="pagination_normal"] {
		padding: 10px 0;

		.btn-prev {
			position: absolute;
			right: 75px;
			padding: 0px;
		}

		.btn-next {
			position: absolute;
			padding: 0px;
			right: -2px;
		}

		button {
			min-width: 20px;
		}

		.el-icon-arrow-right:before {
			top: 5px;
		}

		.el-icon-arrow-left:before {
			content: " ";
			width: $radioWidth;
			height: $radioheight;
			background: url($treeOpen) no-repeat center;
			transform: rotate(90deg);
			background-size: 100% 100%;
			position: absolute;
			top: 5px;
			left: 2px;
		}

		.el-icon-arrow-right:before {
			content: " ";
			width: $radioWidth;
			height: $radioheight;
			background: url($treeOpen) no-repeat center;
			transform: rotate(-90deg);
			background-size: 100% 100%;
			position: absolute;
			top: 5px;
			left: 2px;
		}

		.el-pagination__sizes {
			position: absolute;
			right: 95px;
		}

		.el-pagination__rightwrapper {
			position: relative;
		}

		.el-pagination__total {
			color: $Color17;
		}

		// 页码插槽
		.scenator_pageNum {
			span {
				color: $Color17;

				&:nth-child(1) {
					margin-right: 140px;
				}
			}

			.el-input {
				width: 48px;
				margin-right: 23px;
				height: 28px;

				.el-input__inner {
					height: 28px;
				}
			}
		}

	}

	.el-pagination[scenatorStyle="pagination_full"] {
		display: flex;
		align-items: center;
		margin: 0;
		border-top: none;
		box-sizing: border-box;

		.el-pagination__sizes {
			order: 2;

			input {
				font-size: $font-size;
				color: $Color16;
				height: 28px;
				border-radius: 2px;
			}
		}

		.el-pagination__jump {
			order: 3;
			margin-left: 0;
			height: fit-content;
			line-height: 28px;
			font-size: $font-size;

			input {
				height: 28px;
				border-radius: 2px;
				width: 48px;
			}

			.el-pagination__editor {
				vertical-align: bottom;
			}
		}
	}


	// 分页选择下拉框样式
	// .scenator_selectPopper {
	// 	&.el-select-dropdown {
	// 		border: none;
	// 		box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px $Color12;
	// 	}

	// 	.el-select-dropdown__list {
	// 		padding: 0;
	// 		border-radius: 4px;
	// 		background: $Color6;
	// 		box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px $Color12;

	// 		.el-select-dropdown__item {
	// 			height: 32px;
	// 			line-height: 32px;
	// 			color: $Color16;
	// 			padding: 0 16px;

	// 			.el-checkbox__input {

	// 				.el-checkbox__inner,
	// 				&.is-checked .el-checkbox__inner {
	// 					border: 1px solid $Color11;
	// 					background: $transparent;

	// 					&::after {
	// 						border-color: $Color1;
	// 					}
	// 				}
	// 			}
	// 		}
	// 	}

	// 	.el-select-dropdown__item.selected,
	// 	&.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
	// 		color: $Color16;
	// 		box-shadow: inset 0px -1px 0px 0px $Color1;
	// 		background-color: $Color3;

	// 		.el-checkbox__label {
	// 			color: $Color16;
	// 		}
	// 	}

	// 	&.el-select-dropdown.is-multiple {
	// 		.el-select-dropdown__list {
	// 			.el-select-dropdown__item {
	// 				padding: 0 16px 0 8px;

	// 				.el-checkbox {
	// 					padding: 0;

	// 					.el-checkbox__label {
	// 						padding-left: 8px;
	// 					}
	// 				}
	// 			}
	// 		}
	// 	}

	// 	.el-select-dropdown__item.hover,
	// 	.el-select-dropdown__item:hover {
	// 		background-color: $Color15;
	// 	}

	// 	&.el-popper[x-placement^=top] .popper__arrow,
	// 	&.el-popper[x-placement^=top] .popper__arrow::after {
	// 		border-top-color: $Color6;
	// 		display: none;
	// 	}

	// 	&.el-popper[x-placement^=bottom] .popper__arrow,
	// 	&.el-popper[x-placement^=bottom] .popper__arrow::after {
	// 		border-bottom-color: $Color6;
	// 		display: none;
	// 	}
	// }

	/*#endregion*/

	/*#region 页签tab*/
	.el-tabs--card {
		.el-tabs__header {
			// border-bottom: 1px solid $Color12;
			box-shadow: inset 0px -1px 0px 0px $Color13;
			border-bottom: 0;
			height: 44px;
			line-height: 44px;

			.el-tabs__nav-wrap::after {
				background: none;
			}

			.el-tabs__active-bar {
				background-color: $Color1;
			}

			.el-tabs__item.is-active {
				color: $Color1;
			}
		}

		.el-tabs__item {
			color: $Color16;
			// 被elment或其他样式影响 padding
			padding: 0;
			height: 100%;
			line-height: 100%;
			text-align: center;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			margin-right: 24px;
			background-color: transparent;
			// display: flex;
			// align-items: center;
			// justify-content: center;
			// flex-shrink: 0;
			display: inline-block;
			line-height: inherit;
			border-bottom: 0;

			// > div {
			// 	display: inline-block;
			// 	vertical-align: super;
			// }
			.el-icon-close {
				position: unset;
				display: inline-block;
			}
		}

		&.el-tabs--top>.el-tabs__header .el-tabs__item {
			transition: width 0.3s;

			&.is-active.is-closable,
			&:nth-child(2),
			&:last-child {
				padding-left: 0;
				padding-right: 0;
			}

			&:last-child {
				margin-right: 0;
			}
		}

		&>.el-tabs__header .el-tabs__item {
			border-left: 1px solid transparent;
			position: relative;
		}

		&>.el-tabs__header {

			.el-tabs__nav-wrap,
			.el-tabs__nav-scroll {
				height: 100%;

				.el-tabs__nav {
					// border: 1px solid transparent;
					border: 0;
					background-color: transparent;
					// display: flex;
					height: 100%;
				}
			}
		}

		&>.el-tabs__header .el-tabs__item.is-active {
			&::after {
				content: "";
				width: 100%;
				height: 3px;
				background: $Color1;
				display: block;
				position: absolute;
				bottom: 0;
				left: 0;
				border-top-left-radius: 2px;
				border-top-right-radius: 2px;
			}
		}

		&>.el-tabs__header .el-tabs__item.is-closable:hover {
			padding-left: 0px;
			padding-right: 0px;
		}

		.el-tabs__item:hover {
			padding: 0px;
			color: $Color1;
		}

		.el-table__expand-icon {
			height: 20px;
			width: 20px;
		}

		.el-table__expand-icon>.el-icon {
			height: 20px;
			width: 20px;
			left: 0px;
			top: 0px;
			margin-left: 0px;
			margin-top: 0px;
		}

		.el-table__row.expanded {
			td.el-table__cell {
				border: rgba(0, 0, 0, 0);
			}
		}

		// 左右按钮盒子间距
		.el-tabs__nav-next,
		.el-tabs__nav-prev {
			width: 32px;
			display: flex;
			// display: none;
			height: 100%;
			justify-content: center;
			align-items: center;
		}

		.el-tabs__nav-prev {
			box-shadow: 1px 0px 4px 0px rgba(0, 0, 0, 0.2);
		}

		.el-tabs__nav-next {
			box-shadow: -1px 0px 4px 0px rgba(0, 0, 0, 0.2)
		}

		// 左右按钮
		.el-icon-arrow-left,
		.el-icon-arrow-right {
			width: 20px;
			height: 20px;
			border-radius: 2px;
			border: 1px solid transparent;
			@extend .circulArarrowRightIcon;
		}

		.el-icon-arrow-left {
			@extend .circulArarrowLeftIcon;
		}

		.el-icon-arrow-left:before,
		.el-icon-arrow-right:before {
			content: " ";
		}

		.el-icon-arrow-right:hover,
		.el-icon-arrow-left:hover {
			background-color: $primaryPlaceholderColor;
			border: 1px solid $Color1;
		}

		.el-icon-arrow-right:active,
		.el-icon-arrow-left:active {
			background-color: $Color1;
			border: 1px solid $Color1;
			@extend .circulArarrowRightActiveIcon;
		}

		.el-icon-arrow-left:active {
			@extend .circulArarrowLeftActiveIcon;
		}

		.el-icon-plus {
			width: 20px;
			height: 20px;
			border: 1px solid transparent;
			@extend .circulAddIcon;
			transform: scale(1);
		}

		.el-icon-plus::before {
			content: " ";
		}

		.el-icon-plus:hover {
			background-color: $primaryPlaceholderColor;
			border-radius: 2px;
			border: 1px solid $Color1;
		}

		.el-icon-plus:active {
			background-color: $Color1;
			border-radius: 2px;
			border: 1px solid $Color1;
			@extend .circulAddActiveIcon;
		}

		// 添加按钮盒子
		.el-tabs__new-tab {
			width: 32px;
			height: 100%;
			margin: 0px;
			display: flex;
			justify-content: center;
			align-items: center;
			border: 0px;
			border-radius: 0px;
			border-left: $Color12 1px solid;
		}

		// tab页数
		.el-tabs__nav-wrap.is-scrollable {
			// padding: 0 34px 0 32px;
			// padding: 0;
			padding: 0 32px;
		}


		&>.el-tabs__header .el-tabs__item.is-closable .el-icon-close {
			@extend .removeIcon;
			padding: 0px;
			height: 16px;
			width: 16px;
			background-size: 16px 16px;
			border: 1px solid transparent;
			box-sizing: border-box;
			border-radius: 2px;
			cursor: pointer;
		}

		&>.el-tabs__header .el-tabs__item.is-closable .el-icon-close:hover {
			border-color: $Color1;
			background-color: $primaryPlaceholderColor;
		}

		&>.el-tabs__header .el-tabs__item.is-closable .el-icon-close:active {
			border-color: $Color1;
			background-color: $Color1;
			@extend .removeActiveIcon;
		}

		.el-tabs__item .el-icon-close {
			display: none;
		}

		.el-tabs__header {

			.el-tabs__item.is-closable:hover .el-icon-close,
			.el-tabs__item.is-active.is-closable .el-icon-close {
				position: static;
				// display: block;
				display: inline-block;
				width: 16px;
				height: 16px;
				vertical-align: middle;
				margin-left: 4px;
			}
		}

		.el-icon-close:hover {
			border-radius: 2px;
		}

		.el-icon-close:before {
			content: " ";
		}

		&[scenatorStyle="scenator_tabs_auto"],
		&[scenatorStyle="scenator_tabs"] {
			border: 0px;

			&.el-tabs--top>.el-tabs__header .el-tabs__item {
				// &:nth-child(2), &:last-child {
				// 	padding-left: 16px;
				// 	padding-right: 16px;
				// }
				// &.is-active.is-closable, &.is-closable:hover {
				// 	padding-left: 16px;
				// 	padding-right: 28px;
				// }
			}

			&>.el-tabs__header .el-tabs__item.is-closable .el-icon-close {
				position: absolute;
				right: 8px;
				top: 50%;
				transform: translateY(-50%);
			}

			.el-tabs__header {
				.el-tabs__item {
					width: 216px;
					max-width: 216px;
					min-width: 114px;
					position: relative;
					padding: 0 16px;
					margin: 0;
					background-color: $Color7;

					&.is-closable {

						&.is-active,
						&:hover {
							padding-right: 28px;
						}
					}

					&:nth-child(n) {
						&:before {
							content: '';
							width: 1px;
							height: 16px;
							background: $Color12;
							position: absolute;
							left: 0px;
							top: 50%;
							margin-top: -8px;
						}
					}

					&:nth-of-type(1) {
						&:before {
							opacity: 0;
						}
					}

					&:hover {
						font-size: $font-size;
						font-weight: 400;
						color: $Color1;
					}
				}
			}

			.el-tabs__nav-scroll {
				background: $Color7;
				border-bottom: 1px solid $Color13;
				box-shadow: inset 0px -1px 0px 0px $Color13;

				.el-tabs__nav {
					border: 0;
					// border-bottom: 1px solid $Color12;
					transform: none !important;
					overflow: hidden;
					width: 100%;
					display: flex;
				}
			}

			&>.el-tabs__header .el-tabs__item.is-active {
				background: $Color6;

				&:before {
					opacity: 0;
				}

				&+.el-tabs__item {
					&:before {
						opacity: 0;
					}
				}
			}

			&>.el-tabs__header .el-tabs__nav {
				background-color: $Color7;
			}

			.el-tabs__nav-next,
			.el-tabs__nav-prev {
				display: none;
			}
		}

		&[scenatorStyle="scenator_tabs"] {
			&.el-tabs--top>.el-tabs__header .el-tabs__item {
				transition: padding .3s;

				&:nth-child(2),
				&:last-child {
					padding-left: 16px;
					padding-right: 16px;
				}

				&.is-active.is-closable,
				&.is-closable:hover {
					padding-left: 16px;
					padding-right: 28px;
				}
			}
		}

		&[scenatorStyle="scenator_tabs_auto"] {
			.el-tabs__header {
				.el-tabs__item {
					padding: 0;
					width: auto;
					min-width: auto;
					max-width: none;
					flex: 1;

					&.is-closable {

						&.is-active,
						&:hover {
							padding-right: 0;

							div {
								padding-right: 28px;
							}
						}
					}

					div {
						padding: 0 16px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						transition: padding .3s;
					}
				}
			}
		}
	}

	// scenator 原生Tab样式 目前只改hover颜色
	.scenator_tab {
		display: flex;
		justify-content: center;
		text-align: center;
		font-size: $font-size;
		border-bottom: 1px solid $Color13;

		.scenator_tabContent {
			color: $Color16;
			padding-bottom: 7px;
			margin: 0 12px;
			cursor: pointer;
			position: relative;

			&:hover {
				color: $Color1;
			}
		}

		.scenator_tabContent.select {
			color: $Color1;
			&::after {
				content: "";
				width: 100%;
				height: 3px;
				background: $Color1;
				display: block;
				position: absolute;
				bottom: 0;
				left: 0;
				border-top-left-radius: 2px;
				border-top-right-radius: 2px;
			}
		}
	}

	.scenator-tabs {
		width: 100%;
		overflow-x: hidden;
		background: $Color7;
		box-shadow: inset 0px -1px 0px 0px $Color13;

		.container-item-tabs {
			width: 100%;
			background: $Color7;
			display: flex;
			overflow: hidden;
			white-space: nowrap;
			border-bottom: 1px solid $Color13;
			box-shadow: inset 0px -1px 0px 0px $Color13;

			// 悬浮的页签
			.container-item-tab {
				color: $Color17;
				height: 44px;
				line-height: 44px;
				text-align: center;
				position: relative;
				display: block;
				width: 216px;
				min-width: 114px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;

				.container-item-title {
					color: $Color17;
					font-weight: 400;
					font-size: $font-size;
					display: inline-block;
					width: 100%;
					padding: 0 16px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					transition: padding .3s;
				}

				.container-item-close-tab {
					width: 16px;
					height: 16px;
					position: absolute;
					right: 8px;
					top: 50%;
					transform: translateY(-50%);
					display: none;
				}

				.container-item-tab-separator,
				&.container-item-active>.container-item-tab-separator,
				&.container-item-active+.container-item-tab>.container-item-tab-separator {
					width: 1px;
					height: 16px;
					background: $Color12;
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
				}

				&:first-child>.container-item-tab-separator {
					display: none;
				}

				&:hover,
				&.container-item-active {
					.container-item-title {
						color: $Color1;
					}

					&.has-close {
						.container-item-title {
							padding-right: 28px;
						}

						.container-item-close-tab {
							display: block;
						}
					}
				}

				&.container-item-active {
					background: $Color6;
					&::after {
						content: "";
						width: 100%;
						height: 3px;
						background: $Color1;
						display: block;
						position: absolute;
						bottom: 0;
						left: 0;
						border-top-left-radius: 2px;
						border-top-right-radius: 2px;
					}
				}
			}

			&:nth-child(n) {
				&:before {
					content: '';
					width: 1px;
					height: 16px;
					position: absolute;
					left: 0px;
					top: 50%;
					margin-top: -8px;
				}
			}

			&:nth-of-type(1) {
				&:before {
					opacity: 0;
				}
			}
		}

		// 平分宽度
		&.autoWidth {
			.container-item-tabs {
				display: flex;

				.container-item-tab {
					display: block;
					min-width: 80px;
					max-width: none;
					flex: 1;
				}
			}
		}

		.container-item-active {
			background: $Color6;
			color: $Color1;

			&:before {
				opacity: 0;
			}

			&+.container-item-tab {
				&:before {
					opacity: 0;
				}
			}
		}
	}

	/*#endregion*/

	/*#region 标签*/

	.el-tag {
		width: auto;
		background: $Color7;
		color: $Color16;
		border-color: rgba(0, 0, 0, 0);
		height: 24px;
		padding: 0 8px;
		line-height: 24px;
		font-size: $font-size;
		border-radius: 2px;
		box-sizing: border-box;
		white-space: nowrap;
	}

	.el-tag--tagsIcon {
		height: 32px;
		line-height: 32px;
		padding: 0;
		background: rgba(0, 0, 0, 0);

		&:before {
			content: url($ggbIcon);
			width: 32px;
			height: 32px;
			vertical-align: top;
		}
	}

	.el-tag--tagsLine {
		padding: 0;
		background: $Color28;
		position: relative;
		height: 24px;
		padding: 0 8px;
		line-height: 24px;

		&:before {
			content: '';
			width: 4px;
			height: 24px;
			background: $Color20;
			border-radius: 2px 0px 0px 2px;
			position: absolute;
			left: -4px;
			top: -1px;
		}
	}

	/*#endregion*/

	/*#region Menu菜单*/

	.scenatorToolTip {
		position: relative;

		.scenatorTooltipContent.scenatorStyleMenu {
			background: $Color6;
			border-radius: 4px;
			padding: 0px;
			width: auto;
			box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px $Color12;
			position: absolute;

			li {
				display: block;
				line-height: 44px;
				padding: 0 16px;
				cursor: pointer;
			}

			li:hover {
				background-color: $Color15;
			}

			li.select {
				background-color: $Color3;
				box-shadow: inset 0px -1px 0px 0px $Color1;
			}

			&:before {
				content: "";
				position: absolute;
				top: calc(100% + 1px);
				left: 50%;
				margin-left: -5px;
				border-width: 5px;
				border-style: solid;
				border-color: rgba($Color6, 0.3) transparent transparent transparent;
			}

			&:after {
				content: "";
				position: absolute;
				top: 100%;
				left: 50%;
				margin-left: -5px;
				border-width: 5px;
				border-style: solid;
				border-color: $Color6 transparent transparent transparent;
			}
		}
	}

	// 默认菜单
	.scenatorMenu {
		height: 100%;
		background: $Color6;
		border-radius: 4px;
		box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px $Color12;
		display: inline-block;
		padding-left: 0;

		li {
			color: $Color16;
			line-height: 32px;
			padding: 0 16px;
			cursor: pointer;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			position: relative;
		}

		li:hover {
			background-color: $Color15;
		}

		.witoutactive {
			color: $Color1;
			background-color: $Color6;
			box-shadow: inset 0px -1px 0px 0px $Color1;
		}
	}

	.menu {
		margin-top: 4px;
	}

	.easyui-menu.menu {
		margin-top: 0;
	}

	// easyui右键菜单
	.easyui-menu.menu,
	.menu {
		background-color: $Color6;
		border: none;
		color: $Color16;
		padding: 0;
		width: auto !important;
		// border: 1px solid $Color12;
		box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px $Color12;
		border-radius: 4px;

		.menu-item {
			width: 100%;
			border: none;
			display: flex;
			align-items: center;
			justify-content: space-between;
			line-height: 32px;

			.menu-text {
				height: 100% !important;
				padding: 0 16px;
				color: $Color16;
				line-height: inherit !important;
			}

			.menu-rightarrow {
				// position: static;
				// margin: 0;
				// margin-right: 16px;
				// position: unset;
				position: static;
				float: right;
				margin: 0 16px;
				background: url($arrowUpIcon) !important;
				transform: rotate(90deg) !important;
			}

			&:hover {
				background-color: $Color15;
			}
		}

		.menu-active {
			border: none;
			color: $Color6;
			// background-color: $Color1;
			background-color: $Color15;
			border-radius: 0;
		}

		.menu-line {
			display: none;
		}
	}

	.menu-shadow {
		display: none !important;
	}

	/*#endregion*/

	/*#region jqgrid-属性分组栏背景颜色*/

	.ui-jqgrid-btable {

		// tr,
		// th,
		// td {
		// 	background: transparent;
		// }
        tr{
            border: 1px solid $Color12;
        }
        tr.jqgroup{
            &::before{
                display: inline-block;
                content: '';
                width: 100%;
                height: 1px;
                background-color: $Color12;
                position: absolute;
                left: 0;
                bottom: -1px;
            }
        }
        tr.jqgrow{
            td{
              background: transparent !important;  
            }
            &::before{
                display: inline-block;
                content: '';
                width: 1px;
                height: 100%;
                background-color: $Color12;
                position: absolute;
                left: 128px;
                top: 0;
            }
            &::after{
                display: inline-block;
                content: '';
                width: 100%;
                height: 1px;
                background-color: $Color12;
                position: absolute;
                left: 0px;
                bottom: -1px;
            }
        }
		tr.jqgroup.ui-state-hover,
		tr.jqgroup.ui-state-focus,
		tr.jqgroup.ui-state-highlight {
			background-color: rgba($Color16, 0.03);
		}
        tr.jqgrow.ui-state-hover,
		tr.jqgrow.ui-state-focus,
		tr.jqgrow.ui-state-highlight {
			background-color: rgba($Color6, 0.4);
		}
	}

	.groupBackgroundColor {
		background-color: $Color10;
		border: 1px solid $Color13 !important;

		&:hover,
		&:visited {
			border: 1px solid $Color1;
			background: $Color3;
		}
	}

	/*#endregion*/
}

.scene-content {
	background-color: $Color6;
}


@keyframes fadeIn {
	0% {
		background-color: rgba($Color3, 0.01)
	}

	10% {
		background-color: rgba($Color3, 0.02)
	}

	20% {
		background-color: rgba($Color3, 0.03)
	}

	30% {
		background-color: rgba($Color3, 0.04)
	}

	40% {
		background-color: rgba($Color3, 0.05)
	}

	50% {
		background-color: rgba($Color3, 0.06)
	}

	60% {
		background-color: rgba($Color3, 0.07)
	}

	70% {
		background-color: rgba($Color3, 0.08)
	}

	80% {
		background-color: rgba($Color3, 0.09)
	}

	90% {
		background-color: rgba($Color3, 0.1)
	}

	100% {
		background-color: rgba($Color3, 0.12)
	}
}

@keyframes fadeOut {
	0% {
		background-color: rgba($Color3, 0.12)
	}

	10% {
		background-color: rgba($Color3, 0.1)
	}

	20% {
		background-color: rgba($Color3, 0.08)
	}

	30% {
		background-color: rgba($Color3, 0.07)
	}

	40% {
		background-color: rgba($Color3, 0.06)
	}

	50% {
		background-color: rgba($Color3, 0.05)
	}

	60% {
		background-color: rgba($Color3, 0.04)
	}

	70% {
		background-color: rgba($Color3, 0.03)
	}

	80% {
		background-color: rgba($Color3, 0.02)
	}

	90% {
		background-color: rgba($Color3, 0.01)
	}

	100% {
		background-color: rgba($Color3, 0)
	}
}
// 2_int.scss
$font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";

body,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
blockquote,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
legend,
button,
input,
textarea,
th,
td {
  margin: 0;
  padding: 0;
}

body {
  padding: 0 !important;
}

body,
button,
input,
select,
textarea,
a,
div,
span,
p,
th,
td,
li
{
  //默认字体大小
  font-size: 14px;
  font-family: $font-family; 
  font-weight: 400;
}

address,
cite,
dfn,
em,
var {
  font-style: normal;
}

code,
kbd,
pre,
samp {
  font-family: $font-family;
}

small {
  font-size: 12px;
}

ul,
ol {
  list-style: none;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

sup {
  vertical-align: text-top;
}

sub {
  vertical-align: text-bottom;
}

legend {
  color: #000;
}

fieldset,
img {
  border: 0;
}

button,
input,
select,
textarea {
  font-size: 100%;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}
// 3_iconButton.scss
// 图标数据
$data: (
    (
        "iconUrl": $treeFile,
        "iconActiveUrl": $treeFile,
        "name": "dot"
    ),
    (
        "iconUrl": $removeIcon,
        "iconActiveUrl": $removeActiveIcon,
        "name": "remove"
    ),
    (
        "iconUrl": $arrowUpIcon,
        "iconActiveUrl": $arrowUpActiveIcon,
        "name": "arrowUp"
    ),
    (
        "iconUrl": $editIcon,
        "iconActiveUrl": $editActiveIcon,
        "name": "edit"
    ),
    (
        "iconUrl": $deleteIcon,
        "iconActiveUrl": $deleteActiveIcon,
        "name": "delete"
    ),
    (
        "iconUrl": $setingIcon,
        "iconActiveUrl": $setingActiveIcon,
        "name": "seting"
    ),
    (
        "iconUrl": $magnifyIcon,
        "iconActiveUrl": $magnifyActiveIcon,
        "name": "magnify"
    ),
    (
        "iconUrl": $reduceIcon,
        "iconActiveUrl": $reduceActiveIcon,
        "name": "reduce"
    ),
    (
        "iconUrl": $moreIcon,
        "iconActiveUrl": $moreActiveIcon,
        "name": "more"
    ),
    (
        "iconUrl": $circulAddIcon,
        "iconActiveUrl": $circulAddActiveIcon,
        "name": "circulAdd"
    ),
    (
        "iconUrl": $circulMinusIcon,
        "iconActiveUrl": $circulMinussActiveIcon,
        "name": "circulMinus"
    ),
    (
        "iconUrl": $expendIcon,
        "iconActiveUrl": $expendActiveIcon,
        "name": "expend"
    ),
    (
        "iconUrl": $shrinkIcon,
        "iconActiveUrl": $shrinkActiveIcon,
        "name": "shrink"
    ),
    (
        "iconUrl": $circulArarrowRightIcon,
        "iconActiveUrl": $circulArarrowRightActiveIcon,
        "name": "circulArarrowRight"
    ),
    (
        "iconUrl": $circulArarrowLeftIcon,
        "iconActiveUrl": $circulArarrowLeftActiveIcon,
        "name": "circulArarrowLeft"
    ),
    (
        "iconUrl": $saveIcon,
        "iconActiveUrl": $saveActiveIcon,
        "name": "save"
    ),
	(
		"iconUrl": $askIcon,
        "iconActiveUrl": $askIcon,
        "name": "ask"
	),
	(
		"iconUrl": $successInconImg,
        "iconActiveUrl": $successInconImg,
        "name": "success"
	),
	(
		"iconUrl": $infoInconImg,
        "iconActiveUrl": $infoInconImg,
        "name": "info"
	),
	(
		"iconUrl": $warnInconImg,
        "iconActiveUrl": $warnInconImg,
        "name": "warn"
	),
	(
		"iconUrl": $errorInconImg,
        "iconActiveUrl": $errorInconImg,
        "name": "error"
	),
    (
		"iconUrl": $repeatIcon,
        "iconActiveUrl": $repeatIcon,
        "name": "repeat"
	),
    (
        "iconUrl": $positionIcon,
        "iconActiveUrl": $positionActiveIcon,
        "name": "position"
    ),
    (
        "iconUrl": $dateIcon,
        "iconActiveUrl": $dateIconActive,
        "name": "date"
    ),
    (
        "iconUrl": $drawIcon,
        "iconActiveUrl": $drawIcon,
        "name": "draw"
    ),
    (
        "iconUrl": $packUpIcon,
        "iconActiveUrl": $packUpActiveIcon,
        "name": "packUp"
    )
);

/**
	图标按钮
	iconBtn_：图标前缀
	primary: 系统主题色
**/
.scenatorStyle .iconBtn {
    &:hover{
        background-color: $primaryPlaceholderColor;
        border: 1px solid $Color1;
    }
    &:active, &.active{
        background-color: $Color1;
        border: 1px solid $Color1;
    }
}
@for $i from 1 through length($data) {
	$item: nth($data, $i);
	.scenatorStyle .img_#{map-get($item, name)}{
		background: url(map-get($item, iconUrl)) no-repeat center;
		background-size: 100% 100%;
	}
	.scenatorStyle .iconBtn_#{map-get($item, name)}{
		width: 20px;
		height: 20px;
		background: url(map-get($item, iconUrl)) no-repeat center;
		background-size: 16px 16px;
		cursor: pointer;
		margin: 0px;
    	padding: 0px;
		box-sizing: border-box;
		border-radius: 2px;
		&:hover{
			background-color: $primaryPlaceholderColor;
			border: 1px solid $Color1;
		}
		&:active{
			background: url(map-get($item, iconActiveUrl)) no-repeat center;
			background-color: $Color1;
			border: 1px solid $Color1;
		}
	}
}

/**
	img_：图标
	dot：圆点
	remove: 删除图标（主题色）
**/
@for $i from 1 through length($data) {
	$item: nth($data, $i);
	.scenatorStyle .img_#{map-get($item, name)}{
		background: url(map-get($item, iconUrl)) no-repeat center;
		background-size: 100% 100%;
	}
}// 4_basics.scss

// 5_message.scss
.panel.messager-extend-success, .panel.messager-extend-info, .panel.messager-extend-error, .panel.messager-extend-warning{
    overflow: visible;
    .messager-body{
        min-width: 192px;
        max-width: 536px;
        margin: 0;
        padding: 8px 16px;
        line-height: 14px;
        font-size: 14px;
        border: none;
        background-color: $Color6;
        -webkit-box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        .messager-icon{
            position: static;
            flex-shrink: 0;
            margin: 8px 8px 8px 0;
        }
        .messager-msg{
            max-width: 480px;
            display: inline-block;
            line-height: 20px;
            color: $Color16;
            padding: 5px 0 7px 0;
        }
        .messager-success.messager-icon{
            background: url($successInconImg) no-repeat center;
        }
        .messager-info.messager-icon{
            background: url($infoInconImg) no-repeat center;
        }
        .messager-error.messager-icon{
            background: url($errorInconImg) no-repeat center;
        }
        .messager-warning.messager-icon{
            background: url($warnInconImg) no-repeat center;
        }
        // 关闭按钮
        .icon-close{
            position: static;
            background: none;
            width: 74px;
            height: 32px;
            font-size: 14px;
            line-height: 32px;
            margin-left: 5px;
            &:hover{
                background: none;
                &::after{
                    background: $Color7;
                    border-radius: 2px;
                    border: 1px solid $Color1;
                }
            }
            &::after{
                display: inline-block;
                content: "知道了";
                width: 74px;
                height: 32px;
                line-height: 32px;
                text-align: center;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
                font-weight: 400;
                color: $Color1;
            }
            &:active{
                &::after{
                    color: $Color6;
                    background: $Color2;
                    border-radius: 2px;
                }
            }
        }
    }
}

// easyui 下拉框
.scenatorStyle {
	.textbox.combo {
		border-radius: 2px;
		border: 1px solid $Color11;
	}

	.textbox:hover,
	.textbox-focused.textbox {
		border-color: $Color1;
	}

	.combo-arrow,
	.textbox:hover .combo-arrow {
		width: 32px !important;
		height: 32px;
		background: url($treeOpen) no-repeat center;
		opacity: 1;
	}

	.textbox-focused .combo-arrow,
	.textbox-focused.textbox:hover .combo-arrow {
		background: url($arrowUpActiveIcon) no-repeat center;
		transform: rotateZ(180deg);
		background-color: $Color1;
	}

	.textbox:hover .combo-arrow {
		background-color: $Color3;
	}

	.textbox-text {
		height: 32px;
		font-size: 14px;
	}

	.combo-panel {
		max-height: 192px;
		background: $Color6;
		border-radius: 4px;
		box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px $Color12;
        border-color: $Color12;

		.combobox-item {
			color: $Color16;
			font-size: 14px;
			padding: 6px 16px;
		}

		.combobox-item.combobox-item-selected {
			background: $Color4;
			box-shadow: inset 0px -1px 0px 0px $Color1;
			color: $Color16;
		}

		.combobox-item.combobox-item-hover {
			background: $Color15;
		}
	}
}

/*#region easyui滑动条文字样式*/
.slider-rulelabel span {
	color: $Color17;
}
/*#endregion*/// 6_window.scss
// 模态框背景色
.window-mask{
	background: #000;
    opacity: 0.5;
}
.scenatorStyle{
    .scenator_easyUI_window.window{
        background: $Color6;
        border-radius: 2px;
        border: 0px;
        padding: 0px;
        &.ask .panel-title{
            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px $Color29
        }
        &.warn .panel-title{
            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px $Color25;
        }
        &.error .panel-title{
            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px $Color20;
        }
        &.info .panel-title{
            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px $Color29;
        }
        &.ask, &.warn, &.error, &.info{
            .panel-tool{
                display: none;
            }
        }
        .panel-icon{
            width: 16px;
            height: 16px;
            left: 16px;
            margin-top: -8px;
        }
        .panel-title.panel-with-icon{
            padding-left: 40px;
            text-indent: 0px;
        }
        .window-body{
            background: $Color6;
            border-radius: 2px;
            border: 0px;
            padding: 0px;
            height: auto !important;
        }
        .panel-title{
            width: 100%;
            height: 44px;
            line-height: 44px;
            box-shadow: 0px 1px 0px 0px $Color12, 0px 0px 4px 0px rgba(0, 0, 0, 0.15);
            font-size: 16px;
            text-align: left;
            text-indent: 16px;
            color: $Color16;
            font-family: $font-family;
            font-weight: $font-weight;
        }
        .window-header{
            padding: 0px;
            display: flex;
            align-items: center;
            border: 0px;
            .panel-tool{
                width: 20px;
                height: 20px;
                margin-right: 16px;
                right: 0px;
                margin-top: -10px;
            }
        }
        // 权重小一些去掉关闭按钮默认背景图
        .panel-tool-close{
            background: none;
            width: 100%;
            height: 100%;
            margin: 0px;
        }
        .scenator_easyUI_window_content{
            padding: 16px;
        }
        &.confirmPrompt{
            .scenator_easyUI_window_content{
                padding: 24px 16px;
                color: $Color16;
            }
        }
        .scenator_easyUI_window_footer{
            height: 56px;
            width: 100%;
            box-shadow: inset 0px 1px 0px 0px $Color12;
            display: flex;
            align-items: center;
            padding: 0px 16px;
            justify-content: flex-end;
            background-color: $Color6;
        }
    }
}
// 7_toolTip.scss
/* -----------------------------场景导航样式 start------------------------------*/ 

#system_body{
	display: flex;
	#nav_menu_container{
		width: $leftMenuWidth;
		position: relative;
		overflow: hidden;
		background-color: $Color6;
		.top_area{
			height: calc(100% - 44px);
			.nav_bottom {
				background-color: $Color6;
			}
		}
		.bottom_area {
			height: 44px;
			box-shadow: inset 1px 1px 0px 0px $Color12;
		}
	}
	#scene_container {
		width: calc(100% - #{$leftMenuWidth});
		border-left: 1px solid $Color12;
	}
	&.scene_shrink{
		#nav_menu_container{
			width: 48px;
			overflow: hidden;
		}
		#scene_container{
			width: calc(100% - 48px); 
		}
		.scene-nav-workspace-selected[level="1"]>.workspace_expend {
			background: $Color4;
			box-shadow: inset 0px -1px 0px 0px $Color1;
			&:hover{
				background: $Color3;
			}
		}
		.scene-nav-list{
			width: $leftMenuWidth;
		}
	}
}

.scenatorStyle{
	.sceneNavigator-structure {
		height: 44px;
		box-shadow: inset 0px -1px 0px 0px $Color12;
		display: flex;
		position: relative;
		span,
		div,
		p {
			color: $Color16;
		}
		.workspace-workspace{
			background: url(/Scenator/resources/static/images/scenator_structureIcon.svg) no-repeat center center;
		}
		.workspace-recover {
			position: absolute;
			right: 0;
		}
	
		&.nav-normal {
			.workspace-title {
				width: calc(100% - 96px);
			}
		}
	
		&.nav-seting {
			.workspace-title {
				width: calc(100% - 136px);
			}
		}
		.workspace-title{
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			line-height: 44px;
			cursor: default;
		}
	}
	.sceneNavigator-scene {
		height: 44px;
		position: relative;
		span,
		div,
		p {
			color: $Color16;
		}
		.menu-item-content{
			height: 44px;
			line-height: 44px;
			position: relative;
		}
	
		.nav-menu-img{
			width: 16px;
			height: 16px;
			position: absolute;
			top: 50%;
			left: 15px;
			transform: translateY(-50%);
		}
	
		.nav-menu-text {
			margin-left: 48px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: calc(100% - 48px - 36px);
		}
	
		&.scene-edit .nav-menu-text{
			max-width: calc(100% - 48px - 36px);
		}
	
		&:hover {
			background-color: $Color15;
		}
		&.selected {
			background-color: $Color3;
			box-shadow: inset 0px -1px 0px 0px $Color1;
			.nav-menu-text {
				color: $Color16
			}
		}
		.editDownMenu{
			position: absolute;
			right: 8px;
			top: 50%; 
			transform: translateY(-50%);
		}
	}

	// 场景菜单拉伸按钮样式
	.stretchContainer {
		&:hover,
		&:active {
			border-right: 2px solid $Color1;
		}
	}
}



// 导航收起后，二级菜单样式
.shrinkMenuRootTitle{
	width: 100%;
}
.workspace-shrink-img{
	width: 48px;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	display: none;
}
.menu.shrinkMenu{
	min-width: 88px;
	max-width: 172px;
	&.scenatorScene-shrinkmenu {
		margin-top: 0;
	}
}
.menu.shrinkMenu .shrinkMenuRootTitle{
	height: 44px;
	padding: 0 16px;
	div{
		display: inline-block;
	}
}
.menu.shrinkMenu{
	.shrinkMenuRootTitle{
		div:first-child{
			max-width: 100%;
			text-indent: 0px;
			height: 44px;
			line-height: 44px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			cursor: default;
		}
	}
	&.with-seting .shrinkMenuRootTitle{
		div:first-child{
			max-width: calc(100% - 36px);
		}
		.iconBtn_seting{
			float: right;
			margin: 14px 0 0 16px;
		}
	}
}

.menu.shrinkMenu .menu-item{
	padding: 0 16px;
	height: 44px!important;
	.menu-text{
		padding: 0;
		height: 44px!important;
		line-height: 44px!important;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		max-width: 100%;
	}
}
.menu-item.shrinkMenuItem.with-rightarrow{
	div:first-child{
		max-width: calc(100% - 29px);
	}
	&:hover{
		background-color: $Color15;
	}
}
.menu.shrinkMenu .menu-item .menu-rightarrow{
	margin-right: 0!important;
}

// 子菜单选中时，父级高亮
#nav_menu_container .structure-property.child-selected>.workspace-title,
.menu.shrinkMenu .menu-item.shrinkMenuItem.child-selected>.menu-text,
.scenatorScene-shrinkmenu.child-selected>.shrinkMenuRootTitle>div:first-child
{
	color: $Color1;
}


/* -----------------------------场景导航样式 end------------------------------*/ 


// 原生上拉菜单
.scenatorToolTip {
	position: relative;
	display: inline-block;
	font-size: 14px;
	overflow: visible;
}

.scenatorToolTip .scenatorTooltipContent {
	width: auto;
	padding: 8px 0px;
	display: none;
	background-color: $Color6;
	font-size: 14px;
	color: $Color16;
	text-align: left;
	border-radius: 2px;
	position: absolute;
	z-index: 1;
	// bottom: 115%;
	box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px $Color12;
  // filter:progid:DXImageTransform.Microsoft.gradient(startColorStr=$Color12,endColorStr=$Color12,direction=0,strength=0);
}

.scenatorToolTip .scenatorTooltipContent::after {
	content: "";
	position: absolute;
	top: 100%;
	left: 50%;
	margin-left: -5px;
	border-width: 5px;
	border-style: solid;
	border-color: $Color6 transparent transparent transparent;
}

.scenatorToolTip .scenatorTooltipContent::before {
	content: "";
	position: absolute;
	top: calc(100% + 1px);
	left: 50%;
	margin-left: -5px;
	border-width: 5px;
	border-style: solid;
	border-color: rgba(0, 0, 0, 0.3) transparent transparent transparent;
}

.scenatorToolTip.focus .scenatorTooltipContent {
	display: block;
}


.scenatorTooltipContent li{
	// width: 100%;
	height: 44px;
	line-height: 44px;
	padding: 0 18px;
	white-space: nowrap;
	&:hover{
		background-color: $Color3;
	}
	&.select{
		box-shadow:inset 0px -1px 0px 0px $Color15;
		background-color: $Color3;
	}
}




// 场景菜单收缩样式
.scene-nav-workspace[level="1"]>.workspace_expend {
	box-shadow: inset 0px -1px 0px 0px $Color12;
}

/*------------------------scenator 内部样式 start------------------------*/

// 字体样式
#nav_menu_container {
	span,
	div,
	p {
		color: $Color16;
	}

	div.menu-item.selected>.menu-item-content>.nav-menu-text {
		color: $Color1;
	}
}

// 场景菜单收缩后的弹出菜单样式
.menu.shrinkMenu {
	background-color: $Color6;

	.menu-item.shrinkMenuItem {
		color: $Color16;

		&:hover {
			background-color: $Color15;
		}

		&.selected {
			background-color: $Color4;
			border-bottom: 1px solid $Color1;
			.menu-text{
				color: $Color1;
			}
		}

		&.menu-active {
			border-radius: 0;
		}

		.menu-text {
			color: $Color16;
		}
	}
}

.workspace-shrink-img:after {
	content: " ";
	width: 0px;
	height: 0px;
	position: absolute;
	bottom: -5px;
	right: -5px;
	border: 5px solid transparent;
	border-top: 5px solid $Color1;
	transform: rotate(-45deg);
}



// 未分组场景前的默认图标
.scene-nav-list>.menu-item .nav-menu-img {
	background-repeat: no-repeat;
	background-position: center center;
}

// 分组标题前的默认图标
.scene-nav-workspace {
	.workspace-workspace {
		background: url($structureIcon) no-repeat center center;
	}
}

// 区域分割线
.slide-line {
	background: url($areaLineIcon) no-repeat center;
}

// 编辑场景tip
.workspace-edit-tip {
	// width: 120px;
	height: 176px;
	background: $Color6;
	border-radius: 4px;
	overflow: hidden;
	li {
		color: $Color16;
		cursor: pointer;
		padding: 0 16px;

		&:hover {
			background-color: $Color15;
		}

		&.is-disabled {
			color: $Color19;
		}
	}
}

/* 收缩后的工作空间列表 */
.shrink-workspace-list {
	background: $Color6;
	box-shadow: 0px 10px 30px 2px rgba(0, 0, 0, 0.12), 0px 0px 0px 1px rgba(0, 0, 0, 0.1);
	border-radius: 0 0 4px 4px;
}

.scene-nav-workspace .scene_shrink {
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);

	.structure-property.active::after {
		border-color: transparent transparent $Color1 transparent;
	}
}

.scene-nav-workspace[level="1"]>.workspace_expend {
	box-shadow: inset 0px -1px 0px 0px $Color12;
}

.scene-nav-workspace[level="1"][shrink="open"]>.workspace_list {
	box-shadow: inset 0px -1px 0px 0px $Color12;
}

.scene-nav-workspace[level="1"].scene_shrink>.structure-property{
	cursor: pointer;
	&:hover {
		background-color: $Color15;
	}
	// &:active{
	// 	background-color: $Color1;
	// }
}

.panel-body {
	@extend .scenatorMainFontColor;
	@extend .scenatorMainBackgroundColor;
}

/* 场景菜单宽度变化后，弹出层宽度变化 */
.panel.window.float-window.open,
.panel.window.float-window.open .panel-body {
	left: $leftMenuWidth !important;
	width: calc(100vw - #{$leftMenuWidth}) !important;
}

// 编辑工作空间table
// .edit-workspace-page table th {
// 	background: $tableHeaderBackgroundColor;
// }

// .edit-workspace-page table th,
// .edit-workspace-page table td {
// 	border: 1px solid $Color12;
// }

.edit-workspace-page table tr.drag-highlight {
	background-color: $Color1;
}

.edit-workspace-btn {
	color: $Color1;

	&:hover,
	&:active {
		color: $Color1;
	}

	&.disabled {
		color: $primaryDisabledColor;
	}
}

.edit-workspace-button {
	height: 56px;
	box-shadow: inset 0px 1px 0px 0px $Color12;
}

.scene-nav-list>.menu-item {
	border-bottom: 1px solid $Color12;
}

.nav_bottom .menu-item, .top_area .menu-item {
	border-bottom: 1px solid $Color12;
}

// scenator 页签样式
.container-item>.container-header {
	background: $Color7;
	box-shadow: inset 0px -1px 0px 0px $Color13;

	.container-item-tab.container-item-active {
		background-color: $Color6;
		border-radius: 2px 2px 0px 0px;
		color: $Color1;
	}
}

// tab页下拉列表
.tabLayout-stack-dropmenu.menu {
	width: 256px !important;
	max-height: 264px;
	height: auto !important;
	overflow-y: auto!important;
	padding: 0px;
	.tabLayout-invisible-tab {
		overflow: hidden;
		// padding: 11px 16px 11px 16px;
		padding: 11px 0;
		color: $Color16;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		white-space: nowrap;
		text-overflow: ellipsis;
		cursor: pointer;
		-webkit-transition: all .3s;
		transition: all .3s;
		display: flex;
        align-items: center;
		.tab-item-title {
			flex: 1;
			// margin-right: 16px;
			padding: 0 16px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.tab-item-close-tab {
			width: 16px;
			height: 16px;
			margin-left: 8px;
			margin-right: 8px
        }
		&:hover {
			background: $Color15;
		}
        &:active{
            background: $Color4;
            box-shadow: inset 0px -1px 0px 0px $Color1; 
        }
	}
}

// 悬浮区域
.suspend {
	background-color: rgba($Color6, 0.75);
	border-radius: 2px;
	box-shadow: -1px 0px 0px 0px $Color13, 0px -1px 0px 0px $Color13, 0px 1px 0px 0px $Color13, 1px 0px 0px 0px $Color13;
}

.suspend::before {
	background-color: rgba($Color6, 0.2);
	backdrop-filter: blur(4px);
	border-radius: 2px;
}

// 悬浮的导航菜单样式
.suspend>.container-item.container-stack>.container-header {
	background: rgba($Color6, 0.75);
	box-shadow: -1px -1px 0px 0px $Color13, 1px 1px 0px 0px $Color13;
	// 悬浮的页签
	.container-item-tabs {
		background: rgba($Color6, 0.2);
		backdrop-filter: blur(4px);

		.container-item-tab {
			// border-bottom: 1px solid rgba(217, 217, 217, 1);
			border-bottom: 1px solid $Color13;

			// 悬浮的页签选中样式
			&.container-item-active {
				border-color: $Color1;
				background: $Color3;
				&::after {
					display: none;
				}
			}
		}
	}
}

.nav-menu-container .stretchContainer:hover,
.nav-menu-container .stretchContainer:active {
	border-right: 2px solid $Color1;
}

/* 分割线样式 */
.container-item>.container-header .container-item-tab>.container-item-tab-separator {
	background: $Color12;
}

// 边界线样式
.container-row>.container-items>.container-item {
	/* 只控制下面一级的样式 */
	border-top: 1px solid $Color12;
}

.rightclick-stack-menu {
	box-shadow: 0px 6px 20px 2px rgba(0, 0, 0, 0.08),
		0px 0px 0px 1px $Color12;
	border-radius: 4px;
	overflow: hidden;
	.rightClick-item {
        height: 32px;
        font-size: 14px;
        padding: 0 16px;
        line-height: 32px;
		&:hover {
			background: $Color15;
            cursor: pointer;
		}
	}
}

.area-title {
	// background: rgba($Color6, 0.4);
	@extend .scenatorOpacityHeaderBackgroundColor;
	border-bottom: 1px solid $Color13;
}

.slide-area {
	@extend .scenatorMainBackgroundColor;
}

.area-slide-container {
	box-shadow: inset -1px 0px 0px 0px $Color12, inset 1px 0px 0px 0px $Color12;
}

.workspace-edit-tip {
	box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px $Color12;
}

.side-panel {
	background-color: $Color14;
}

.shrinkMenuRootTitle {
	box-shadow: inset 0px -1px 0px 0px $Color12;
}

// 头部背景按钮样式
.switch-system-tab {
    padding: 0;
	color: $Color16;
    background: $Color6;
	box-shadow: 0px 10px 30px 0px rgba(0, 0, 0 ,0.1), 0px 0px 0px 1px $Color12;
    .perSystem:hover{
        background-color: $Color15;
    }
    // .perSystem:active{
    //     background: $Color3;
    //     box-shadow: inset 0px -1px 0px 0px $Color1;
    // }
}

.markdown>table th {
	background: $Color15;
}

.switch-system-tab .perSystem:hover {
	background: $Color15;
}

.userInformation-tab span:hover {
	background: $Color15;
}

// 头部扩展点按钮样式
// 右侧btn样式
#system_head_button .menu-item-content>.nav-menu-icon{
	&:hover{
		background-color: $Color31;
	}
	&:active{
		background-color: $Color32;
	}
}
/*------------------------scenator 内部样式 end------------------------*/


// 定义顶部小三角样式
.scenatorStyle{
	.triangle {
		&:before {
			border-color: $Color6;
		}
	
		&:after {
			border-color: $Color6;
		}
	}
}

// R3D 变色弹框背景
body .render3DColorPicker table.jPicker {
	background-color: rgba($Color6, 0.75);
}

body .render3DColorPicker table.jPicker::before {
	color: $Color16;
	background-color: rgba($Color6, 0.4);
	box-shadow: inset 0px -1px 0px 0px $Color13;
}

body .render3DColorPicker .colorSetterPickerFooter {
	border-top: 1px solid $Color13;
}
body .render3DColorPicker table.jPicker tbody tr{
    height: auto;
    background-color: transparent;
    td{
        height: auto;
        background-color: transparent;
    }
}
// 右上角innnerSwitchStytem的下拉菜单样式
.switch-system-tab {
    position: absolute;
    z-index: 100;
    width:auto;
    height: auto;
    padding: 0;
    display:block;
    background: $Color6;
    font-family: $font-family;
    color: $Color16; 
    font-size: 14px;
    cursor: default;
    border-radius: 4px;
    box-shadow: 0px 0px 30px 0px rgba(0 ,0 ,0 ,0.1), 0px 0px 0px 1px $Color12;
    .perSystem{
        position: relative;
    }
    .perSystem:hover{
        background-color: $Color15;
        z-index: 102;
    }
    .perSystem:nth-child(1){
        &:hover{
            background-color: $Color15;
            border-radius: 2px;
            z-index: 102;
        }
    }
    
    // .perSystem:active{
    //     background: $Color3;
    //     box-shadow: inset 0px -1px 0px 0px $Color1;
    // }
}
#switchSystemPanel::before{
    background-color: $Color6;
    border-color: $Color12 transparent transparent $Color12 ;
}
// 三角样式
.popoverTriangle::before{
    width: 10px;
    height: 10px;
    transform: rotate(45deg);
    z-index: 101;
    border: 1px solid;
    background-color: $Color6;
    border-color: $Color12 transparent transparent $Color12 ;
}

// 右上角userinfomation样式
.userInformation-tab{
    position: absolute;
    background: $Color6;
    color: $Color16; 
    box-shadow: 0px 0px 30px 0px rgba(0 ,0 ,0 ,0.1), 0px 0px 0px 1px $Color12;
}

.userInformation-tab span{
    display: block;
    height: 44px;
    padding: 0 16px;
    text-align:center;
    line-height: 44px;
    font-size: 14px;
    color: $Color16; 
    font-family: $font-family;
    font-weight: 400;
    cursor: pointer;
}
.userInformation-tab span{
    z-index: 103;
    position: relative;
    &:hover{
        background-color: $Color15;
        border-radius: 2px;
    }
    
}

// 空白页样式
.scenatorStyle_empty_container {
	position: absolute;
	top: calc(100% / 4);
	left: 50%;
	transform: translateX(-50%);
	.empty_text {
		margin-top: 16px;
		color: $Color17;
		font-size: 14px;
		text-align: center;
	}
	&.big_container>div{
		background: url($url + "scenatorStyle_empty_container_big.svg") no-repeat center center;
		width: 313px;
		height: 198px;
		margin: auto;
	}
	&.medium_container>div{
		background: url($url + "scenatorStyle_empty_container_medium.svg") no-repeat center center;
		width: 120px;
		height: 90px;
		margin: auto;
	}
	&.small_container>div{
		background: url($url + "scenatorStyle_empty_container_small.svg") no-repeat center center;
		width: 120px;
		height: 90px;
		margin: auto;
	}
}

/* 浮动窗口样式 */
.scenatorStyle {
	.float-window.panel {
		background-color: $Color6;
		padding: 0;
		border-radius: 2px;
		.panel-header {
			padding: 0px;
			display: flex;
			align-items: center;
			border: 0px;
			width: 100%!important;
			box-shadow: 0px 1px 0px 0px $Color13, 0px 0px 4px 0px rgba(0, 0, 0, 0.15);
		}
		&.no-header {
			.panel-header {
				display: none;
			}
			.panel-body {
				padding: 0;
			}
		}
	
		.panel-title {
			height: 40px;
			line-height: 40px;
			font-size: $font-title-size;
			text-align: left;
			text-indent: 16px;
			color: $Color16;
			font-family: $font-family;
			font-weight: $font-weight;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			width: calc(100% - 52px);
		}
		.panel-tool {
			width: 20px;
			height: 20px;
			margin-right: 16px;
			right: 0px;
			margin-top: -10px;
			.panel-tool-close{
				background: none;
			}
		}
		.panel-body{
			width: 100%!important;
			padding: 16px;
		}
		&.transparent-window{
			background-color: rgba($Color6, 0.75);
    	backdrop-filter: blur(4px);
			border: 1px solid $Color13;
			box-shadow: 1px 0px 0px 0px $Color13, 0px 1px 0px 0px $Color13, 0px 0px 1px 0px $Color13, 0px 0px 0px 1px $Color13;;
			.panel-header{
				background-color: rgba($Color6, 0.4);
			}
			.panel-body{
				background-color: transparent;
			}
		}
	}
}

