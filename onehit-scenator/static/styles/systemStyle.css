@charset "UTF-8";
body,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
blockquote,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
legend,
button,
input,
textarea,
th,
td {
  margin: 0;
  padding: 0; }

body {
  padding: 0 !important; }

body,
button,
input,
select,
textarea,
a,
div,
span,
p,
th,
td,
li {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
  font-weight: 400; }

address,
cite,
dfn,
em,
var {
  font-style: normal; }

code,
kbd,
pre,
samp {
  font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑"; }

small {
  font-size: 12px; }

ul,
ol {
  list-style: none; }

a {
  text-decoration: none; }

a:hover {
  text-decoration: underline; }

sup {
  vertical-align: text-top; }

sub {
  vertical-align: text-bottom; }

legend {
  color: #000; }

fieldset,
img {
  border: 0; }

button,
input,
select,
textarea {
  font-size: 100%; }

table {
  border-collapse: collapse;
  border-spacing: 0; }

/*#region 基本色*/
/*#endregion*/
/*#region 公用class*/
.scenatorStyle {
  /*#region 产品色*/
  /*#endregion*/
  /*#region 顶部区域*/
  /*#endregion*/
  /*#region 容器背景色*/
  /*#endregion*/
  /*#region 边框和分割线*/
  /*#endregion*/
  /*#region 文字颜色*/
  /*#endregion*/
  /*#region 语义颜色*/
  /*#endregion*/ }
  .scenatorStyle .scenatorMainBackgroundColor, .scenatorStyle .el-dialog__wrapper .el-dialog, .scenatorStyle .panel-body, .scenatorStyle .slide-area {
    background-color: #FFFFFF; }
  .scenatorStyle .scenatorOpacityBackgroundColor {
    background-color: rgba(255, 255, 255, 0.75);
    backdrop-filter: blur(4px); }
  .scenatorStyle .scenatorOpacityHeaderBackgroundColor, .scenatorStyle .area-title {
    background-color: rgba(255, 255, 255, 0.4); }
  .scenatorStyle .scenatorOpacityBorderColor {
    border-color: rgba(24, 42, 78, 0.12); }
  .scenatorStyle .scenatorBorderColor {
    border-color: #D9D9D9; }
  .scenatorStyle .text-color-active {
    color: #0854A1; }
  .scenatorStyle .background-color-active {
    background-color: #0854A1; }
  .scenatorStyle .border-color-active {
    border-color: #0854A1; }
  .scenatorStyle .text-color-clickOrSelectOrHover {
    color: #114171; }
  .scenatorStyle .list-opacity-color-hover {
    background-color: rgba(0, 129, 255, 0.12); }
  .scenatorStyle .list-primary-color-hover {
    background-color: #E5F0FA; }
  .scenatorStyle .scenatorHeaderBackgroundColor {
    background-color: #0A2440; }
  .scenatorStyle .scenatorHeaderBtnHoverColor {
    background-color: #244F7D; }
  .scenatorStyle .scenatorHeaderBtnClickColor {
    background-color: #143F6C; }
  .scenatorStyle .background-primary {
    background-color: #FFFFFF; }
  .scenatorStyle .background-table-header {
    background-color: #EFEFEF; }
  .scenatorStyle .background-table-body {
    background-color: #FFFFFF; }
  .scenatorStyle .background-table-body-secondary {
    background-color: #FAFAFA; }
  .scenatorStyle .background-table-cardAndCollapse {
    background-color: rgba(24, 42, 78, 0.06); }
  .scenatorStyle .background-table-secondary {
    background-color: #F7F7F7; }
  .scenatorStyle .background-list-hover {
    background-color: #EFF2F4; }
  .scenatorStyle .list-item:hover {
    background-color: #EFF2F4; }
  .scenatorStyle .list-item.is-checked {
    background-color: #E5F0FA;
    box-shadow: inset 0px -1px 0px 0px #0854A1; }
  .scenatorStyle .list-item.is-disabled {
    opacity: 0.4;
    cursor: not-allowed; }
    .scenatorStyle .list-item.is-disabled:hover {
      background-color: #FFFFFF; }
  .scenatorStyle .border-color-input {
    border-color: #89919A; }
  .scenatorStyle .border-color-primary {
    border-color: #D9D9D9; }
  .scenatorStyle .border-color-opacity {
    border-color: rgba(24, 42, 78, 0.12); }
  .scenatorStyle .scenatorMainFontColor, .scenatorStyle .panel-body {
    color: #182A4E; }
  .scenatorStyle .scenatorSecondFontColor {
    color: #5D697A; }
  .scenatorStyle .scenatorCoverFontColor {
    color: #94999D; }
  .scenatorStyle .scenatorDisaledFontColor {
    color: rgba(24, 42, 78, 0.4); }
  .scenatorStyle .scenatorErrorFontColor {
    color: #BB0000; }
  .scenatorStyle .scenatorSuccessFontColor {
    color: #107E3E; }
  .scenatorStyle .scenatorWhiteOrBlackFontColor {
    color: #FFFFFF; }
  .scenatorStyle .color-positive {
    color: #107E3E; }
  .scenatorStyle .color-alert {
    color: #E9730C; }
  .scenatorStyle .color-negative {
    color: #BB0000; }
  .scenatorStyle .color-information {
    color: #0A6ED1; }
  .scenatorStyle .background-positive {
    background-color: #F1FDF6; }
  .scenatorStyle .background-alert {
    background-color: #FEF7F1; }
  .scenatorStyle .background-negative {
    background-color: #FFEBEB; }
  .scenatorStyle .background-information {
    background-color: #F5FAFF; }
  .scenatorStyle .scenatorToolbox:hover {
    background-color: rgba(0, 129, 255, 0.12); }
  .scenatorStyle .jqGridHeadBackgroundColor {
    background-color: rgba(24, 42, 78, 0.03); }
  .scenatorStyle .jqGridBodyBackgroundColor {
    background-color: rgba(255, 255, 255, 0.4); }

/*#endregion*/
/*#region 图标*/
.iconBtn_noCirculArarrowRight {
  position: relative;
  top: 14px;
  float: right;
  width: 16px;
  height: 16px;
  background: url("../images/scenator_arrowUp.svg") no-repeat center;
  transform: rotate(90deg);
  background-size: 100% 100%; }

.circulArarrowRightIcon, .scenatorStyle .el-tabs--card .el-icon-arrow-left,
.scenatorStyle .el-tabs--card .el-icon-arrow-right {
  background: url("../images/scenator_circulArarrowRight.svg") no-repeat center;
  background-size: 100% 100%; }

.circulArarrowRightActiveIcon, .scenatorStyle .el-tabs--card .el-icon-arrow-right:active,
.scenatorStyle .el-tabs--card .el-icon-arrow-left:active {
  background: url("../images/scenator_circulArarrowRight_active.svg") no-repeat center;
  background-size: 100% 100%; }

.circulArarrowLeftIcon, .scenatorStyle .el-tabs--card .el-icon-arrow-left {
  background: url("../images/scenator_circulArarrowLeft.svg") no-repeat center;
  background-size: 100% 100%; }

.circulArarrowLeftActiveIcon,
.scenatorStyle .el-tabs--card .el-icon-arrow-left:active {
  background: url("../images/scenator_circulArarrowLeft_active.svg") no-repeat center;
  background-size: 100% 100%; }

.circulAddIcon, .scenatorStyle .el-tabs--card .el-icon-plus {
  background: url("../images/scenator_circulAdd.svg") no-repeat center;
  background-size: 100% 100%; }

.circulAddActiveIcon, .scenatorStyle .el-tabs--card .el-icon-plus:active {
  background: url("../images/scenator_circulAdd_active.svg") no-repeat center;
  background-size: 100% 100%; }

.removeIcon, .scenatorStyle .el-input.el-input--suffix .el-input__suffix .el-input__clear:before,
.scenatorStyle .el-picker-panel.el-input--suffix .el-input__suffix .el-input__clear:before, .scenatorStyle .el-dialog__wrapper .el-dialog__header .el-dialog__close, .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .el-icon-close {
  background: url("../images/scenator_removeIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.removeActiveIcon, .scenatorStyle .el-input.el-input--suffix .el-input__suffix:active .el-input__clear:before,
.scenatorStyle .el-picker-panel.el-input--suffix .el-input__suffix:active .el-input__clear:before, .scenatorStyle .el-dialog__wrapper .el-dialog__header .el-dialog__close:active, .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .el-icon-close:active {
  background: url("../images/scenator_removeActiveIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.dateIcon {
  background: url("../images/scenator_dateIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.dateIconActive {
  background: url("../images/scenator_dateIconActive.svg") no-repeat center;
  background-size: 100% 100%; }

.secrchIcon, .scenatorStyle .el-input.el-input--prefix .el-input__prefix .el-icon-search:before,
.scenatorStyle .el-picker-panel.el-input--prefix .el-input__prefix .el-icon-search:before {
  background: url("../images/scenator_searchIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenator_askIcon, .scenatorStyle .el-message-box.scenator_confirmPrompt.ask .el-message-box__title::before {
  background: url("../images/scenator_askIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenator_successIcon, .scenatorStyle .scenator_briefMsg.success .el-message__icon, .scenatorStyle .scenator_complexMsg.success .el-icon-info, .scenatorStyle .scenator_topMsg.success .el-icon-info {
  background: url("../images/scenator_successIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenator_warnIcon, .scenatorStyle .scenator_briefMsg.warn .el-message__icon, .scenatorStyle .scenator_complexMsg.warn .el-icon-info, .scenatorStyle .scenator_topMsg.warn .el-icon-info, .scenatorStyle .el-message-box.scenator_confirmPrompt.warn .el-message-box__title::before {
  background: url("../images/scenator_warnIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenator_infoIcon, .scenatorStyle .scenator_briefMsg.info .el-message__icon, .scenatorStyle .scenator_complexMsg.info .el-icon-info, .scenatorStyle .scenator_topMsg.info .el-icon-info, .scenatorStyle .el-message-box.scenator_confirmPrompt.info .el-message-box__title::before {
  background: url("../images/scenator_infoIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenator_errorIcon, .scenatorStyle .scenator_briefMsg.error .el-message__icon, .scenatorStyle .scenator_complexMsg.error .el-icon-info, .scenatorStyle .scenator_topMsg.error .el-icon-info, .scenatorStyle .el-message-box.scenator_confirmPrompt.error .el-message-box__title::before {
  background: url("../images/scenator_errorIncon.svg") no-repeat center;
  background-size: 100% 100%; }

/*#endregion*/
/*#region 自定义颜色变量*/
/*#endregion*/
/*#region 自定义宽度或大小边距*/
/*#endregion*/
/* -------------------------------------------样式------------------------------------------------------------*/
.scenatorStyle {
  /*#region 按钮组件*/
  /*#endregion*/
  /*#region 单选框/多选框*/
  /*#endregion*/
  /*#region 输入框*/
  /*#endregion*/
  /*#region 下拉框*/
  /*#endregion*/
  /*#region textArea*/
  /*#endregion*/
  /*#region 反馈提示*/
  /* 复杂的提示 */
  /*#endregion*/
  /*#region 弹窗*/
  /*#endregion*/
  /*#region loading*/
  /*#endregion*/
  /*#region Title*/
  /*#endregion*/
  /*#region 日期选择框*/
  /*#endregion*/
  /*#region 树结构（tree）*/
  /*#endregion*/
  /*#region 表格*/
  /*#endregion*/
  /*#region 卡片*/
  /*#endregion*/
  /*#region 列表*/
  /*endregion*/
  /*#region 下拉菜单dropdown*/
  /*#endregion*/
  /*#region tooltip*/
  /*#endregion*/
  /*#region 分页*/
  /*#endregion*/
  /*#region 页签tab*/
  /*#endregion*/
  /*#region 标签*/
  /*#endregion*/
  /*#region Menu菜单*/
  /*#endregion*/
  /*#region jqgrid-属性分组栏背景颜色*/
  /*#endregion*/ }
  .scenatorStyle ::-webkit-scrollbar-thumb {
    background-color: #B8BDC5 !important;
    border-radius: 0; }
  .scenatorStyle .el-button {
    padding: 8px 16px;
    background: transparent;
    border: 1px solid transparent;
    color: #0854A1;
    border-radius: 2px;
    font-size: 14px;
    font-weight: normal; }
    .scenatorStyle .el-button + .el-button {
      margin-left: 16px; }
    .scenatorStyle .el-button.less3word {
      width: 62px; }
      .scenatorStyle .el-button.less3word.iconBtn {
        width: 80px; }
    .scenatorStyle .el-button.iconBtn {
      padding: 7px 16px 7px 12px; }
      .scenatorStyle .el-button.iconBtn .icon {
        width: 16px;
        height: 16px; }
      .scenatorStyle .el-button.iconBtn span {
        margin-left: 8px; }
    .scenatorStyle .el-button .el-message-box__btns button:nth-child(2) {
      margin-left: 16px; }
    .scenatorStyle .el-button.el-button--main--button {
      background: #0854A1;
      border-color: #0854A1;
      color: #FFFFFF; }
      .scenatorStyle .el-button.el-button--main--button span {
        color: #FFFFFF; }
    .scenatorStyle .el-button.el-button--main--button:hover {
      background: #114171;
      border-color: #114171;
      color: #FFFFFF; }
      .scenatorStyle .el-button.el-button--main--button:hover span {
        color: #FFFFFF; }
    .scenatorStyle .el-button.el-button--main--button:active {
      background: #114171;
      border-color: #114171;
      color: #FFFFFF; }
      .scenatorStyle .el-button.el-button--main--button:active span {
        color: #FFFFFF; }
    .scenatorStyle .el-button.el-button--main--button.is-disabled, .scenatorStyle .el-button.el-button--main--button.is-disabled:hover {
      background-color: #0854A1;
      border-color: #0854A1;
      color: #FFFFFF;
      opacity: 0.4; }
      .scenatorStyle .el-button.el-button--main--button.is-disabled span, .scenatorStyle .el-button.el-button--main--button.is-disabled:hover span {
        color: #FFFFFF; }
    .scenatorStyle .el-button.el-button--secondary--button {
      background: #FFFFFF;
      border-color: #0854A1;
      color: #0854A1; }
      .scenatorStyle .el-button.el-button--secondary--button span {
        color: #0854A1; }
    .scenatorStyle .el-button.el-button--secondary--button.transparentBackground {
      background-color: transparent; }
    .scenatorStyle .el-button.el-button--secondary--button:hover {
      background: #E5F0FA;
      border-color: #114171;
      color: #0854A1; }
    .scenatorStyle .el-button.el-button--secondary--button:active {
      background: #114171;
      border-color: #114171;
      color: #FFFFFF; }
      .scenatorStyle .el-button.el-button--secondary--button:active span {
        color: #FFFFFF; }
    .scenatorStyle .el-button.el-button--secondary--button.is-disabled, .scenatorStyle .el-button.el-button--secondary--button.is-disabled:hover {
      color: #0854A1;
      border-color: #0854A1;
      opacity: 0.4;
      background: #FFFFFF; }
      .scenatorStyle .el-button.el-button--secondary--button.is-disabled.transparentBackground, .scenatorStyle .el-button.el-button--secondary--button.is-disabled:hover.transparentBackground {
        background-color: transparent; }
      .scenatorStyle .el-button.el-button--secondary--button.is-disabled span, .scenatorStyle .el-button.el-button--secondary--button.is-disabled:hover span {
        color: #0854A1; }
    .scenatorStyle .el-button.el-button--third--button:hover, .scenatorStyle .el-button.el-button--third--button:active {
      color: #114171; }
      .scenatorStyle .el-button.el-button--third--button:hover span, .scenatorStyle .el-button.el-button--third--button:active span {
        color: #114171; }
    .scenatorStyle .el-button.el-button--third--button.is-disabled, .scenatorStyle .el-button.el-button--third--button.is-disabled:hover, .scenatorStyle .el-button.el-button--third--button.is-disabled:active {
      background: transparent;
      color: #114171;
      opacity: 0.4;
      border-color: transparent; }
      .scenatorStyle .el-button.el-button--third--button.is-disabled span, .scenatorStyle .el-button.el-button--third--button.is-disabled:hover span, .scenatorStyle .el-button.el-button--third--button.is-disabled:active span {
        color: #0854A1; }
  .scenatorStyle .el-link.el-link--default {
    color: #0854A1;
    text-decoration: none; }
    .scenatorStyle .el-link.el-link--default:hover {
      color: #114171;
      text-decoration: none; }
    .scenatorStyle .el-link.el-link--default.is-disabled {
      color: #0854A1;
      opacity: 0.4; }
  .scenatorStyle .el-radio {
    border-color: #89919A; }
    .scenatorStyle .el-radio .el-radio__inner {
      border: 1px solid #89919A;
      width: 16px;
      height: 16px;
      background-color: #FFFFFF; }
    .scenatorStyle .el-radio .el-radio__inner::after {
      background-color: #0854A1;
      width: 6px;
      height: 6px; }
    .scenatorStyle .el-radio .el-radio__label {
      color: #182A4E;
      padding-left: 8px; }
    .scenatorStyle .el-radio.is-checked .el-radio__label {
      color: #182A4E; }
    .scenatorStyle .el-radio.is-checked .el-radio__inner {
      border: 1px solid #89919A;
      background-color: #FFFFFF; }
    .scenatorStyle .el-radio.is-disabled .el-radio__label {
      color: #182A4E;
      opacity: 40%; }
    .scenatorStyle .el-radio.is-disabled .el-radio__input.is-disabled .el-radio__inner,
    .scenatorStyle .el-radio.is-disabled .el-radio__input.is-disabled.is-checked .el-radio__inner {
      background-color: rgba(255, 255, 255, 0.4);
      border-color: rgba(137, 145, 154, 0.4); }
    .scenatorStyle .el-radio.is-disabled .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
      background-color: rgba(8, 84, 161, 0.4); }
    .scenatorStyle .el-radio[scenatorType='readOnly'] .el-radio__input.is-disabled .el-radio__inner,
    .scenatorStyle .el-radio[scenatorType='readOnly'] .el-radio__input.is-disabled.is-checked .el-radio__inner {
      background-color: rgba(255, 255, 255, 0.4);
      border-color: #89919A; }
    .scenatorStyle .el-radio[scenatorType='readOnly'] .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
      background-color: rgba(8, 84, 161, 0.4); }
    .scenatorStyle .el-radio:hover .el-radio__inner {
      border-color: #0854A1; }
  .scenatorStyle .scenator_radio {
    font-size: 14px;
    color: #182A4E; }
    .scenatorStyle .scenator_radio:hover input:before {
      border: 1px solid #0854A1; }
    .scenatorStyle .scenator_radio:hover input:checked:before {
      border: 1px solid #0854A1; }
    .scenatorStyle .scenator_radio input {
      margin-right: 8px;
      height: 16px;
      width: 16px;
      line-height: 16px;
      vertical-align: middle;
      margin-top: -5px;
      appearance: none;
      position: relative;
      outline: none; }
      .scenatorStyle .scenator_radio input:before {
        content: "";
        width: 16px;
        height: 16px;
        border: 1px solid #89919A;
        display: inline-block;
        box-sizing: border-box;
        border-radius: 50%;
        vertical-align: middle; }
      .scenatorStyle .scenator_radio input:checked:before {
        content: "";
        width: 16px;
        height: 16px;
        border: 1px solid #89919A;
        display: inline-block;
        box-sizing: border-box;
        border-radius: 100%;
        vertical-align: middle; }
      .scenatorStyle .scenator_radio input:checked:after {
        content: "";
        width: 6px;
        height: 6px;
        text-align: center;
        background: #0854A1;
        border-radius: 100%;
        display: block;
        position: absolute;
        top: calc(50% - 2px);
        left: calc(50% - 3px); }
  .scenatorStyle .el-checkbox {
    color: #182A4E;
    font-size: 14px; }
    .scenatorStyle .el-checkbox .el-checkbox__label {
      padding-left: 8px; }
    .scenatorStyle .el-checkbox .el-checkbox__inner {
      border: 1px solid #89919A;
      width: 16px;
      height: 16px;
      background-color: #FFFFFF; }
      .scenatorStyle .el-checkbox .el-checkbox__inner:hover {
        border: 1px solid #0854A1; }
    .scenatorStyle .el-checkbox .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #182A4E; }
    .scenatorStyle .el-checkbox .el-checkbox__input.is-checked .el-checkbox__inner,
    .scenatorStyle .el-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner,
    .scenatorStyle .el-checkbox .el-checkbox__input.is-focus .el-checkbox__inner {
      background: #FFFFFF;
      border-color: #89919A; }
      .scenatorStyle .el-checkbox .el-checkbox__input.is-checked .el-checkbox__inner:hover,
      .scenatorStyle .el-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner:hover,
      .scenatorStyle .el-checkbox .el-checkbox__input.is-focus .el-checkbox__inner:hover {
        border: 1px solid #0854A1; }
    .scenatorStyle .el-checkbox .el-checkbox__inner::after {
      border-color: #0854A1;
      top: 2px;
      left: 5px; }
    .scenatorStyle .el-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background: #FFFFFF; }
    .scenatorStyle .el-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      background-color: #0854A1;
      top: 3px;
      left: 3px;
      height: 8px;
      width: 8px;
      transform: rotate(0) scaleY(1); }
    .scenatorStyle .el-checkbox:hover .el-checkbox__inner,
    .scenatorStyle .el-checkbox.is-checked:hover .el-checkbox__inner,
    .scenatorStyle .el-checkbox:hover .el-checkbox__input.is-indeterminate .el-checkbox__inner,
    .scenatorStyle .el-checkbox:hover .el-checkbox__input.is-focus .el-checkbox__inner {
      border: 1px solid #0854A1; }
    .scenatorStyle .el-checkbox[scenatortype="readOnly"]:hover .el-checkbox__inner {
      border-color: #89919A; }
    .scenatorStyle .el-checkbox.is-disabled:hover .el-checkbox__inner {
      border-color: #89919A; }
    .scenatorStyle .el-checkbox.is-disabled .el-checkbox__input.is-disabled + span.el-checkbox__label {
      color: #182A4E;
      opacity: 40%; }
    .scenatorStyle .el-checkbox.is-disabled .el-checkbox__input.is-disabled .el-checkbox__inner {
      background: rgba(255, 255, 255, 0.4);
      border-color: rgba(137, 145, 154, 0.4); }
    .scenatorStyle .el-checkbox.is-disabled .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      border-color: rgba(137, 145, 154, 0.4); }
    .scenatorStyle .el-checkbox.is-disabled .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
      border-color: rgba(8, 84, 161, 0.4); }
    .scenatorStyle .el-checkbox.is-disabled .el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner {
      border-color: rgba(137, 145, 154, 0.4); }
    .scenatorStyle .el-checkbox.is-disabled .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      background-color: rgba(8, 84, 161, 0.4); }
    .scenatorStyle .el-checkbox[scenatorType='readOnly'].is-disabled .el-checkbox__input.is-disabled + span.el-checkbox__label {
      color: #182A4E; }
    .scenatorStyle .el-checkbox[scenatorType='readOnly'].is-disabled .el-checkbox__input.is-disabled .el-checkbox__inner {
      background: rgba(255, 255, 255, 0.4);
      border-color: #89919A; }
    .scenatorStyle .el-checkbox[scenatorType='readOnly'].is-disabled .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      border-color: #89919A; }
    .scenatorStyle .el-checkbox[scenatorType='readOnly'].is-disabled .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
      border-color: rgba(8, 84, 161, 0.4); }
    .scenatorStyle .el-checkbox[scenatorType='readOnly'].is-disabled .el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner {
      border-color: #89919A; }
    .scenatorStyle .el-checkbox[scenatorType='readOnly'].is-disabled .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      background-color: rgba(8, 84, 161, 0.4); }
  .scenatorStyle .el-input,
  .scenatorStyle .el-picker-panel {
    overflow: hidden; }
    .scenatorStyle .el-input.el-input--prefix .el-input__inner,
    .scenatorStyle .el-picker-panel.el-input--prefix .el-input__inner {
      padding-left: 36px; }
    .scenatorStyle .el-input.el-input--prefix .el-input__prefix,
    .scenatorStyle .el-picker-panel.el-input--prefix .el-input__prefix {
      left: 1px;
      width: 32px;
      cursor: pointer;
      transition: background-color 0.3s; }
      .scenatorStyle .el-input.el-input--prefix .el-input__prefix .el-input__icon,
      .scenatorStyle .el-picker-panel.el-input--prefix .el-input__prefix .el-input__icon {
        width: 100%; }
      .scenatorStyle .el-input.el-input--prefix .el-input__prefix .el-icon-search,
      .scenatorStyle .el-picker-panel.el-input--prefix .el-input__prefix .el-icon-search {
        transition: none; }
      .scenatorStyle .el-input.el-input--prefix .el-input__prefix .el-icon-search:before,
      .scenatorStyle .el-picker-panel.el-input--prefix .el-input__prefix .el-icon-search:before {
        content: " ";
        width: calc(100% - 2px);
        height: calc(100% - 2px);
        position: absolute;
        top: 1px;
        left: 1px; }
    .scenatorStyle .el-input.el-input--suffix .el-input__inner,
    .scenatorStyle .el-picker-panel.el-input--suffix .el-input__inner {
      padding-right: 36px; }
    .scenatorStyle .el-input.el-input--suffix .el-input__suffix,
    .scenatorStyle .el-picker-panel.el-input--suffix .el-input__suffix {
      width: 32px;
      height: 30px;
      right: 0px;
      margin-top: 1px;
      pointer-events: auto;
      cursor: pointer; }
      .scenatorStyle .el-input.el-input--suffix .el-input__suffix .el-input__icon,
      .scenatorStyle .el-picker-panel.el-input--suffix .el-input__suffix .el-input__icon {
        width: 100%; }
      .scenatorStyle .el-input.el-input--suffix .el-input__suffix:hover,
      .scenatorStyle .el-picker-panel.el-input--suffix .el-input__suffix:hover {
        background-color: rgba(0, 129, 255, 0.12); }
      .scenatorStyle .el-input.el-input--suffix .el-input__suffix:active,
      .scenatorStyle .el-picker-panel.el-input--suffix .el-input__suffix:active {
        background-color: #0854A1; }
      .scenatorStyle .el-input.el-input--suffix .el-input__suffix .el-input__clear:before,
      .scenatorStyle .el-picker-panel.el-input--suffix .el-input__suffix .el-input__clear:before {
        content: " ";
        width: 16px;
        height: 16px;
        position: absolute;
        top: calc(50% - 8px);
        left: 50%;
        transform: translateX(-50%);
        cursor: pointer; }
      .scenatorStyle .el-input.el-input--suffix .el-input__suffix .el-input__clear:hover::before,
      .scenatorStyle .el-picker-panel.el-input--suffix .el-input__suffix .el-input__clear:hover::before {
        border-radius: 2px;
        cursor: pointer; }
    .scenatorStyle .el-input:hover .el-input__suffix, .scenatorStyle .el-input:focus .el-input__suffix,
    .scenatorStyle .el-picker-panel:hover .el-input__suffix,
    .scenatorStyle .el-picker-panel:focus .el-input__suffix {
      display: block; }
    .scenatorStyle .el-input .el-input__inner:focus ~ .el-input__suffix,
    .scenatorStyle .el-picker-panel .el-input__inner:focus ~ .el-input__suffix {
      display: block; }
    .scenatorStyle .el-input .el-input__inner,
    .scenatorStyle .el-picker-panel .el-input__inner {
      background: transparent;
      border-radius: 2px;
      border: 1px solid #89919A;
      height: 32px;
      color: #182A4E;
      line-height: 100%;
      padding: 6px 8px; }
      .scenatorStyle .el-input .el-input__inner:hover,
      .scenatorStyle .el-picker-panel .el-input__inner:hover {
        border: 1px solid #0854A1; }
      .scenatorStyle .el-input .el-input__inner::-webkit-input-placeholder,
      .scenatorStyle .el-picker-panel .el-input__inner::-webkit-input-placeholder {
        color: #94999D; }
      .scenatorStyle .el-input .el-input__inner:-ms-input-placeholder,
      .scenatorStyle .el-picker-panel .el-input__inner:-ms-input-placeholder {
        color: #94999D; }
      .scenatorStyle .el-input .el-input__inner[disabled='disabled'],
      .scenatorStyle .el-picker-panel .el-input__inner[disabled='disabled'] {
        border: 1px solid #89919A;
        opacity: 0.4; }
      .scenatorStyle .el-input .el-input__inner[disabled='disabled']:hover,
      .scenatorStyle .el-picker-panel .el-input__inner[disabled='disabled']:hover {
        border: 1px solid #89919A; }
      .scenatorStyle .el-input .el-input__inner[disabled='disabled'][scenatorType='readOnly'],
      .scenatorStyle .el-picker-panel .el-input__inner[disabled='disabled'][scenatorType='readOnly'] {
        background: rgba(24, 42, 78, 0.06);
        border: 1px solid #89919A;
        opacity: 1; }
      .scenatorStyle .el-input .el-input__inner[disabled='disabled'][scenatorType='readOnly']:hover,
      .scenatorStyle .el-picker-panel .el-input__inner[disabled='disabled'][scenatorType='readOnly']:hover {
        border: 1px solid #89919A; }
    .scenatorStyle .el-input.is-disabled .el-input__inner,
    .scenatorStyle .el-picker-panel.is-disabled .el-input__inner {
      background: transparent;
      color: rgba(24, 42, 78, 0.4); }
    .scenatorStyle .el-input.is-disabled .el-input__inner[scenatorType='readOnly'],
    .scenatorStyle .el-picker-panel.is-disabled .el-input__inner[scenatorType='readOnly'] {
      color: #182A4E; }
    .scenatorStyle .el-input.is-active .el-input__inner,
    .scenatorStyle .el-input .el-input__inner:focus,
    .scenatorStyle .el-picker-panel.is-active .el-input__inner,
    .scenatorStyle .el-picker-panel .el-input__inner:focus {
      border-color: #0854A1; }
    .scenatorStyle .el-input .el-input__suffix .el-input__count .el-input__count-inner,
    .scenatorStyle .el-picker-panel .el-input__suffix .el-input__count .el-input__count-inner {
      line-height: normal; }
    .scenatorStyle .el-input .el-input__inner[scenatorStyle="bgColor"],
    .scenatorStyle .el-picker-panel .el-input__inner[scenatorStyle="bgColor"] {
      background: #FFFFFF; }
  .scenatorStyle .el-form .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
  .scenatorStyle .el-form .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
    content: ''; }
  .scenatorStyle .el-form .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:after,
  .scenatorStyle .el-form .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:after {
    content: '*';
    color: #BB0000; }
  .scenatorStyle .el-form .el-form-item.is-error .el-input__inner,
  .scenatorStyle .el-form .el-form-item.is-error .el-input__inner:focus,
  .scenatorStyle .el-form .el-form-item.is-error .el-textarea__inner,
  .scenatorStyle .el-form .el-form-item.is-error .el-textarea__inner:focus {
    border: 1px solid #BB0000; }
  .scenatorStyle .el-form .el-form-item.is-error .el-form-item__error {
    color: #BB0000;
    padding-top: 0; }
  .scenatorStyle .el-form .el-form-item.is-error .el-form-item__content {
    line-height: inherit; }
  .scenatorStyle .input {
    background: transparent;
    border-radius: 2px;
    border: 1px solid #89919A;
    height: 32px;
    color: #182A4E;
    line-height: 100%;
    padding: 6px 8px;
    width: 100%; }
    .scenatorStyle .input:hover, .scenatorStyle .input:focus, .scenatorStyle .input:focus-visible {
      border: 1px solid #0854A1;
      outline: #0854A1; }
    .scenatorStyle .input:-ms-input-placeholder {
      color: #94999D; }
    .scenatorStyle .input::-webkit-input-placeholder {
      color: #94999D; }
    .scenatorStyle .input.error {
      border-color: #BB0000; }
    .scenatorStyle .input::-ms-clear {
      display: none; }
  .scenatorStyle .textbox {
    border: 1px solid #89919A;
    border-radius: 2px;
    background: transparent; }
    .scenatorStyle .textbox:hover, .scenatorStyle .textbox.textbox-focused {
      border-color: #0854A1;
      box-shadow: none; }
    .scenatorStyle .textbox .textbox-text {
      padding: 6px 8px;
      width: 100%;
      background: transparent; }
  .scenatorStyle .textbox.combo .textbox-text {
    margin-right: 32px;
    padding: 0 4px 0 8px !important;
    width: calc(100% - 32px) !important;
    color: #182A4E; }
  .scenatorStyle .panel.combo-p {
    margin-top: 2px; }
  .scenatorStyle .el-select .el-input .el-select__caret {
    transform: none;
    transition: none; }
  .scenatorStyle .el-select .el-input__inner:focus {
    border-color: #0854A1; }
    .scenatorStyle .el-select .el-input__inner:focus + .el-input__suffix {
      background: #0854A1; }
      .scenatorStyle .el-select .el-input__inner:focus + .el-input__suffix .el-icon-arrow-up:before {
        background: url("../images/scenator_arrowUpActive.svg") no-repeat center;
        transform: rotateZ(180deg); }
  .scenatorStyle .el-select .el-input__inner {
    background-color: transparent;
    border-radius: 2px;
    border: 1px solid #89919A;
    height: 32px;
    line-height: 100%;
    padding: 6px 8px;
    padding-right: 0px;
    color: #182A4E; }
    .scenatorStyle .el-select .el-input__inner:-ms-input-placeholder {
      color: #94999D; }
    .scenatorStyle .el-select .el-input__inner::-webkit-input-placeholder {
      color: #94999D; }
  .scenatorStyle .el-select[scenatorStyle="bgColor"] .el-input__inner {
    background: #FFFFFF; }
  .scenatorStyle .el-select .el-input__inner:hover {
    border: 1px solid #0854A1; }
  .scenatorStyle .el-select .el-input__icon {
    line-height: 100%;
    height: 100%;
    color: #0854A1;
    font-weight: 900;
    font-weight: 900;
    width: 32px; }
  .scenatorStyle .el-select .el-input .el-input__suffix {
    right: 0;
    top: 0px;
    box-sizing: border-box;
    height: 30px;
    padding: 0px 0px 0px 0px;
    margin: 1px 1px 0px 0px;
    transform: scale(1); }
    .scenatorStyle .el-select .el-input .el-input__suffix:active {
      background-color: rgba(0, 129, 255, 0.12); }
  .scenatorStyle .el-select:hover .el-input__suffix, .scenatorStyle .el-select:active .el-input__suffix {
    background: rgba(0, 129, 255, 0.12);
    height: 30px; }
  .scenatorStyle .el-select .el-input.is-focus .el-input__inner {
    border: 1px solid #0854A1; }
  .scenatorStyle .el-select .el-icon-arrow-up:before {
    content: " ";
    width: 16px;
    height: 16px;
    background: url("../images/scenator_treeOpen.svg") no-repeat center;
    position: absolute;
    top: calc(50% - 8px);
    left: calc(50% - 8px); }
  .scenatorStyle .el-select .el-input.is-focus .el-icon-arrow-up:before {
    background: url("../images/scenator_arrowUpActive.svg") no-repeat center;
    transform: rotateZ(180deg); }
  .scenatorStyle .el-select .el-input.is-focus .el-input__suffix {
    background: #0854A1; }
  .scenatorStyle .el-select .el-input__inner[disabled='disabled'] {
    border: 1px solid #89919A;
    opacity: 0.4; }
  .scenatorStyle .el-select .el-input.is-disabled .el-input__inner:hover {
    border-color: #89919A; }
  .scenatorStyle .el-select .el-input.is-disabled:hover .el-input__suffix {
    background-color: transparent; }
  .scenatorStyle .el-select .el-input.is-disabled .el-input__icon {
    opacity: 0.4; }
  .scenatorStyle .select-checked .el-input {
    width: 200px; }
  .scenatorStyle .select-checked .el-select-dropdown.is-multiple .el-select-dropdown__item {
    padding: 0 16px 0 0; }
    .scenatorStyle .select-checked .el-select-dropdown.is-multiple .el-select-dropdown__item .el-checkbox__inner::after {
      display: none; }
    .scenatorStyle .select-checked .el-select-dropdown.is-multiple .el-select-dropdown__item.hover, .scenatorStyle .select-checked .el-select-dropdown.is-multiple .el-select-dropdown__item:hover {
      background-color: #E5F0FA; }
      .scenatorStyle .select-checked .el-select-dropdown.is-multiple .el-select-dropdown__item.hover .el-checkbox .el-checkbox__inner, .scenatorStyle .select-checked .el-select-dropdown.is-multiple .el-select-dropdown__item:hover .el-checkbox .el-checkbox__inner {
        border: 1px solid #0854A1; }
    .scenatorStyle .select-checked .el-select-dropdown.is-multiple .el-select-dropdown__item.selected .el-checkbox__inner::after {
      display: block;
      -webkit-transform: rotate(45deg) scaleY(1);
      transform: rotate(45deg) scaleY(1); }
    .scenatorStyle .select-checked .el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
      content: ""; }
  .scenatorStyle .select-checked .el-checkbox {
    width: 100%; }
    .scenatorStyle .select-checked .el-checkbox .el-checkbox__input {
      margin-left: 8px; }
    .scenatorStyle .select-checked .el-checkbox .el-checkbox__label {
      margin-left: 0; }
  .scenatorStyle .el-textarea .el-textarea__inner {
    background: transparent;
    border-color: #89919A;
    border-radius: 2px;
    color: #182A4E;
    padding: 6px 8px;
    overflow: auto; }
    .scenatorStyle .el-textarea .el-textarea__inner:-ms-input-placeholder,
    .scenatorStyle .el-textarea .el-textarea__inner textarea:-ms-input-placeholder {
      color: #94999D; }
    .scenatorStyle .el-textarea .el-textarea__inner::-webkit-input-placeholder,
    .scenatorStyle .el-textarea .el-textarea__inner textarea::-webkit-input-placeholder {
      color: #94999D; }
  .scenatorStyle .el-textarea.error .el-textarea__inner {
    border-color: #BB0000; }
  .scenatorStyle .el-textarea .el-textarea__inner[scenatorStyle="bgColor"] {
    background: #FFFFFF; }
  .scenatorStyle .el-textarea .el-textarea__inner:hover {
    border-color: #0854A1; }
  .scenatorStyle .el-textarea .el-textarea__inner:focus {
    border-color: #0854A1; }
  .scenatorStyle .el-textarea .el-input__count {
    color: #94999D;
    background: transparent;
    position: relative;
    float: right;
    margin-top: 6px; }
  .scenatorStyle .scenator_briefMsg {
    min-width: 192px;
    max-width: 536px;
    background: #FFFFFF;
    padding: 14px 16px;
    box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: 0px; }
    .scenatorStyle .scenator_briefMsg article {
      color: #182A4E; }
    .scenatorStyle .scenator_briefMsg .el-message__icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      position: absolute;
      top: 16px; }
    .scenatorStyle .scenator_briefMsg .el-message__content {
      line-height: 20px;
      width: calc(100% - 24px);
      margin-left: 24px;
      color: #182A4E; }
    .scenatorStyle .scenator_briefMsg .el-icon-info:before {
      content: ""; }
  .scenatorStyle .scenator_complexMsg {
    padding: 14px 16px;
    border: 0;
    border-radius: 4px;
    width: 720px;
    color: #182A4E; }
    .scenatorStyle .scenator_complexMsg article {
      color: #182A4E;
      display: inline-block;
      width: calc(100% - 190px); }
    .scenatorStyle .scenator_complexMsg .el-icon-info {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      position: absolute;
      top: 16px; }
    .scenatorStyle .scenator_complexMsg .el-message__content {
      line-height: 20px;
      width: calc(100% - 24px);
      margin-left: 24px;
      color: #182A4E; }
      .scenatorStyle .scenator_complexMsg .el-message__content .btns {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 0; }
    .scenatorStyle .scenator_complexMsg .el-icon-info:before {
      content: ""; }
    .scenatorStyle .scenator_complexMsg.success {
      background: #F1FDF6;
      box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(16, 126, 62, 0.45); }
    .scenatorStyle .scenator_complexMsg.warn {
      background: #FEF7F1;
      box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(233, 115, 12, 0.45); }
    .scenatorStyle .scenator_complexMsg.info {
      background: #F5FAFF;
      box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(10, 110, 209, 0.45); }
    .scenatorStyle .scenator_complexMsg.error {
      background: #FFEBEB;
      box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(187, 0, 0, 0.45); }
  .scenatorStyle .scenator_topMsg {
    box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.12);
    border-radius: 0px;
    width: 100%;
    color: #182A4E;
    padding: 14px 16px;
    top: 0px !important;
    border: 0;
    display: flex;
    align-items: center;
    justify-content: center; }
    .scenatorStyle .scenator_topMsg article {
      color: #182A4E; }
    .scenatorStyle .scenator_topMsg .el-message__content {
      line-height: 20px;
      color: #182A4E; }
    .scenatorStyle .scenator_topMsg .el-icon-info {
      width: 16px;
      height: 16px;
      margin-right: 8px; }
    .scenatorStyle .scenator_topMsg .el-icon-info:before {
      content: ""; }
    .scenatorStyle .scenator_topMsg.success {
      background: #F1FDF6; }
    .scenatorStyle .scenator_topMsg.warn {
      background: #FEF7F1; }
    .scenatorStyle .scenator_topMsg.info {
      background: #F5FAFF; }
    .scenatorStyle .scenator_topMsg.error {
      background: #FFEBEB; }
  .scenatorStyle .el-message-box.scenator_confirmPrompt {
    box-shadow: inset 0px 1px 0px 0px #D9D9D9;
    background-color: #FFFFFF;
    border: 0;
    border-radius: 4px;
    padding: 0px;
    width: 480px; }
    .scenatorStyle .el-message-box.scenator_confirmPrompt .el-message-box__header {
      padding: 16px;
      font-size: 14px;
      background-color: #FFFFFF;
      height: 44px; }
      .scenatorStyle .el-message-box.scenator_confirmPrompt .el-message-box__header .el-message-box__title {
        color: #182A4E;
        font-size: 16px;
        line-height: 100%;
        overflow: hidden; }
        .scenatorStyle .el-message-box.scenator_confirmPrompt .el-message-box__header .el-message-box__title span {
          font-size: 16px; }
      .scenatorStyle .el-message-box.scenator_confirmPrompt .el-message-box__header .el-message-box__title::before {
        content: ' ';
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -2px; }
    .scenatorStyle .el-message-box.scenator_confirmPrompt .el-message-box__content {
      padding: 24px 16px;
      background-color: #FFFFFF;
      color: #182A4E; }
    .scenatorStyle .el-message-box.scenator_confirmPrompt .el-message-box__btns {
      padding: 12px 16px;
      -webkit-box-shadow: inset 0px 1px 0px 0px #D9D9D9;
      box-shadow: inset 0px 1px 0px 0px #D9D9D9;
      overflow: hidden; }
    .scenatorStyle .el-message-box.scenator_confirmPrompt .el-message-box__btns .el-button:nth-child(1) {
      margin-left: 16px; }
    .scenatorStyle .el-message-box.scenator_confirmPrompt.ask .el-message-box__header {
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #0A6ED1; }
    .scenatorStyle .el-message-box.scenator_confirmPrompt.warn .el-message-box__header {
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #E9730C; }
    .scenatorStyle .el-message-box.scenator_confirmPrompt.info .el-message-box__header {
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #0A6ED1; }
    .scenatorStyle .el-message-box.scenator_confirmPrompt.error .el-message-box__header {
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #BB0000; }
    .scenatorStyle .el-message-box.scenator_confirmPrompt .scenator_confirmPrompt_article article {
      line-height: 28px;
      color: #182A4E; }
    .scenatorStyle .el-message-box.scenator_confirmPrompt .scenator_confirmPrompt_showMore {
      margin-top: 16px;
      color: #0854A1;
      cursor: pointer; }
  .scenatorStyle .el-dialog__wrapper {
    justify-content: center;
    align-items: center;
    display: flex; }
    .scenatorStyle .el-dialog__wrapper .el-dialog__header {
      height: 44px;
      padding: 13px 16px 15px 16px;
      background-color: #FFFFFF;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0px 1px 0px 0px #D9D9D9, 0px 0px 4px 0px rgba(0, 0, 0, 0.15); }
      .scenatorStyle .el-dialog__wrapper .el-dialog__header .el-dialog__title {
        font-size: 16px;
        color: #182A4E;
        font-weight: 400;
        font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑"; }
      .scenatorStyle .el-dialog__wrapper .el-dialog__header .el-dialog__headerbtn .el-dialog__close {
        color: #0854A1;
        font-weight: 900;
        margin-top: 4px; }
      .scenatorStyle .el-dialog__wrapper .el-dialog__header .el-dialog__close {
        width: 20px;
        height: 20px;
        border: 1px solid transparent;
        border-radius: 2px; }
      .scenatorStyle .el-dialog__wrapper .el-dialog__header .el-dialog__close:hover {
        background-color: #E5F0FA;
        width: 20px;
        height: 20px;
        border: 1px solid #0854A1; }
      .scenatorStyle .el-dialog__wrapper .el-dialog__header .el-dialog__close:active {
        background-color: #0854A1;
        width: 20px;
        height: 20px;
        border: 1px solid #0854A1; }
      .scenatorStyle .el-dialog__wrapper .el-dialog__header .el-icon-close:before {
        content: " "; }
    .scenatorStyle .el-dialog__wrapper .el-dialog__headerbtn {
      position: initial;
      top: 10px; }
    .scenatorStyle .el-dialog__wrapper .el-dialog {
      margin: 0; }
    .scenatorStyle .el-dialog__wrapper .el-dialog__footer {
      padding: 12px 16px;
      box-shadow: inset 0px 1px 0px 0px #D9D9D9; }
      .scenatorStyle .el-dialog__wrapper .el-dialog__footer .dialog-footer {
        font-size: 0px; }
        .scenatorStyle .el-dialog__wrapper .el-dialog__footer .dialog-footer button:nth-of-type(1) {
          margin-left: 0px; }
        .scenatorStyle .el-dialog__wrapper .el-dialog__footer .dialog-footer button:nth-last-child(1) {
          margin-left: 16px; }
      .scenatorStyle .el-dialog__wrapper .el-dialog__footer .el-button:nth-child(1) {
        margin-right: 0px; }
  .scenatorStyle .scenator_loading {
    display: flex;
    justify-content: space-around;
    align-items: center; }
    .scenatorStyle .scenator_loading span {
      display: block;
      width: 10%;
      height: 100%;
      margin-right: 0px;
      border-radius: 50%;
      background: #0854A1;
      -webkit-animation: scenatorLoading 1.5s ease infinite; }
    .scenatorStyle .scenator_loading span:nth-child(1) {
      animation-delay: 0.5s; }
    .scenatorStyle .scenator_loading span:nth-child(2) {
      animation-delay: 0.9s; }
    .scenatorStyle .scenator_loading span:nth-child(3) {
      animation-delay: 1.2s; }

@keyframes scenatorLoading {
  0% {
    opacity: 1;
    transform: scale(2.5); }
  100% {
    opacity: 0.2;
    transform: scale(0.3); } }
  .scenatorStyle .scenator_loading_shadow {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center; }
    .scenatorStyle .scenator_loading_shadow .loading-item {
      width: 6px;
      height: 6px;
      margin-left: 20px;
      border-radius: 50%;
      box-shadow: -30px 0px #0854A1, -10px 0px #0854A1, 10px 0px #0854A1; }
      .scenatorStyle .scenator_loading_shadow .loading-item.loading-active {
        animation: shadowScaleForLoading 1.2s linear infinite; }
    .scenatorStyle .scenator_loading_shadow .loading-text {
      margin-top: 24px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
      font-weight: 400;
      color: #182A4E;
      line-height: 20px; }

@-webkit-keyframes shadowScaleForLoading {
  0% {
    box-shadow: -30px 0px 0px 4px #0854A1, -10px 0px #0854A1, 10px 0px #0854A1; }
  33% {
    box-shadow: -30px 0px #0854A1, -10px 0px 0px 4px #0854A1, 10px 0px #0854A1; }
  66% {
    box-shadow: -30px 0px #0854A1, -10px 0px #0854A1, 10px 0px 0px 4px #0854A1; }
  100% {
    box-shadow: -30px 0px #0854A1, -10px 0px #0854A1, 10px 0px #0854A1; } }

@keyframes shadowScaleForLoading {
  0% {
    box-shadow: -30px 0px 0px 4px #0854A1, -10px 0px #0854A1, 10px 0px #0854A1; }
  33% {
    box-shadow: -30px 0px #0854A1, -10px 0px 0px 4px #0854A1, 10px 0px #0854A1; }
  66% {
    box-shadow: -30px 0px #0854A1, -10px 0px #0854A1, 10px 0px 0px 4px #0854A1; }
  100% {
    box-shadow: -30px 0px #0854A1, -10px 0px #0854A1, 10px 0px #0854A1; } }
  .scenatorStyle .scenatorTitle {
    box-sizing: content-box;
    padding: 4px 8px;
    color: white;
    background: black;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.35);
    opacity: 0.6;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 99999999;
    display: none;
    border-radius: 2px;
    font-size: 14px;
    line-height: 20px;
    max-width: 320px; }
    .scenatorStyle .scenatorTitle p {
      line-height: 20px; }
    .scenatorStyle .scenatorTitle span {
      font-size: 12px;
      line-height: 17px;
      margin-top: 4px;
      display: inline-block; }
  .scenatorStyle .el-date-editor {
    width: 200px;
    height: 32px;
    line-height: 32px; }
    .scenatorStyle .el-date-editor.el-input {
      width: 200px; }
    .scenatorStyle .el-date-editor table {
      display: table;
      border: 0;
      box-shadow: none; }
    .scenatorStyle .el-date-editor th,
    .scenatorStyle .el-date-editor td {
      border: 0px; }
    .scenatorStyle .el-date-editor tr:nth-child(2n) {
      background-color: transparent; }
    .scenatorStyle .el-date-editor tr {
      border-top: 0px; }
    .scenatorStyle .el-date-editor .el-input__inner {
      background-color: #FFFFFF;
      border-radius: 2px;
      border: 1px solid #89919A;
      height: 32px;
      color: #182A4E;
      padding-left: 35px; }
    .scenatorStyle .el-date-editor .el-input__inner:focus ~ .el-input__prefix {
      background: #0854A1; }
    .scenatorStyle .el-date-editor .el-input__inner:focus ~ .el-input__prefix .el-input__icon::before {
      background: url("../images/scenator_dateIconActive.svg") no-repeat center; }
    .scenatorStyle .el-date-editor .el-input__icon {
      line-height: 100%;
      font-weight: 550;
      transition: none; }
    .scenatorStyle .el-date-editor .el-input__icon::before {
      content: "";
      width: 16px;
      height: 16px;
      display: inline-block;
      margin-top: 7px; }
    .scenatorStyle .el-date-editor .el-icon-date::before,
    .scenatorStyle .el-date-editor .el-icon-time::before {
      background: url("../images/scenator_dateIcon.svg") no-repeat center;
      background-size: 100% 100%; }
    .scenatorStyle .el-date-editor .el-input__prefix {
      width: 30px;
      height: 30px;
      background: #FFFFFF;
      top: 1px;
      right: 1px; }
      .scenatorStyle .el-date-editor .el-input__prefix .el-icon-time:before {
        width: 14px;
        height: 16px; }
    .scenatorStyle .el-date-editor .el-input__prefix:active .el-icon-date {
      background-color: #0854A1; }
      .scenatorStyle .el-date-editor .el-input__prefix:active .el-icon-date::before {
        background: url("../images/scenator_dateIconActive.svg") no-repeat center; }
    .scenatorStyle .el-date-editor.el-input .el-input__prefix {
      left: auto;
      top: 1px;
      right: 1px; }
    .scenatorStyle .el-date-editor.el-input .el-input__suffix {
      display: none; }
      .scenatorStyle .el-date-editor.el-input .el-input__suffix:hover, .scenatorStyle .el-date-editor.el-input .el-input__suffix:focus {
        background-color: transparent; }
    .scenatorStyle .el-date-editor.el-input:hover .el-input__suffix,
    .scenatorStyle .el-date-editor.el-input:focus .el-input__suffix {
      display: none; }
    .scenatorStyle .el-date-editor.el-input .el-input__inner {
      padding: 6px 8px;
      padding-right: 36px; }
    .scenatorStyle .el-date-editor .el-icon-circle-close:before {
      content: " ";
      background: url("../images/scenator_removeIcon.svg") no-repeat center;
      width: 16px;
      height: 16px;
      position: absolute;
      margin: 0;
      right: 5px;
      top: calc(50% - 8px); }
    .scenatorStyle .el-date-editor .el-input__suffix {
      display: none;
      width: 25px;
      right: 5px; }
    .scenatorStyle .el-date-editor:hover .el-input__inner {
      border: 1px solid #0854A1; }
    .scenatorStyle .el-date-editor:hover .el-input__prefix {
      background-color: rgba(0, 129, 255, 0.12); }
      .scenatorStyle .el-date-editor:hover .el-input__prefix .el-icon-time:before {
        background: #BB0000; }
    .scenatorStyle .el-date-editor.el-range-editor.el-input__inner {
      padding: 6px 8px; }
    .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange {
      width: 280px;
      height: 32px;
      border-color: #89919A;
      border-radius: 2px;
      background: #FFFFFF; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange:hover, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange:hover {
        border: 1px solid #0854A1; }
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange:hover .el-range__icon, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange:hover .el-range__icon {
          background-color: #E5F0FA; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange:focus, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange:focus {
        border: 1px solid #0854A1; }
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange:focus .el-icon-date:before,
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange:focus .el-icon-time:before, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange:focus .el-icon-date:before,
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange:focus .el-icon-time:before {
          background: url("../images/scenator_dateIconActive.svg") no-repeat center; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active {
        border: 1px solid #0854A1; }
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active .el-icon-date:before,
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active .el-icon-time:before, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active .el-icon-date:before,
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active .el-icon-time:before {
          background: url("../images/scenator_dateIconActive.svg") no-repeat center; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active .el-icon-date,
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active .el-icon-time, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active .el-icon-date,
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active .el-icon-time {
        background-color: #0854A1; }
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active .el-icon-date:before,
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active .el-icon-time:before, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active .el-icon-date:before,
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active .el-icon-time:before {
          background: url("../images/scenator_dateIconActive.svg") no-repeat center; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active:hover .el-icon-date,
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active .el-icon-time, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active:hover .el-icon-date,
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active .el-icon-time {
        background-color: #0854A1; }
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active:hover .el-icon-date:before,
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange.is-active .el-icon-time:before, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active:hover .el-icon-date:before,
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange.is-active .el-icon-time:before {
          background: url("../images/scenator_dateIconActive.svg") no-repeat center; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-range__icon, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-range__icon {
        width: 32px;
        height: 30px;
        padding: 0 2px;
        position: absolute;
        right: 0;
        top: 0; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-input__icon, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-input__icon {
        line-height: 100%;
        color: #0854A1; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-input__prefix, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-input__prefix {
        width: 30px;
        height: 30px;
        left: 1px;
        background: #FFFFFF;
        top: 1px; }
        .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-input__prefix .el-icon-time:before, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-input__prefix .el-icon-time:before {
          width: 14px;
          height: 16px; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-input__suffix, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-input__suffix {
        width: 25px;
        right: 0px; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-range-separator, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-range-separator {
        line-height: 18px;
        color: #182A4E;
        width: auto;
        padding: 0 8px; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-date-table td.end-date span,
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-date-table td.start-date span, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-date-table td.end-date span,
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-date-table td.start-date span {
        background-color: #0854A1;
        color: #FFFFFF; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-range-input, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-range-input {
        background: #FFFFFF;
        color: #182A4E;
        text-align: left;
        width: calc((100% - 50px) / 2); }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-range-input::-webkit-input-placeholder, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-range-input::-webkit-input-placeholder {
        color: #94999D; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange:-ms-input-placeholder, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange:-ms-input-placeholder {
        color: #94999D; }
      .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--daterange .el-range__close-icon, .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange .el-range__close-icon {
        display: none; }
    .scenatorStyle .el-date-editor.el-range-editor.el-date-editor--datetimerange {
      width: 400px; }
  .scenatorStyle .el-picker-panel {
    background-color: #FFFFFF;
    border: 0;
    box-shadow: 0px 10px 30px 2px rgba(0, 0, 0, 0.12), 0px 0px 0px 1px #D9D9D9;
    border-radius: 4px; }
    .scenatorStyle .el-picker-panel table {
      display: table;
      border: 0px;
      box-shadow: none; }
    .scenatorStyle .el-picker-panel th,
    .scenatorStyle .el-picker-panel td {
      border: 0px;
      text-align: center;
      color: #182A4E; }
    .scenatorStyle .el-picker-panel tr:nth-child(2n) {
      background-color: transparent; }
    .scenatorStyle .el-picker-panel tr {
      border-top: 0px; }
    .scenatorStyle .el-picker-panel .el-date-table {
      font-size: 14px; }
      .scenatorStyle .el-picker-panel .el-date-table tr,
      .scenatorStyle .el-picker-panel .el-date-table th,
      .scenatorStyle .el-picker-panel .el-date-table td {
        background: transparent; }
      .scenatorStyle .el-picker-panel .el-date-table td div {
        height: 34px;
        padding: 0; }
      .scenatorStyle .el-picker-panel .el-date-table span {
        width: 34px;
        height: 34px;
        line-height: 34px; }
      .scenatorStyle .el-picker-panel .el-date-table td.end-date span,
      .scenatorStyle .el-picker-panel .el-date-table td.start-date span {
        background-color: #0854A1;
        color: #FFFFFF; }
      .scenatorStyle .el-picker-panel .el-date-table td.prev-month span,
      .scenatorStyle .el-picker-panel .el-date-table td.next-month span {
        color: #5D697A; }
      .scenatorStyle .el-picker-panel .el-date-table td.today span {
        color: #0854A1;
        font-weight: 400; }
      .scenatorStyle .el-picker-panel .el-date-table td.in-range div {
        background-color: rgba(0, 129, 255, 0.12); }
      .scenatorStyle .el-picker-panel .el-date-table td.current:not(.disabled) span {
        background-color: #0854A1;
        color: #FFFFFF; }
      .scenatorStyle .el-picker-panel .el-date-table td.available:hover {
        color: #0854A1; }
    .scenatorStyle .el-picker-panel .el-date-picker__header,
    .scenatorStyle .el-picker-panel .el-date-range-picker__header {
      margin-top: 15px;
      margin-bottom: 15px; }
      .scenatorStyle .el-picker-panel .el-date-picker__header .el-picker-panel__icon-btn,
      .scenatorStyle .el-picker-panel .el-date-range-picker__header .el-picker-panel__icon-btn {
        border: none;
        color: #0854A1; }
      .scenatorStyle .el-picker-panel .el-date-picker__header .el-picker-panel__icon-btn:hover,
      .scenatorStyle .el-picker-panel .el-date-range-picker__header .el-picker-panel__icon-btn:hover {
        border: none;
        color: #0854A1; }
      .scenatorStyle .el-picker-panel .el-date-picker__header .el-date-picker__header-label,
      .scenatorStyle .el-picker-panel .el-date-picker__header div,
      .scenatorStyle .el-picker-panel .el-date-range-picker__header .el-date-picker__header-label,
      .scenatorStyle .el-picker-panel .el-date-range-picker__header div {
        color: #182A4E;
        font-size: 14px;
        font-weight: 500; }
    .scenatorStyle .el-picker-panel .el-picker-panel__content {
      border: none;
      margin-top: 0; }
      .scenatorStyle .el-picker-panel .el-picker-panel__content .el-date-range-picker__header {
        margin-top: 0; }
    .scenatorStyle .el-picker-panel .el-date-range-picker__time-header {
      border-bottom: 1px solid #D9D9D9; }
      .scenatorStyle .el-picker-panel .el-date-range-picker__time-header .el-input__inner {
        background: #FFFFFF;
        border: 1px solid #89919A;
        border-radius: 2px;
        padding: 0 6px;
        color: #182A4E; }
        .scenatorStyle .el-picker-panel .el-date-range-picker__time-header .el-input__inner:hover {
          border: 1px solid #0854A1; }
        .scenatorStyle .el-picker-panel .el-date-range-picker__time-header .el-input__inner::-webkit-input-placeholder {
          color: #94999D; }
        .scenatorStyle .el-picker-panel .el-date-range-picker__time-header .el-input__inner:-ms-input-placeholder {
          color: #94999D; }
      .scenatorStyle .el-picker-panel .el-date-range-picker__time-header .el-icon-arrow-right {
        color: #182A4E; }
    .scenatorStyle .el-picker-panel .el-picker-panel__footer {
      background: #FFFFFF;
      border-top: 1px solid #D9D9D9; }
      .scenatorStyle .el-picker-panel .el-picker-panel__footer .el-button {
        padding: 8px 16px; }
      .scenatorStyle .el-picker-panel .el-picker-panel__footer .el-button--default {
        border-radius: 2px;
        background-color: transparent;
        border-color: #0854A1;
        color: #0854A1; }
        .scenatorStyle .el-picker-panel .el-picker-panel__footer .el-button--default:hover, .scenatorStyle .el-picker-panel .el-picker-panel__footer .el-button--default:focus {
          background: #E5F0FA;
          border-color: #114171;
          color: #0854A1; }
        .scenatorStyle .el-picker-panel .el-picker-panel__footer .el-button--default:active {
          background: #0854A1;
          border-color: #114171;
          color: #FFFFFF; }
        .scenatorStyle .el-picker-panel .el-picker-panel__footer .el-button--default.is-disabled {
          color: #0854A1;
          border-color: #0854A1;
          opacity: 0.4; }
      .scenatorStyle .el-picker-panel .el-picker-panel__footer .el-button--text {
        color: #0854A1; }
        .scenatorStyle .el-picker-panel .el-picker-panel__footer .el-button--text:hover {
          color: #114171; }
    .scenatorStyle .el-picker-panel .el-date-range-picker__content.is-left {
      border-right: 0; }
    .scenatorStyle .el-picker-panel.el-popper[x-placement^=top] .popper__arrow,
    .scenatorStyle .el-picker-panel.el-popper[x-placement^=top] .popper__arrow::after {
      border-top-color: #FFFFFF;
      display: none; }
    .scenatorStyle .el-picker-panel.el-popper[x-placement^=bottom] .popper__arrow,
    .scenatorStyle .el-picker-panel.el-popper[x-placement^=bottom] .popper__arrow::after {
      border-bottom-color: #FFFFFF;
      display: none; }
    .scenatorStyle .el-picker-panel .el-picker-panel__icon-btn {
      color: #0854A1; }
    .scenatorStyle .el-picker-panel .el-picker-panel__body .el-date-range-picker__time-picker-wrap .el-time-panel {
      left: -34px;
      background-color: #FFFFFF;
      border: 0;
      box-shadow: 0px 10px 30px 2px rgba(0, 0, 0, 0.12), 0px 0px 0px 1px #D9D9D9;
      border-radius: 4px; }
      .scenatorStyle .el-picker-panel .el-picker-panel__body .el-date-range-picker__time-picker-wrap .el-time-panel .el-time-spinner__item {
        color: #5D697A; }
        .scenatorStyle .el-picker-panel .el-picker-panel__body .el-date-range-picker__time-picker-wrap .el-time-panel .el-time-spinner__item.active {
          color: #0854A1; }
        .scenatorStyle .el-picker-panel .el-picker-panel__body .el-date-range-picker__time-picker-wrap .el-time-panel .el-time-spinner__item:hover:not(.disabled):not(.active) {
          background: transparent;
          color: #0854A1; }
      .scenatorStyle .el-picker-panel .el-picker-panel__body .el-date-range-picker__time-picker-wrap .el-time-panel .el-time-panel__content::before,
      .scenatorStyle .el-picker-panel .el-picker-panel__body .el-date-range-picker__time-picker-wrap .el-time-panel .el-time-panel__content::after {
        border-bottom: 1px solid #D9D9D9;
        border-top: 1px solid #D9D9D9; }
      .scenatorStyle .el-picker-panel .el-picker-panel__body .el-date-range-picker__time-picker-wrap .el-time-panel .el-time-panel__footer {
        border-top: 1px solid #D9D9D9; }
        .scenatorStyle .el-picker-panel .el-picker-panel__body .el-date-range-picker__time-picker-wrap .el-time-panel .el-time-panel__footer .el-time-panel__btn.confirm {
          color: #0854A1; }
        .scenatorStyle .el-picker-panel .el-picker-panel__body .el-date-range-picker__time-picker-wrap .el-time-panel .el-time-panel__footer .el-time-panel__btn.cancel {
          color: #0854A1; }
  .scenatorStyle .scenator_ztree {
    padding: 0 0 0 16px;
    overflow-x: hidden; }
    .scenatorStyle .scenator_ztree .ztree * {
      font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑"; }
    .scenatorStyle .scenator_ztree .ztree {
      padding: 0px; }
      .scenatorStyle .scenator_ztree .ztree ul,
      .scenatorStyle .scenator_ztree .ztree li {
        list-style: none; }
      .scenatorStyle .scenator_ztree .ztree li a {
        width: 100%;
        padding: 0px;
        display: inline-block; }
      .scenatorStyle .scenator_ztree .ztree li ul {
        padding: 0 0 0 22px; }
      .scenatorStyle .scenator_ztree .ztree span {
        display: inline-block;
        vertical-align: middle; }
      .scenatorStyle .scenator_ztree .ztree .node_name {
        font-size: 14px;
        font-weight: 400;
        color: #182A4E;
        height: 28px;
        line-height: 28px;
        overflow: hidden;
        white-space: nowrap;
        width: calc(100% - 42px);
        text-overflow: ellipsis; }
      .scenatorStyle .scenator_ztree .ztree .node_name::before {
        left: -100%; }
      .scenatorStyle .scenator_ztree .ztree li {
        background: transparent;
        white-space: nowrap; }
      .scenatorStyle .scenator_ztree .ztree li span.button.switch {
        height: 26px;
        width: 16px;
        padding-right: 4px;
        position: relative;
        z-index: 2;
        transition: transform .3s ease-in-out; }
        .scenatorStyle .scenator_ztree .ztree li span.button.switch.bottom_close, .scenatorStyle .scenator_ztree .ztree li span.button.switch.roots_close, .scenatorStyle .scenator_ztree .ztree li span.button.switch.center_close, .scenatorStyle .scenator_ztree .ztree li span.button.switch.root_close, .scenatorStyle .scenator_ztree .ztree li span.button.switch.bottom_open, .scenatorStyle .scenator_ztree .ztree li span.button.switch.roots_open, .scenatorStyle .scenator_ztree .ztree li span.button.switch.center_open, .scenatorStyle .scenator_ztree .ztree li span.button.switch.root_open, .scenatorStyle .scenator_ztree .ztree li span.button.switch.noline_open, .scenatorStyle .scenator_ztree .ztree li span.button.switch.noline_close {
          background: none;
          background-color: transparent;
          border: 1px solid transparent;
          box-sizing: content-box;
          overflow: hidden; }
        .scenatorStyle .scenator_ztree .ztree li span.button.switch.noline_docu {
          width: 16px;
          box-sizing: content-box;
          border: 1px solid transparent; }
        .scenatorStyle .scenator_ztree .ztree li span.button.switch.bottom_close::after, .scenatorStyle .scenator_ztree .ztree li span.button.switch.roots_close::after, .scenatorStyle .scenator_ztree .ztree li span.button.switch.center_close::after, .scenatorStyle .scenator_ztree .ztree li span.button.switch.root_close::after, .scenatorStyle .scenator_ztree .ztree li span.button.switch.noline_close::after {
          content: ' ';
          width: 16px;
          height: 16px;
          border: 1px solid transparent;
          box-sizing: content-box;
          margin-top: calc(50% - 6px);
          display: inline-block;
          background: url("../images/scenator_treeOpen.svg") no-repeat center;
          background-size: 100% 100%;
          transform: rotate(-90deg); }
        .scenatorStyle .scenator_ztree .ztree li span.button.switch.bottom_open::after, .scenatorStyle .scenator_ztree .ztree li span.button.switch.roots_open::after, .scenatorStyle .scenator_ztree .ztree li span.button.switch.center_open::after, .scenatorStyle .scenator_ztree .ztree li span.button.switch.root_open::after, .scenatorStyle .scenator_ztree .ztree li span.button.switch.noline_open::after {
          content: ' ';
          width: 16px;
          height: 16px;
          border: 1px solid transparent;
          display: inline-block;
          box-sizing: content-box;
          margin-top: calc(50% - 6px);
          background: url("../images/scenator_treeOpen.svg") no-repeat center;
          background-size: 100% 100%;
          transform: rotate(0deg); }
      .scenatorStyle .scenator_ztree .ztree li span.button.chk {
        height: 26px;
        width: 16px;
        margin: 0;
        padding-right: 4px;
        border-radius: 2px;
        background: none;
        cursor: pointer;
        position: relative;
        box-sizing: content-box;
        z-index: 2;
        border: 1px solid transparent;
        overflow: hidden; }
        .scenatorStyle .scenator_ztree .ztree li span.button.chk::after {
          content: ' ';
          width: 16px;
          height: 16px;
          border: 1px solid #89919A;
          display: inline-block;
          border-radius: 2px;
          box-sizing: border-box;
          margin-top: calc(50% - 4px); }
        .scenatorStyle .scenator_ztree .ztree li span.button.chk.checkbox_true_part_focus::after {
          background: url("../images/scenator_halfSelected.svg") no-repeat center;
          border-radius: 2px;
          border: 1px solid #0854A1; }
        .scenatorStyle .scenator_ztree .ztree li span.button.chk.checkbox_true_part::after {
          background: url("../images/scenator_halfSelected.svg") no-repeat center;
          border-radius: 2px;
          border: 1px solid #89919A; }
        .scenatorStyle .scenator_ztree .ztree li span.button.chk.checkbox_true_full::after {
          background: url("../images/scenator_rightIncon.svg") no-repeat center;
          border-radius: 2px;
          border: 1px solid #89919A; }
        .scenatorStyle .scenator_ztree .ztree li span.button.chk.checkbox_true_full_focus::after {
          background: url("../images/scenator_rightIncon.svg") no-repeat center;
          border-radius: 2px;
          border: 1px solid #0854A1; }
        .scenatorStyle .scenator_ztree .ztree li span.button.chk.checkbox_false_full_focus::after {
          background: none;
          background-size: 100% 100%;
          border-color: #0854A1; }
        .scenatorStyle .scenator_ztree .ztree li span.button.chk.checkbox_false_full::after {
          background: none;
          background-size: 100% 100%;
          border-radius: 2px; }
      .scenatorStyle .scenator_ztree .ztree li a {
        height: 28px; }
      .scenatorStyle .scenator_ztree .ztree li a.curSelectedNode {
        background-color: rgba(0, 129, 255, 0.12);
        border: 0px;
        opacity: 1;
        height: 28px;
        border-left: 0px; }
        .scenatorStyle .scenator_ztree .ztree li a.curSelectedNode .node_name {
          color: #0854A1; }
      .scenatorStyle .scenator_ztree .ztree li a:hover {
        text-decoration: none;
        background-color: rgba(0, 129, 255, 0.12);
        position: relative; }
        .scenatorStyle .scenator_ztree .ztree li a:hover::before {
          background-color: rgba(0, 129, 255, 0.12); }
      .scenatorStyle .scenator_ztree .ztree li a.selectedParentNode {
        text-decoration: none;
        position: relative;
        background-color: rgba(0, 129, 255, 0.12); }
        .scenatorStyle .scenator_ztree .ztree li a.selectedParentNode::before {
          background-color: rgba(0, 129, 255, 0.12); }
      .scenatorStyle .scenator_ztree .ztree li a.selectedParentNodeFadeIn {
        text-decoration: none;
        position: relative;
        animation: fadeIn 1.5s 1; }
        .scenatorStyle .scenator_ztree .ztree li a.selectedParentNodeFadeIn:before {
          animation: fadeIn 1.5s 1; }
      .scenatorStyle .scenator_ztree .ztree li a.selectedParentNodeFadeOut {
        text-decoration: none;
        position: relative;
        animation: fadeOut 1.5s 1; }
        .scenatorStyle .scenator_ztree .ztree li a.selectedParentNodeFadeOut:before {
          animation: fadeOut 1.5s 1; }
      .scenatorStyle .scenator_ztree .ztree li a {
        position: relative; }
        .scenatorStyle .scenator_ztree .ztree li a:before {
          content: ' ';
          margin-top: 0px;
          width: 1000px;
          height: 100%;
          position: absolute;
          z-index: 0;
          left: -1000px; }
      .scenatorStyle .scenator_ztree .ztree li a.hoverNode {
        text-decoration: none;
        background-color: rgba(0, 129, 255, 0.12);
        position: relative; }
        .scenatorStyle .scenator_ztree .ztree li a.hoverNode:before {
          background-color: rgba(0, 129, 255, 0.12); }
      .scenatorStyle .scenator_ztree .ztree li span.button.ico_open,
      .scenatorStyle .scenator_ztree .ztree li span.button.ico_docu,
      .scenatorStyle .scenator_ztree .ztree li span.button.ico_close,
      .scenatorStyle .scenator_ztree .ztree li span.button.bright_folder_ico_open,
      .scenatorStyle .scenator_ztree .ztree li span.button.bright_folder_ico_docu,
      .scenatorStyle .scenator_ztree .ztree li span.button.bright_node_ico_docu,
      .scenatorStyle .scenator_ztree .ztree li span.button.bright_folder_ico_close {
        background: none;
        width: 16px;
        height: 28px;
        vertical-align: middle;
        position: relative;
        z-index: 2;
        margin-right: 5px;
        display: block;
        float: left; }
      .scenatorStyle .scenator_ztree .ztree li span.button.ico_close::after,
      .scenatorStyle .scenator_ztree .ztree li span.button.bright_folder_ico_close::after,
      .scenatorStyle .scenator_ztree .ztree li span.button.bright_folder_ico_docu::after {
        content: ' ';
        width: 16px;
        height: 16px;
        background: url("../images/scenator_tree_folderClose.svg") no-repeat center;
        border: 1px solid transparent;
        position: absolute;
        left: 0px;
        top: calc(50% - 10px); }
      .scenatorStyle .scenator_ztree .ztree li span.button.ico_open::after,
      .scenatorStyle .scenator_ztree .ztree li span.button.bright_folder_ico_open::after {
        content: ' ';
        width: 16px;
        height: 16px;
        background: url("../images/scenator_tree_folderOpen.svg") no-repeat center;
        border: 1px solid transparent;
        position: absolute;
        left: 0px;
        top: calc(50% - 10px); }
      .scenatorStyle .scenator_ztree .ztree li span.button.ico_docu {
        display: none; }
      .scenatorStyle .scenator_ztree .ztree li span.button.ico_docu::after,
      .scenatorStyle .scenator_ztree .ztree li span.button.bright_node_ico_docu::after {
        content: ' ';
        width: 16px;
        height: 16px;
        background: url("../images/scenator_tree_file.svg") no-repeat center;
        border: 1px solid transparent;
        position: absolute;
        left: 0px;
        top: calc(50% - 10px); }
      .scenatorStyle .scenator_ztree .ztree .curSelectedNode::before {
        background-color: rgba(0, 129, 255, 0.12); }
    .scenatorStyle .scenator_ztree[scenatorstyle="main"] .ztree li span.button.chk {
      display: none; }
    .scenatorStyle .scenator_ztree[scenatorstyle="main"] .ztree li span.button.ico_open,
    .scenatorStyle .scenator_ztree[scenatorstyle="main"] .ztree li span.button.ico_close,
    .scenatorStyle .scenator_ztree[scenatorstyle="main"] .ztree li span.button.ico_docu {
      display: none; }
    .scenatorStyle .scenator_ztree[scenatorstyle="main"] .ztree a {
      width: calc(100% - 22px); }
    .scenatorStyle .scenator_ztree[scenatorstyle="main"] .ztree .node_name {
      width: calc(100% - 16px); }
    .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree {
      position: relative; }
      .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a .scenator-tree-hover-icon {
        height: 20px;
        width: 20px;
        border-radius: 2px;
        position: absolute;
        margin-top: 4px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId1 {
          right: 16px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId2 {
          right: 48px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId3 {
          right: 90px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId4 {
          right: 112px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId5 {
          right: 144px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a .scenator-tree-hover-icon:hover {
          background-color: #E5F0FA;
          border: 1px solid #0854A1; }
        .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a .scenator-tree-hover-icon:active {
          background-color: #0854A1;
          border: 1px solid #0854A1; }
      .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a[hoverIconNum="1"] .node_name {
        width: calc(100% - 16px - 36px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a[hoverIconNum="2"] .node_name {
        width: calc(100% - 16px - 72px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a[hoverIconNum="3"] .node_name {
        width: calc(100% - 16px - 108px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a[hoverIconNum="4"] .node_name {
        width: calc(100% - 16px - 144px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="main"].hoverIcon .ztree a[hoverIconNum="5"] .node_name {
        width: calc(100% - 16px - 180px); }
    .scenatorStyle .scenator_ztree[scenatorstyle="icon"] .ztree li span.button.chk {
      display: none; }
    .scenatorStyle .scenator_ztree[scenatorstyle="icon"] .ztree a {
      min-width: calc(100% - 22px);
      width: max-content; }
    .scenatorStyle .scenator_ztree[scenatorstyle="icon"] .ztree .node_name {
      width: calc(100% - 36px); }
    .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree {
      position: relative; }
      .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a .scenator-tree-hover-icon {
        height: 20px;
        width: 20px;
        border-radius: 2px;
        position: absolute;
        margin-top: 4px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId1 {
          right: 16px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId2 {
          right: 48px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId3 {
          right: 90px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId4 {
          right: 112px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId5 {
          right: 144px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a .scenator-tree-hover-icon:hover {
          background-color: #E5F0FA;
          border: 1px solid #0854A1; }
        .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a .scenator-tree-hover-icon:active {
          background-color: #0854A1;
          border: 1px solid #0854A1; }
      .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a[hoverIconNum="1"] .node_name {
        width: calc(100% - 36px - 36px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a[hoverIconNum="2"] .node_name {
        width: calc(100% - 36px - 72px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a[hoverIconNum="3"] .node_name {
        width: calc(100% - 36px - 108px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a[hoverIconNum="4"] .node_name {
        width: calc(100% - 36px - 144px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="icon"].hoverIcon .ztree a[hoverIconNum="5"] .node_name {
        width: calc(100% - 36px - 180px); }
    .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"] .ztree li span.button.ico_open,
    .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"] .ztree li span.button.ico_close,
    .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"] .ztree li span.button.ico_docu {
      display: none; }
    .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"] .ztree a {
      width: calc(100% - 44px); }
    .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"] .ztree .node_name {
      width: calc(100% - 16px); }
    .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree {
      position: relative; }
      .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a .scenator-tree-hover-icon {
        height: 20px;
        width: 20px;
        border-radius: 2px;
        position: absolute;
        margin-top: 4px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId1 {
          right: 16px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId2 {
          right: 48px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId3 {
          right: 90px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId4 {
          right: 112px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId5 {
          right: 144px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a .scenator-tree-hover-icon:hover {
          background-color: #E5F0FA;
          border: 1px solid #0854A1; }
        .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a .scenator-tree-hover-icon:active {
          background-color: #0854A1;
          border: 1px solid #0854A1; }
      .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a[hoverIconNum="1"] .node_name {
        width: calc(100% - 16px - 36px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a[hoverIconNum="2"] .node_name {
        width: calc(100% - 16px - 72px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a[hoverIconNum="3"] .node_name {
        width: calc(100% - 16px - 108px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a[hoverIconNum="4"] .node_name {
        width: calc(100% - 16px - 144px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="checkBox"].hoverIcon .ztree a[hoverIconNum="5"] .node_name {
        width: calc(100% - 16px - 180px); }
    .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"] .ztree a {
      width: calc(100% - 44px); }
    .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"] .ztree .node_name {
      width: calc(100% - 36px); }
    .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree {
      position: relative; }
      .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a .scenator-tree-hover-icon {
        height: 20px;
        width: 20px;
        border-radius: 2px;
        position: absolute;
        margin-top: 4px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId1 {
          right: 16px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId2 {
          right: 48px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId3 {
          right: 80px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId4 {
          right: 112px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a .scenator-tree-hover-icon.hoverIconId5 {
          right: 144px; }
        .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a .scenator-tree-hover-icon:hover {
          background-color: #E5F0FA;
          border: 1px solid #0854A1; }
        .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a .scenator-tree-hover-icon:active {
          background-color: #0854A1;
          border: 1px solid #0854A1; }
      .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a[hoverIconNum="1"] .node_name {
        width: calc(100% - 36px - 36px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a[hoverIconNum="2"] .node_name {
        width: calc(100% - 36px - 72px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a[hoverIconNum="3"] .node_name {
        width: calc(100% - 36px - 108px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a[hoverIconNum="4"] .node_name {
        width: calc(100% - 36px - 144px); }
      .scenatorStyle .scenator_ztree[scenatorstyle="check+Icon"].hoverIcon .ztree a[hoverIconNum="5"] .node_name {
        width: calc(100% - 36px - 180px); }
  .scenatorStyle .scenator_ztree.overflow_scroll {
    overflow: auto;
    overflow-y: overlay; }
    .scenatorStyle .scenator_ztree.overflow_scroll .ztree {
      display: inline-block;
      min-width: 100%; }
      .scenatorStyle .scenator_ztree.overflow_scroll .ztree a {
        overflow: initial; }
      .scenatorStyle .scenator_ztree.overflow_scroll .ztree .node_name {
        overflow: initial;
        width: auto !important; }
      .scenatorStyle .scenator_ztree.overflow_scroll .ztree .scenator-tree-hover-icon {
        position: absolute;
        margin-top: 4px; }
        .scenatorStyle .scenator_ztree.overflow_scroll .ztree .scenator-tree-hover-icon.hoverIconId1 {
          right: 16px; }
        .scenatorStyle .scenator_ztree.overflow_scroll .ztree .scenator-tree-hover-icon.hoverIconId2 {
          right: 48px; }
  .scenatorStyle .el-tree {
    background: transparent; }
    .scenatorStyle .el-tree .el-tree-node__content {
      height: 28px; }
    .scenatorStyle .el-tree .el-tree-node.is-current > .el-tree-node__content {
      background-color: rgba(0, 129, 255, 0.12); }
      .scenatorStyle .el-tree .el-tree-node.is-current > .el-tree-node__content .el-tree-node__label {
        color: #0854A1; }
    .scenatorStyle .el-tree .el-tree-node__content:hover {
      background-color: rgba(0, 129, 255, 0.12); }
      .scenatorStyle .el-tree .el-tree-node__content:hover .el-tree-node__label {
        color: #0854A1; }
    .scenatorStyle .el-tree .is-current .el-tree-node__content:hover {
      background-color: rgba(0, 129, 255, 0.12); }
      .scenatorStyle .el-tree .is-current .el-tree-node__content:hover .el-tree-node__label {
        color: #0854A1; }
    .scenatorStyle .el-tree .el-icon-caret-right:before {
      content: " ";
      width: 16px;
      width: 16px;
      position: absolute;
      background-size: 100% 100%; }
    .scenatorStyle .el-tree .el-tree-node__content > .el-tree-node__expand-icon {
      background: url("../images/scenator_treeOpen.svg") no-repeat center;
      transform: rotate(-90deg);
      height: 28px;
      width: 16px;
      margin-right: 4px; }
    .scenatorStyle .el-tree .el-tree-node__expand-icon.expanded {
      transform: rotate(0deg); }
    .scenatorStyle .el-tree .el-tree-node__expand-icon.is-leaf {
      color: transparent;
      cursor: default;
      background: none; }
    .scenatorStyle .el-tree .el-tree-node__label {
      color: #182A4E;
      position: relative; }
    .scenatorStyle .el-tree .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content .el-tree-node__label {
      font-size: 14px;
      color: #0854A1; }
    .scenatorStyle .el-tree .el-checkbox__inner {
      border: 1px solid #89919A;
      width: 16px;
      height: 16px;
      background-color: transparent; }
    .scenatorStyle .el-tree .el-checkbox__inner::after {
      left: 5px;
      top: 2px; }
    .scenatorStyle .el-tree .el-checkbox__inner:hover {
      border: 1px solid #0854A1; }
    .scenatorStyle .el-tree .el-checkbox__input.is-checked .el-checkbox__inner,
    .scenatorStyle .el-tree .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #FFFFFF; }
    .scenatorStyle .el-tree .el-checkbox__inner::after {
      border-color: #0854A1; }
    .scenatorStyle .el-tree[scenatorStyle="icon"] .el-tree-node__label {
      text-indent: 20px;
      display: inline-block; }
    .scenatorStyle .el-tree[scenatorStyle="icon"] .el-tree-node__expand-icon.expanded ~ .el-tree-node__label:before {
      background: url("../images/scenator_tree_folderOpen.svg") no-repeat center; }
    .scenatorStyle .el-tree[scenatorStyle="icon"] .el-tree-node__label:before {
      content: " ";
      width: 16px;
      height: 16px;
      position: absolute;
      background: url("../images/scenator_tree_folderClose.svg") no-repeat center;
      left: 0px;
      top: 2px;
      background-size: 100% 100%; }
    .scenatorStyle .el-tree[scenatorStyle="icon"] .el-tree-node__expand-icon.is-leaf ~ .el-tree-node__label:before,
    .scenatorStyle .el-tree[scenatorStyle="icon"] .el-tree-node__expand-icon.is-leaf ~ .custom-tree-node .el-tree-node__label:before {
      background: url("../images/scenator_tree_file.svg") no-repeat center; }
    .scenatorStyle .el-tree .el-checkbox__input .el-checkbox__inner::before,
    .scenatorStyle .el-tree .el-checkbox__input .el-checkbox__inner::after {
      width: 16px;
      height: 16px;
      left: -1px;
      top: -1px;
      border-radius: 2px;
      border: none;
      -webkit-transform: rotate(0deg) scaleY(1);
      transform: rotate(0deg) scaleY(1); }
    .scenatorStyle .el-tree .el-checkbox__input.is-checked .el-checkbox__inner::after {
      background: url("../images/scenator_rightIncon.svg") no-repeat center; }
    .scenatorStyle .el-tree .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      background: url("../images/scenator_halfSelected.svg") no-repeat center; }
    .scenatorStyle .el-tree .el-tree-node__content > label.el-checkbox {
      margin-right: 5px; }
    .scenatorStyle .el-tree.hoverIcon .el-tree-node__content:hover > .custom-tree-node > .scenator-tree-hover-icon {
      visibility: visible; }
    .scenatorStyle .el-tree.hoverIcon .is-current[role="treeitem"] > .el-tree-node__content > .custom-tree-node > .scenator-tree-hover-icon {
      visibility: visible; }
    .scenatorStyle .el-tree.hoverIcon .el-tree-node__content ~ .is-current .el-tree-node.is-current > .el-tree-node__content .el-tree-node__expand-icon.is-leaf ~ .custom-tree-node > .el-tree-node__label ~ .scenator-tree-hover-icon {
      visibility: visible; }
    .scenatorStyle .el-tree.hoverIcon .scenator-tree-hover-icon {
      height: 20px;
      width: 20px;
      border-radius: 2px;
      position: absolute;
      visibility: hidden; }
      .scenatorStyle .el-tree.hoverIcon .scenator-tree-hover-icon.hoverIconId1 {
        right: 16px; }
      .scenatorStyle .el-tree.hoverIcon .scenator-tree-hover-icon.hoverIconId2 {
        right: 48px; }
      .scenatorStyle .el-tree.hoverIcon .scenator-tree-hover-icon.hoverIconId3 {
        right: 80px; }
      .scenatorStyle .el-tree.hoverIcon .scenator-tree-hover-icon.hoverIconId4 {
        right: 112px; }
      .scenatorStyle .el-tree.hoverIcon .scenator-tree-hover-icon.hoverIconId5 {
        right: 144px; }
      .scenatorStyle .el-tree.hoverIcon .scenator-tree-hover-icon:hover {
        background-color: #E5F0FA;
        border: 1px solid #0854A1; }
      .scenatorStyle .el-tree.hoverIcon .scenator-tree-hover-icon:active {
        background-color: #0854A1;
        border: 1px solid #0854A1; }
  .scenatorStyle .table {
    border: 1px solid #D9D9D9;
    box-shadow: none;
    overflow: auto;
    width: 100%;
    table-layout: fixed; }
    .scenatorStyle .table tr {
      height: 40px;
      background: #FFFFFF;
      box-shadow: inset 0px -1px 0px 0px #D9D9D9; }
      .scenatorStyle .table tr th,
      .scenatorStyle .table tr td {
        padding: 0 12px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        text-align: left;
        border: 1px solid #D9D9D9;
        min-width: 56px;
        max-width: 400px;
        color: #182A4E; }
      .scenatorStyle .table tr th {
        background: #EFEFEF; }
      .scenatorStyle .table tr td:hover {
        background: #EFF2F4; }
      .scenatorStyle .table tr:hover,
      .scenatorStyle .table tr:hover td, .scenatorStyle .table tr.table__row--striped:hover,
      .scenatorStyle .table tr.table__row--striped:hover td {
        background: #EFF2F4; }
      .scenatorStyle .table tr.table__row--striped,
      .scenatorStyle .table tr.table__row--striped td {
        background: #FAFAFA; }
      .scenatorStyle .table tr.current-row, .scenatorStyle .table tr.current-row:hover,
      .scenatorStyle .table tr.current-row td,
      .scenatorStyle .table tr.current-row:hover td {
        background: #E5F0FA;
        box-shadow: inset 0px -1px 0px 0px #0854A1; }
  .scenatorStyle table {
    margin: 0px;
    border: 0px;
    display: table; }
    .scenatorStyle table tr,
    .scenatorStyle table th,
    .scenatorStyle table td {
      background: #FFFFFF;
      height: 40px; }
  .scenatorStyle .el-table {
    background: #FFFFFF;
    box-shadow: inset -1px -1px 0px 0px #D9D9D9; }
    .scenatorStyle .el-table tr,
    .scenatorStyle .el-table th,
    .scenatorStyle .el-table td {
      background: #FFFFFF;
      height: 40px; }
    .scenatorStyle .el-table.el-table--border, .scenatorStyle .el-table.el-table--group {
      border-color: #D9D9D9; }
      .scenatorStyle .el-table.el-table--border th.el-table__cell.gutter:last-of-type, .scenatorStyle .el-table.el-table--group th.el-table__cell.gutter:last-of-type {
        border-color: #D9D9D9; }
    .scenatorStyle .el-table td.el-table__cell,
    .scenatorStyle .el-table th.el-table__cell.is-leaf {
      border-bottom-color: #D9D9D9; }
    .scenatorStyle .el-table .el-table__cell,
    .scenatorStyle .el-table .el-table__row td,
    .scenatorStyle .el-table .el-table__header th {
      padding: 0 12px; }
    .scenatorStyle .el-table .el-table__header tr,
    .scenatorStyle .el-table .el-table__header th {
      background: #EFEFEF; }
    .scenatorStyle .el-table .el-table__body .el-table__row {
      border: 0; }
      .scenatorStyle .el-table .el-table__body .el-table__row:hover td.el-table__cell,
      .scenatorStyle .el-table .el-table__body .el-table__row.el-table__row--striped:hover td.el-table__cell,
      .scenatorStyle .el-table .el-table__body .el-table__row.hover-row > td.el-table__cell,
      .scenatorStyle .el-table .el-table__body .el-table__row.el-table__row--striped.hover-row td.el-table__cell {
        background: #EFF2F4; }
      .scenatorStyle .el-table .el-table__body .el-table__row.el-table__row--striped td.el-table__cell {
        background: #FAFAFA; }
      .scenatorStyle .el-table .el-table__body .el-table__row.current-row {
        box-shadow: inset 0px -1px 0px 0px #0854A1; }
      .scenatorStyle .el-table .el-table__body .el-table__row.current-row td.el-table__cell,
      .scenatorStyle .el-table .el-table__body .el-table__row.el-table__row--striped.current-row td.el-table__cell {
        background: #E5F0FA;
        box-shadow: inset 0px -1px 0px 0px #0854A1; }
    .scenatorStyle .el-table tr {
      box-shadow: inset 0px -1px 0px 0px #D9D9D9; }
    .scenatorStyle .el-table th,
    .scenatorStyle .el-table td {
      border: 0;
      border-right: 1px solid #D9D9D9;
      border-bottom: 1px solid #D9D9D9;
      padding: 0 12px;
      color: #182A4E;
      min-width: 56px;
      max-width: 400px; }
      .scenatorStyle .el-table th.el-table__cell > .cell,
      .scenatorStyle .el-table td.el-table__cell > .cell {
        padding-left: 0;
        padding-right: 0;
        white-space: nowrap; }
      .scenatorStyle .el-table th .cell.el-tooltip,
      .scenatorStyle .el-table td .cell.el-tooltip {
        width: auto !important; }
    .scenatorStyle .el-table.el-table__cell:first-child .cell {
      padding-left: 0;
      padding-right: 0; }
    .scenatorStyle .el-table .el-table__empty-block {
      border-right: 1px solid #D9D9D9;
      border-bottom: 1px solid #D9D9D9; }
      .scenatorStyle .el-table .el-table__empty-block .el-table__empty-text {
        color: #182A4E; }
    .scenatorStyle .el-table::before, .scenatorStyle .el-table::after {
      display: none; }
    .scenatorStyle .el-table .el-table__cell.el-table-column--selection .cell {
      text-align: center; }
    .scenatorStyle .el-table .el-table__cell.el-table-column--selection .el-checkbox {
      color: #182A4E;
      font-size: 14px; }
      .scenatorStyle .el-table .el-table__cell.el-table-column--selection .el-checkbox .el-checkbox__input .el-checkbox__inner {
        border: 1px solid #89919A;
        width: 16px;
        height: 16px;
        background-color: #FFFFFF; }
        .scenatorStyle .el-table .el-table__cell.el-table-column--selection .el-checkbox .el-checkbox__input .el-checkbox__inner::after {
          border-color: #0854A1;
          top: 2px;
          left: 5px; }
      .scenatorStyle .el-table .el-table__cell.el-table-column--selection .el-checkbox .el-checkbox__input.is-checked .el-checkbox__inner,
      .scenatorStyle .el-table .el-table__cell.el-table-column--selection .el-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background: #FFFFFF;
        border-color: #89919A; }
      .scenatorStyle .el-table .el-table__cell.el-table-column--selection .el-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
        background-color: #0854A1;
        top: 3px;
        left: 3px;
        height: 8px;
        width: 8px;
        transform: rotate(0) scaleY(1); }
      .scenatorStyle .el-table .el-table__cell.el-table-column--selection .el-checkbox:hover .el-checkbox__inner,
      .scenatorStyle .el-table .el-table__cell.el-table-column--selection .el-checkbox.is-checked:hover .el-checkbox__inner,
      .scenatorStyle .el-table .el-table__cell.el-table-column--selection .el-checkbox:hover .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        border: 1px solid #0854A1; }
    .scenatorStyle .el-table .el-table__cell.el-table__expand-column .el-table__expand-icon .el-icon-arrow-right::before {
      content: " ";
      width: 16px;
      height: 16px;
      position: absolute;
      background: url("../images/scenator_treeOpen.svg") no-repeat center;
      transform: rotate(-90deg);
      background-size: 100% 100%; }
    .scenatorStyle .el-table .el-table__cell.el-table__expand-column .el-table__expand-icon--expanded .el-icon-arrow-right:before {
      transform: rotate(270deg); }
    .scenatorStyle .el-table .el-table__cell.el-table__expand-column .el-table__expand-icon > .el-icon {
      left: 50%;
      top: 50%;
      transform: translate(-8px, -8px);
      margin-left: 0;
      margin-top: 0; }
    .scenatorStyle .el-table .el-table__fixed-right-patch {
      background: #EFEFEF;
      border-bottom: 0;
      box-shadow: inset -1px -1px 0px 0px #D9D9D9; }
    .scenatorStyle .el-table .el-table__footer-wrapper td.el-table__cell {
      border-color: #D9D9D9; }
  .scenatorStyle .el-table__fixed::before,
  .scenatorStyle .el-table__fixed-right::before {
    display: none; }
  .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] {
    border: 1px solid #D9D9D9;
    border-bottom: 0px;
    border-right: 0px; }
    .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] .el-table--border::after,
    .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] .el-table--group::after,
    .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] .el-table::before {
      background-color: transparent;
      width: 0px; }
    .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] .el-table--border::after,
    .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] .el-table--group::after,
    .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] .el-table::before {
      content: none; }
  .scenatorStyle .el-table[scenatorStyle="scenator_smallTable"] tr,
  .scenatorStyle .el-table[scenatorStyle="scenator_smallTable"] th,
  .scenatorStyle .el-table[scenatorStyle="scenator_smallTable"] td {
    height: 32px; }
  .scenatorStyle .el-table[scenatorStyle="scenator_smallTable"] .el-table__row td,
  .scenatorStyle .el-table[scenatorStyle="scenator_smallTable"] .el-table__header th {
    padding: 0 12px; }
  .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] .el-icon-arrow-right:before {
    content: " ";
    width: 16px;
    height: 16px;
    position: absolute;
    background: url("../images/scenator_treeOpen.svg") no-repeat center;
    transform: rotate(-90deg);
    background-size: 100% 100%; }
  .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] .el-table__expand-icon--expanded .el-icon-arrow-right:before {
    transform: rotate(270deg); }
  .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] .el-table__expand-icon > .el-icon {
    left: 50%;
    top: 50%;
    transform: translate(-8px, -8px);
    margin-left: 0;
    margin-top: 0; }
  .scenatorStyle .el-table[scenatorstyle="scenator_expandNoBorder"] .el-table__expanded-cell {
    padding: 8px 16px; }
  .scenatorStyle .scenatorStyleNavCard {
    width: 280px;
    height: 60px;
    display: flex;
    flex-direction: column;
    padding: 8px;
    background-color: rgba(24, 42, 78, 0.06);
    border: 1px solid rgba(24, 42, 78, 0.12);
    border-radius: 2px;
    cursor: pointer; }
    .scenatorStyle .scenatorStyleNavCard.active {
      background-color: rgba(0, 129, 255, 0.12);
      border: 1px solid #0854A1; }
    .scenatorStyle .scenatorStyleNavCard .nav-card-name {
      color: #182A4E;
      margin-bottom: 4px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
      font-weight: 400;
      width: 100%;
      overflow: hidden; }
      .scenatorStyle .scenatorStyleNavCard .nav-card-name .nav-card-icon {
        height: 16px;
        width: 16px;
        display: inline-block;
        border-radius: 4px;
        background-position-y: 2px;
        margin-right: 4px; }
    .scenatorStyle .scenatorStyleNavCard .nav-card-text {
      color: #5D697A;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      margin-left: 20px; }
    .scenatorStyle .scenatorStyleNavCard:hover {
      background-color: rgba(0, 129, 255, 0.12);
      border: 1px solid #0854A1; }
  .scenatorStyle .scenatorStyleCardSecondary {
    width: 100%;
    height: 40px;
    border: 1px solid #89919A;
    box-sizing: border-box;
    padding: 10px 12px;
    margin: 8px 0;
    background-color: rgba(24, 42, 78, 0.06);
    cursor: pointer; }
    .scenatorStyle .scenatorStyleCardSecondary:hover {
      color: #0854A1;
      border-color: #0854A1;
      background-color: rgba(137, 145, 154, 0.3); }
    .scenatorStyle .scenatorStyleCardSecondary.active {
      color: #0854A1;
      border-color: #0854A1;
      background-color: rgba(137, 145, 154, 0.3); }
  .scenatorStyle .scenatorStyleListItem .list-item-wrapper {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid rgba(24, 42, 78, 0.12);
    font-size: 14px;
    padding: 13px 0 16px;
    margin: 0 16px; }
    .scenatorStyle .scenatorStyleListItem .list-item-wrapper .list-item-name {
      color: #182A4E;
      height: 20px;
      line-height: 20px;
      white-space: nowrap; }
    .scenatorStyle .scenatorStyleListItem .list-item-wrapper .list-item-text {
      margin-top: 4px;
      color: #5D697A;
      height: 20px;
      line-height: 20px;
      white-space: nowrap; }
  .scenatorStyle .scenatorStyleListItem:hover {
    background-color: rgba(0, 129, 255, 0.12); }
  .scenatorStyle .scenatorStyleListItem.active {
    background-color: rgba(0, 129, 255, 0.12); }
    .scenatorStyle .scenatorStyleListItem.active .list-item-name {
      color: #0A6ED1; }
  .scenatorStyle .el-select-dropdown {
    background: #FFFFFF;
    box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px #D9D9D9;
    border-radius: 4px;
    border: 0;
    overflow: hidden; }
    .scenatorStyle .el-select-dropdown.el-popper[x-placement^=bottom] {
      margin-top: 3px; }
    .scenatorStyle .el-select-dropdown.el-popper[x-placement^=top] {
      margin-bottom: 3px; }
    .scenatorStyle .el-select-dropdown .el-select-dropdown__list {
      padding: 0;
      max-height: 192px;
      overflow-y: auto; }
      .scenatorStyle .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item {
        color: #182A4E;
        background: #FFFFFF;
        height: 32px;
        line-height: 32px;
        padding: 0 16px; }
        .scenatorStyle .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item.hover, .scenatorStyle .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item:hover {
          background-color: #EFF2F4; }
          .scenatorStyle .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item.hover .el-checkbox__input .el-checkbox__inner, .scenatorStyle .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item:hover .el-checkbox__input .el-checkbox__inner {
            border: 1px solid #0854A1; }
        .scenatorStyle .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item.selected {
          color: #0854A1;
          box-shadow: inset 0px -1px 0px 0px #0854A1;
          background-color: rgba(0, 129, 255, 0.12); }
          .scenatorStyle .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item.selected.hover {
            background-color: #EFF2F4; }
          .scenatorStyle .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item.selected .el-checkbox__input .el-checkbox__inner {
            border: 1px solid #89919A; }
        .scenatorStyle .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__item .el-checkbox__input .el-checkbox__inner {
          border: 1px solid #89919A;
          background: transparent; }
    .scenatorStyle .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
      color: #182A4E;
      box-shadow: inset 0px -1px 0px 0px #0854A1;
      background-color: #E5F0FA; }
      .scenatorStyle .el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
        background-color: #E5F0FA; }
    .scenatorStyle .el-select-dropdown .popper__arrow {
      display: none; }
  .scenatorStyle .el-tooltip__popper {
    max-width: 320px !important;
    padding: 4px 8px;
    color: white;
    font-size: 14px;
    line-height: 24px;
    border-radius: 2px;
    background: black;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.35);
    opacity: 1; }
    .scenatorStyle .el-tooltip__popper.is-dark {
      background: black; }
    .scenatorStyle .el-tooltip__popper[x-placement="top"] {
      margin-top: 15px; }
    .scenatorStyle .el-tooltip__popper .popper__arrow {
      display: none; }
  .scenatorStyle .el-pagination {
    height: 48px; }
    .scenatorStyle .el-pagination .el-pager {
      margin: 0 8px; }
      .scenatorStyle .el-pagination .el-pager li {
        border-radius: 2px;
        border: 1px solid transparent;
        background: transparent;
        width: 28px;
        height: 28px !important;
        line-height: 28px !important;
        font-size: 14px !important;
        color: #5D697A;
        font-weight: normal;
        margin-right: 8px; }
        .scenatorStyle .el-pagination .el-pager li:nth-last-child(1) {
          margin: 0; }
        .scenatorStyle .el-pagination .el-pager li:hover {
          background: #EFF2F4; }
        .scenatorStyle .el-pagination .el-pager li.active {
          border-color: #0854A1;
          color: #0854A1; }
    .scenatorStyle .el-pagination .el-pagination__sizes .el-input .el-input__inner:hover {
      border-color: #0854A1; }
    .scenatorStyle .el-pagination .el-pagination__sizes .el-select {
      height: 100%; }
      .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-input__inner {
        color: #182A4E;
        height: 100%;
        border-color: #89919A;
        border-radius: 2px; }
      .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-input {
        height: 100%; }
        .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-input .el-input__inner {
          border-radius: 2px;
          padding: 0 5px;
          padding-right: 32px; }
        .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-input .el-input__suffix {
          height: 28px; }
        .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-input.is-focus {
          background: #E5F0FA; }
          .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-input.is-focus .el-input__inner {
            border-color: #0854A1; }
          .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-input.is-focus .el-input__icon {
            margin-top: -1px; }
          .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-input.is-focus .el-input__suffix-inner {
            display: flex;
            justify-content: center;
            align-items: center; }
        .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-input .el-input__icon {
          line-height: inherit;
          width: 20px;
          margin-top: -2px;
          line-height: 32px; }
      .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-select:hover .el-input__suffix {
        background: #E5F0FA;
        height: 28px; }
      .scenatorStyle .el-pagination .el-pagination__sizes .el-select .el-select:hover .el-input__icon {
        color: #0854A1; }
    .scenatorStyle .el-pagination .el-pagination__rightwrapper {
      margin-right: auto; }
      .scenatorStyle .el-pagination .el-pagination__rightwrapper span {
        font-size: 14px;
        color: #5D697A; }
      .scenatorStyle .el-pagination .el-pagination__rightwrapper + span.el-pagination__total {
        font-size: 14px; }
      .scenatorStyle .el-pagination .el-pagination__rightwrapper button {
        background: transparent; }
    .scenatorStyle .el-pagination button {
      background: transparent;
      position: relative; }
      .scenatorStyle .el-pagination button:disabled {
        background: transparent; }
    .scenatorStyle .el-pagination .el-pagination__jump {
      color: #5D697A; }
    .scenatorStyle .el-pagination .el-icon.el-icon-arrow-left:before {
      content: " ";
      width: 16px;
      height: 16px;
      background: url("../images/scenator_treeOpen.svg") no-repeat center;
      transform: rotate(90deg);
      background-size: 100% 100%;
      position: absolute;
      top: 5px;
      left: 2px; }
    .scenatorStyle .el-pagination .el-icon.el-icon-arrow-right:before {
      content: " ";
      width: 16px;
      height: 16px;
      background: url("../images/scenator_treeOpen.svg") no-repeat center;
      transform: rotate(-90deg);
      background-size: 100% 100%;
      position: absolute;
      top: 5px;
      left: 2px; }
    .scenatorStyle .el-pagination .btn-prev[disabled="disabled"] .el-icon-arrow-left:before {
      opacity: 0.4; }
    .scenatorStyle .el-pagination .btn-next[disabled="disabled"] .el-icon-arrow-right:before {
      opacity: 0.4; }
  .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] {
    padding: 10px 0; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .btn-prev {
      position: absolute;
      right: 75px;
      padding: 0px; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .btn-next {
      position: absolute;
      padding: 0px;
      right: -2px; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] button {
      min-width: 20px; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .el-icon-arrow-right:before {
      top: 5px; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .el-icon-arrow-left:before {
      content: " ";
      width: 16px;
      height: 16px;
      background: url("../images/scenator_treeOpen.svg") no-repeat center;
      transform: rotate(90deg);
      background-size: 100% 100%;
      position: absolute;
      top: 5px;
      left: 2px; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .el-icon-arrow-right:before {
      content: " ";
      width: 16px;
      height: 16px;
      background: url("../images/scenator_treeOpen.svg") no-repeat center;
      transform: rotate(-90deg);
      background-size: 100% 100%;
      position: absolute;
      top: 5px;
      left: 2px; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .el-pagination__sizes {
      position: absolute;
      right: 95px; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .el-pagination__rightwrapper {
      position: relative; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .el-pagination__total {
      color: #5D697A; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .scenator_pageNum span {
      color: #5D697A; }
      .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .scenator_pageNum span:nth-child(1) {
        margin-right: 140px; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .scenator_pageNum .el-input {
      width: 48px;
      margin-right: 23px;
      height: 28px; }
      .scenatorStyle .el-pagination[scenatorStyle="pagination_normal"] .scenator_pageNum .el-input .el-input__inner {
        height: 28px; }
  .scenatorStyle .el-pagination[scenatorStyle="pagination_full"] {
    display: flex;
    align-items: center;
    margin: 0;
    border-top: none;
    box-sizing: border-box; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_full"] .el-pagination__sizes {
      order: 2; }
      .scenatorStyle .el-pagination[scenatorStyle="pagination_full"] .el-pagination__sizes input {
        font-size: 14px;
        color: #182A4E;
        height: 28px;
        border-radius: 2px; }
    .scenatorStyle .el-pagination[scenatorStyle="pagination_full"] .el-pagination__jump {
      order: 3;
      margin-left: 0;
      height: fit-content;
      line-height: 28px;
      font-size: 14px; }
      .scenatorStyle .el-pagination[scenatorStyle="pagination_full"] .el-pagination__jump input {
        height: 28px;
        border-radius: 2px;
        width: 48px; }
      .scenatorStyle .el-pagination[scenatorStyle="pagination_full"] .el-pagination__jump .el-pagination__editor {
        vertical-align: bottom; }
  .scenatorStyle .el-tabs--card .el-tabs__header {
    box-shadow: inset 0px -1px 0px 0px rgba(24, 42, 78, 0.12);
    border-bottom: 0;
    height: 44px;
    line-height: 44px; }
    .scenatorStyle .el-tabs--card .el-tabs__header .el-tabs__nav-wrap::after {
      background: none; }
    .scenatorStyle .el-tabs--card .el-tabs__header .el-tabs__active-bar {
      background-color: #0854A1; }
    .scenatorStyle .el-tabs--card .el-tabs__header .el-tabs__item.is-active {
      color: #0854A1; }
  .scenatorStyle .el-tabs--card .el-tabs__item {
    color: #182A4E;
    padding: 0;
    height: 100%;
    line-height: 100%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 24px;
    background-color: transparent;
    display: inline-block;
    line-height: inherit;
    border-bottom: 0; }
    .scenatorStyle .el-tabs--card .el-tabs__item .el-icon-close {
      position: unset;
      display: inline-block; }
  .scenatorStyle .el-tabs--card.el-tabs--top > .el-tabs__header .el-tabs__item {
    transition: width 0.3s; }
    .scenatorStyle .el-tabs--card.el-tabs--top > .el-tabs__header .el-tabs__item.is-active.is-closable, .scenatorStyle .el-tabs--card.el-tabs--top > .el-tabs__header .el-tabs__item:nth-child(2), .scenatorStyle .el-tabs--card.el-tabs--top > .el-tabs__header .el-tabs__item:last-child {
      padding-left: 0;
      padding-right: 0; }
    .scenatorStyle .el-tabs--card.el-tabs--top > .el-tabs__header .el-tabs__item:last-child {
      margin-right: 0; }
  .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__item {
    border-left: 1px solid transparent;
    position: relative; }
  .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__nav-wrap,
  .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__nav-scroll {
    height: 100%; }
    .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav,
    .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__nav-scroll .el-tabs__nav {
      border: 0;
      background-color: transparent;
      height: 100%; }
  .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::after {
    content: "";
    width: 100%;
    height: 3px;
    background: #0854A1;
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px; }
  .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__item.is-closable:hover {
    padding-left: 0px;
    padding-right: 0px; }
  .scenatorStyle .el-tabs--card .el-tabs__item:hover {
    padding: 0px;
    color: #0854A1; }
  .scenatorStyle .el-tabs--card .el-table__expand-icon {
    height: 20px;
    width: 20px; }
  .scenatorStyle .el-tabs--card .el-table__expand-icon > .el-icon {
    height: 20px;
    width: 20px;
    left: 0px;
    top: 0px;
    margin-left: 0px;
    margin-top: 0px; }
  .scenatorStyle .el-tabs--card .el-table__row.expanded td.el-table__cell {
    border: rgba(0, 0, 0, 0); }
  .scenatorStyle .el-tabs--card .el-tabs__nav-next,
  .scenatorStyle .el-tabs--card .el-tabs__nav-prev {
    width: 32px;
    display: flex;
    height: 100%;
    justify-content: center;
    align-items: center; }
  .scenatorStyle .el-tabs--card .el-tabs__nav-prev {
    box-shadow: 1px 0px 4px 0px rgba(0, 0, 0, 0.2); }
  .scenatorStyle .el-tabs--card .el-tabs__nav-next {
    box-shadow: -1px 0px 4px 0px rgba(0, 0, 0, 0.2); }
  .scenatorStyle .el-tabs--card .el-icon-arrow-left,
  .scenatorStyle .el-tabs--card .el-icon-arrow-right {
    width: 20px;
    height: 20px;
    border-radius: 2px;
    border: 1px solid transparent; }
  .scenatorStyle .el-tabs--card .el-icon-arrow-left:before,
  .scenatorStyle .el-tabs--card .el-icon-arrow-right:before {
    content: " "; }
  .scenatorStyle .el-tabs--card .el-icon-arrow-right:hover,
  .scenatorStyle .el-tabs--card .el-icon-arrow-left:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .el-tabs--card .el-icon-arrow-right:active,
  .scenatorStyle .el-tabs--card .el-icon-arrow-left:active {
    background-color: #0854A1;
    border: 1px solid #0854A1; }
  .scenatorStyle .el-tabs--card .el-icon-plus {
    width: 20px;
    height: 20px;
    border: 1px solid transparent;
    transform: scale(1); }
  .scenatorStyle .el-tabs--card .el-icon-plus::before {
    content: " "; }
  .scenatorStyle .el-tabs--card .el-icon-plus:hover {
    background-color: #E5F0FA;
    border-radius: 2px;
    border: 1px solid #0854A1; }
  .scenatorStyle .el-tabs--card .el-icon-plus:active {
    background-color: #0854A1;
    border-radius: 2px;
    border: 1px solid #0854A1; }
  .scenatorStyle .el-tabs--card .el-tabs__new-tab {
    width: 32px;
    height: 100%;
    margin: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 0px;
    border-radius: 0px;
    border-left: #D9D9D9 1px solid; }
  .scenatorStyle .el-tabs--card .el-tabs__nav-wrap.is-scrollable {
    padding: 0 32px; }
  .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .el-icon-close {
    padding: 0px;
    height: 16px;
    width: 16px;
    background-size: 16px 16px;
    border: 1px solid transparent;
    box-sizing: border-box;
    border-radius: 2px;
    cursor: pointer; }
  .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .el-icon-close:hover {
    border-color: #0854A1;
    background-color: #E5F0FA; }
  .scenatorStyle .el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .el-icon-close:active {
    border-color: #0854A1;
    background-color: #0854A1; }
  .scenatorStyle .el-tabs--card .el-tabs__item .el-icon-close {
    display: none; }
  .scenatorStyle .el-tabs--card .el-tabs__header .el-tabs__item.is-closable:hover .el-icon-close,
  .scenatorStyle .el-tabs--card .el-tabs__header .el-tabs__item.is-active.is-closable .el-icon-close {
    position: static;
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-left: 4px; }
  .scenatorStyle .el-tabs--card .el-icon-close:hover {
    border-radius: 2px; }
  .scenatorStyle .el-tabs--card .el-icon-close:before {
    content: " "; }
  .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"], .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] {
    border: 0px; }
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] > .el-tabs__header .el-tabs__item.is-closable .el-icon-close, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] > .el-tabs__header .el-tabs__item.is-closable .el-icon-close {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%); }
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] .el-tabs__header .el-tabs__item {
      width: 216px;
      max-width: 216px;
      min-width: 114px;
      position: relative;
      padding: 0 16px;
      margin: 0;
      background-color: #EFEFEF; }
      .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item.is-closable.is-active, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item.is-closable:hover, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] .el-tabs__header .el-tabs__item.is-closable.is-active, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] .el-tabs__header .el-tabs__item.is-closable:hover {
        padding-right: 28px; }
      .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item:nth-child(n):before, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] .el-tabs__header .el-tabs__item:nth-child(n):before {
        content: '';
        width: 1px;
        height: 16px;
        background: #D9D9D9;
        position: absolute;
        left: 0px;
        top: 50%;
        margin-top: -8px; }
      .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item:nth-of-type(1):before, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] .el-tabs__header .el-tabs__item:nth-of-type(1):before {
        opacity: 0; }
      .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item:hover, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] .el-tabs__header .el-tabs__item:hover {
        font-size: 14px;
        font-weight: 400;
        color: #0854A1; }
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__nav-scroll, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] .el-tabs__nav-scroll {
      background: #EFEFEF;
      border-bottom: 1px solid rgba(24, 42, 78, 0.12);
      box-shadow: inset 0px -1px 0px 0px rgba(24, 42, 78, 0.12); }
      .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__nav-scroll .el-tabs__nav, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] .el-tabs__nav-scroll .el-tabs__nav {
        border: 0;
        transform: none !important;
        overflow: hidden;
        width: 100%;
        display: flex; }
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] > .el-tabs__header .el-tabs__item.is-active, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] > .el-tabs__header .el-tabs__item.is-active {
      background: #FFFFFF; }
      .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] > .el-tabs__header .el-tabs__item.is-active:before, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] > .el-tabs__header .el-tabs__item.is-active:before {
        opacity: 0; }
      .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] > .el-tabs__header .el-tabs__item.is-active + .el-tabs__item:before, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] > .el-tabs__header .el-tabs__item.is-active + .el-tabs__item:before {
        opacity: 0; }
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] > .el-tabs__header .el-tabs__nav, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] > .el-tabs__header .el-tabs__nav {
      background-color: #EFEFEF; }
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__nav-next,
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__nav-prev, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] .el-tabs__nav-next,
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"] .el-tabs__nav-prev {
      display: none; }
  .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"].el-tabs--top > .el-tabs__header .el-tabs__item {
    transition: padding .3s; }
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"].el-tabs--top > .el-tabs__header .el-tabs__item:nth-child(2), .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"].el-tabs--top > .el-tabs__header .el-tabs__item:last-child {
      padding-left: 16px;
      padding-right: 16px; }
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"].el-tabs--top > .el-tabs__header .el-tabs__item.is-active.is-closable, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs"].el-tabs--top > .el-tabs__header .el-tabs__item.is-closable:hover {
      padding-left: 16px;
      padding-right: 28px; }
  .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item {
    padding: 0;
    width: auto;
    min-width: auto;
    max-width: none;
    flex: 1; }
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item.is-closable.is-active, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item.is-closable:hover {
      padding-right: 0; }
      .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item.is-closable.is-active div, .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item.is-closable:hover div {
        padding-right: 28px; }
    .scenatorStyle .el-tabs--card[scenatorStyle="scenator_tabs_auto"] .el-tabs__header .el-tabs__item div {
      padding: 0 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      transition: padding .3s; }
  .scenatorStyle .scenator_tab {
    display: flex;
    justify-content: center;
    text-align: center;
    font-size: 14px;
    border-bottom: 1px solid rgba(24, 42, 78, 0.12); }
    .scenatorStyle .scenator_tab .scenator_tabContent {
      color: #182A4E;
      padding-bottom: 7px;
      margin: 0 12px;
      cursor: pointer;
      position: relative; }
      .scenatorStyle .scenator_tab .scenator_tabContent:hover {
        color: #0854A1; }
    .scenatorStyle .scenator_tab .scenator_tabContent.select {
      color: #0854A1; }
      .scenatorStyle .scenator_tab .scenator_tabContent.select::after {
        content: "";
        width: 100%;
        height: 3px;
        background: #0854A1;
        display: block;
        position: absolute;
        bottom: 0;
        left: 0;
        border-top-left-radius: 2px;
        border-top-right-radius: 2px; }
  .scenatorStyle .scenator-tabs {
    width: 100%;
    overflow-x: hidden;
    background: #EFEFEF;
    box-shadow: inset 0px -1px 0px 0px rgba(24, 42, 78, 0.12); }
    .scenatorStyle .scenator-tabs .container-item-tabs {
      width: 100%;
      background: #EFEFEF;
      display: flex;
      overflow: hidden;
      white-space: nowrap;
      border-bottom: 1px solid rgba(24, 42, 78, 0.12);
      box-shadow: inset 0px -1px 0px 0px rgba(24, 42, 78, 0.12); }
      .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab {
        color: #5D697A;
        height: 44px;
        line-height: 44px;
        text-align: center;
        position: relative;
        display: block;
        width: 216px;
        min-width: 114px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap; }
        .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab .container-item-title {
          color: #5D697A;
          font-weight: 400;
          font-size: 14px;
          display: inline-block;
          width: 100%;
          padding: 0 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          transition: padding .3s; }
        .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab .container-item-close-tab {
          width: 16px;
          height: 16px;
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          display: none; }
        .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab .container-item-tab-separator,
        .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab.container-item-active > .container-item-tab-separator,
        .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab.container-item-active + .container-item-tab > .container-item-tab-separator {
          width: 1px;
          height: 16px;
          background: #D9D9D9;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%); }
        .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab:first-child > .container-item-tab-separator {
          display: none; }
        .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab:hover .container-item-title, .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab.container-item-active .container-item-title {
          color: #0854A1; }
        .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab:hover.has-close .container-item-title, .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab.container-item-active.has-close .container-item-title {
          padding-right: 28px; }
        .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab:hover.has-close .container-item-close-tab, .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab.container-item-active.has-close .container-item-close-tab {
          display: block; }
        .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab.container-item-active {
          background: #FFFFFF; }
          .scenatorStyle .scenator-tabs .container-item-tabs .container-item-tab.container-item-active::after {
            content: "";
            width: 100%;
            height: 3px;
            background: #0854A1;
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            border-top-left-radius: 2px;
            border-top-right-radius: 2px; }
      .scenatorStyle .scenator-tabs .container-item-tabs:nth-child(n):before {
        content: '';
        width: 1px;
        height: 16px;
        position: absolute;
        left: 0px;
        top: 50%;
        margin-top: -8px; }
      .scenatorStyle .scenator-tabs .container-item-tabs:nth-of-type(1):before {
        opacity: 0; }
    .scenatorStyle .scenator-tabs.autoWidth .container-item-tabs {
      display: flex; }
      .scenatorStyle .scenator-tabs.autoWidth .container-item-tabs .container-item-tab {
        display: block;
        min-width: 80px;
        max-width: none;
        flex: 1; }
    .scenatorStyle .scenator-tabs .container-item-active {
      background: #FFFFFF;
      color: #0854A1; }
      .scenatorStyle .scenator-tabs .container-item-active:before {
        opacity: 0; }
      .scenatorStyle .scenator-tabs .container-item-active + .container-item-tab:before {
        opacity: 0; }
  .scenatorStyle .el-tag {
    width: auto;
    background: #EFEFEF;
    color: #182A4E;
    border-color: rgba(0, 0, 0, 0);
    height: 24px;
    padding: 0 8px;
    line-height: 24px;
    font-size: 14px;
    border-radius: 2px;
    box-sizing: border-box;
    white-space: nowrap; }
  .scenatorStyle .el-tag--tagsIcon {
    height: 32px;
    line-height: 32px;
    padding: 0;
    background: rgba(0, 0, 0, 0); }
    .scenatorStyle .el-tag--tagsIcon:before {
      content: url("../images/scenator_ggb.png");
      width: 32px;
      height: 32px;
      vertical-align: top; }
  .scenatorStyle .el-tag--tagsLine {
    padding: 0;
    background: #FFEBEB;
    position: relative;
    height: 24px;
    padding: 0 8px;
    line-height: 24px; }
    .scenatorStyle .el-tag--tagsLine:before {
      content: '';
      width: 4px;
      height: 24px;
      background: #BB0000;
      border-radius: 2px 0px 0px 2px;
      position: absolute;
      left: -4px;
      top: -1px; }
  .scenatorStyle .scenatorToolTip {
    position: relative; }
    .scenatorStyle .scenatorToolTip .scenatorTooltipContent.scenatorStyleMenu {
      background: #FFFFFF;
      border-radius: 4px;
      padding: 0px;
      width: auto;
      box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px #D9D9D9;
      position: absolute; }
      .scenatorStyle .scenatorToolTip .scenatorTooltipContent.scenatorStyleMenu li {
        display: block;
        line-height: 44px;
        padding: 0 16px;
        cursor: pointer; }
      .scenatorStyle .scenatorToolTip .scenatorTooltipContent.scenatorStyleMenu li:hover {
        background-color: #EFF2F4; }
      .scenatorStyle .scenatorToolTip .scenatorTooltipContent.scenatorStyleMenu li.select {
        background-color: rgba(0, 129, 255, 0.12);
        box-shadow: inset 0px -1px 0px 0px #0854A1; }
      .scenatorStyle .scenatorToolTip .scenatorTooltipContent.scenatorStyleMenu:before {
        content: "";
        position: absolute;
        top: calc(100% + 1px);
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: rgba(255, 255, 255, 0.3) transparent transparent transparent; }
      .scenatorStyle .scenatorToolTip .scenatorTooltipContent.scenatorStyleMenu:after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #FFFFFF transparent transparent transparent; }
  .scenatorStyle .scenatorMenu {
    height: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px #D9D9D9;
    display: inline-block;
    padding-left: 0; }
    .scenatorStyle .scenatorMenu li {
      color: #182A4E;
      line-height: 32px;
      padding: 0 16px;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      position: relative; }
    .scenatorStyle .scenatorMenu li:hover {
      background-color: #EFF2F4; }
    .scenatorStyle .scenatorMenu .witoutactive {
      color: #0854A1;
      background-color: #FFFFFF;
      box-shadow: inset 0px -1px 0px 0px #0854A1; }
  .scenatorStyle .menu {
    margin-top: 4px; }
  .scenatorStyle .easyui-menu.menu {
    margin-top: 0; }
  .scenatorStyle .easyui-menu.menu,
  .scenatorStyle .menu {
    background-color: #FFFFFF;
    border: none;
    color: #182A4E;
    padding: 0;
    width: auto !important;
    box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px #D9D9D9;
    border-radius: 4px; }
    .scenatorStyle .easyui-menu.menu .menu-item,
    .scenatorStyle .menu .menu-item {
      width: 100%;
      border: none;
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: 32px; }
      .scenatorStyle .easyui-menu.menu .menu-item .menu-text,
      .scenatorStyle .menu .menu-item .menu-text {
        height: 100% !important;
        padding: 0 16px;
        color: #182A4E;
        line-height: inherit !important; }
      .scenatorStyle .easyui-menu.menu .menu-item .menu-rightarrow,
      .scenatorStyle .menu .menu-item .menu-rightarrow {
        position: static;
        float: right;
        margin: 0 16px;
        background: url("../images/scenator_arrowUp.svg") !important;
        transform: rotate(90deg) !important; }
      .scenatorStyle .easyui-menu.menu .menu-item:hover,
      .scenatorStyle .menu .menu-item:hover {
        background-color: #EFF2F4; }
    .scenatorStyle .easyui-menu.menu .menu-active,
    .scenatorStyle .menu .menu-active {
      border: none;
      color: #FFFFFF;
      background-color: #EFF2F4;
      border-radius: 0; }
    .scenatorStyle .easyui-menu.menu .menu-line,
    .scenatorStyle .menu .menu-line {
      display: none; }
  .scenatorStyle .menu-shadow {
    display: none !important; }
  .scenatorStyle .ui-jqgrid-btable tr {
    border: 1px solid #D9D9D9; }
  .scenatorStyle .ui-jqgrid-btable tr.jqgroup::before {
    display: inline-block;
    content: '';
    width: 100%;
    height: 1px;
    background-color: #D9D9D9;
    position: absolute;
    left: 0;
    bottom: -1px; }
  .scenatorStyle .ui-jqgrid-btable tr.jqgrow td {
    background: transparent !important; }
  .scenatorStyle .ui-jqgrid-btable tr.jqgrow::before {
    display: inline-block;
    content: '';
    width: 1px;
    height: 100%;
    background-color: #D9D9D9;
    position: absolute;
    left: 128px;
    top: 0; }
  .scenatorStyle .ui-jqgrid-btable tr.jqgrow::after {
    display: inline-block;
    content: '';
    width: 100%;
    height: 1px;
    background-color: #D9D9D9;
    position: absolute;
    left: 0px;
    bottom: -1px; }
  .scenatorStyle .ui-jqgrid-btable tr.jqgroup.ui-state-hover,
  .scenatorStyle .ui-jqgrid-btable tr.jqgroup.ui-state-focus,
  .scenatorStyle .ui-jqgrid-btable tr.jqgroup.ui-state-highlight {
    background-color: rgba(24, 42, 78, 0.03); }
  .scenatorStyle .ui-jqgrid-btable tr.jqgrow.ui-state-hover,
  .scenatorStyle .ui-jqgrid-btable tr.jqgrow.ui-state-focus,
  .scenatorStyle .ui-jqgrid-btable tr.jqgrow.ui-state-highlight {
    background-color: rgba(255, 255, 255, 0.4); }
  .scenatorStyle .groupBackgroundColor {
    background-color: rgba(24, 42, 78, 0.06);
    border: 1px solid rgba(24, 42, 78, 0.12) !important; }
    .scenatorStyle .groupBackgroundColor:hover, .scenatorStyle .groupBackgroundColor:visited {
      border: 1px solid #0854A1;
      background: rgba(0, 129, 255, 0.12); }

.scene-content {
  background-color: #FFFFFF; }

@keyframes fadeIn {
  0% {
    background-color: rgba(0, 129, 255, 0.01); }
  10% {
    background-color: rgba(0, 129, 255, 0.02); }
  20% {
    background-color: rgba(0, 129, 255, 0.03); }
  30% {
    background-color: rgba(0, 129, 255, 0.04); }
  40% {
    background-color: rgba(0, 129, 255, 0.05); }
  50% {
    background-color: rgba(0, 129, 255, 0.06); }
  60% {
    background-color: rgba(0, 129, 255, 0.07); }
  70% {
    background-color: rgba(0, 129, 255, 0.08); }
  80% {
    background-color: rgba(0, 129, 255, 0.09); }
  90% {
    background-color: rgba(0, 129, 255, 0.1); }
  100% {
    background-color: rgba(0, 129, 255, 0.12); } }

@keyframes fadeOut {
  0% {
    background-color: rgba(0, 129, 255, 0.12); }
  10% {
    background-color: rgba(0, 129, 255, 0.1); }
  20% {
    background-color: rgba(0, 129, 255, 0.08); }
  30% {
    background-color: rgba(0, 129, 255, 0.07); }
  40% {
    background-color: rgba(0, 129, 255, 0.06); }
  50% {
    background-color: rgba(0, 129, 255, 0.05); }
  60% {
    background-color: rgba(0, 129, 255, 0.04); }
  70% {
    background-color: rgba(0, 129, 255, 0.03); }
  80% {
    background-color: rgba(0, 129, 255, 0.02); }
  90% {
    background-color: rgba(0, 129, 255, 0.01); }
  100% {
    background-color: rgba(0, 129, 255, 0); } }

/**
	图标按钮
	iconBtn_：图标前缀
	primary: 系统主题色
**/
.scenatorStyle .iconBtn:hover {
  background-color: #E5F0FA;
  border: 1px solid #0854A1; }

.scenatorStyle .iconBtn:active, .scenatorStyle .iconBtn.active {
  background-color: #0854A1;
  border: 1px solid #0854A1; }

.scenatorStyle .img_dot {
  background: url("../images/scenator_tree_file.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_dot {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_tree_file.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_dot:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_dot:active {
    background: url("../images/scenator_tree_file.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_remove {
  background: url("../images/scenator_removeIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_remove {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_removeIcon.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_remove:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_remove:active {
    background: url("../images/scenator_removeActiveIcon.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_arrowUp {
  background: url("../images/scenator_arrowUp.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_arrowUp {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_arrowUp.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  /*.scenatorStyle .iconBtn_arrowUp:hover {*/
  /*  background-color: #E5F0FA;*/
  /*  border: 1px solid #0854A1; }*/
  /*.scenatorStyle .iconBtn_arrowUp:active {*/
  /*  background: url("../images/scenator_arrowUpActive.svg") no-repeat center;*/
  /*  background-color: #0854A1;*/
  /*  border: 1px solid #0854A1; }*/

.scenatorStyle .img_edit {
  background: url("../images/scenator_edit.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_edit {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_edit.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_edit:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_edit:active {
    background: url("../images/scenator_editActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_delete {
  background: url("../images/scenator_delete.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_delete {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_delete.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_delete:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_delete:active {
    background: url("../images/scenator_deleteActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_seting {
  background: url("../images/scenator_seting.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_seting {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_seting.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_seting:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_seting:active {
    background: url("../images/scenator_setingActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_magnify {
  background: url("../images/scenator_magnify.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_magnify {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_magnify.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_magnify:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_magnify:active {
    background: url("../images/scenator_magnifyActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_reduce {
  background: url("../images/scenator_reduce.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_reduce {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_reduce.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_reduce:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_reduce:active {
    background: url("../images/scenator_reduceActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_more {
  background: url("../images/scenator_more.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_more {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_more.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_more:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_more:active {
    background: url("../images/scenator_moreActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_circulAdd {
  background: url("../images/scenator_circulAdd.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_circulAdd {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_circulAdd.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_circulAdd:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_circulAdd:active {
    background: url("../images/scenator_circulAdd_active.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_circulMinus {
  background: url("../images/scenator_circulMinus.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_circulMinus {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_circulMinus.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_circulMinus:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_circulMinus:active {
    background: url("../images/scenator_circulMinusActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_expend {
  background: url("../images/scenator_expend.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_expend {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_expend.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_expend:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_expend:active {
    background: url("../images/scenator_expendActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_shrink {
  background: url("../images/scenator_shrink.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_shrink {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_shrink.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_shrink:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_shrink:active {
    background: url("../images/scenator_shrinkActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_circulArarrowRight {
  background: url("../images/scenator_circulArarrowRight.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_circulArarrowRight {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_circulArarrowRight.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_circulArarrowRight:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_circulArarrowRight:active {
    background: url("../images/scenator_circulArarrowRight_active.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_circulArarrowLeft {
  background: url("../images/scenator_circulArarrowLeft.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_circulArarrowLeft {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_circulArarrowLeft.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_circulArarrowLeft:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_circulArarrowLeft:active {
    background: url("../images/scenator_circulArarrowLeft_active.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_save {
  background: url("../images/scenator_save.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_save {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_save.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_save:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_save:active {
    background: url("../images/scenator_saveActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_ask {
  background: url("../images/scenator_askIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_ask {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_askIcon.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_ask:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_ask:active {
    background: url("../images/scenator_askIcon.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_success {
  background: url("../images/scenator_successIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_success {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_successIcon.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_success:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_success:active {
    background: url("../images/scenator_successIcon.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_info {
  background: url("../images/scenator_infoIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_info {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_infoIcon.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_info:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_info:active {
    background: url("../images/scenator_infoIcon.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_warn {
  background: url("../images/scenator_warnIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_warn {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_warnIcon.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_warn:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_warn:active {
    background: url("../images/scenator_warnIcon.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_error {
  background: url("../images/scenator_errorIncon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_error {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_errorIncon.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_error:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_error:active {
    background: url("../images/scenator_errorIncon.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_repeat {
  background: url("../images/scenator_repeatIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_repeat {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_repeatIcon.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_repeat:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_repeat:active {
    background: url("../images/scenator_repeatIcon.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_position {
  background: url("../images/scenator_position.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_position {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_position.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_position:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_position:active {
    background: url("../images/scenator_positionActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_date {
  background: url("../images/scenator_dateIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_date {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_dateIcon.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_date:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_date:active {
    background: url("../images/scenator_dateIconActive.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_draw {
  background: url("../images/scenator_drawIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_draw {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_drawIcon.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_draw:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_draw:active {
    background: url("../images/scenator_drawIcon.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

.scenatorStyle .img_packUp {
  background: url("../images/scenator_packUpIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .iconBtn_packUp {
  width: 20px;
  height: 20px;
  background: url("../images/scenator_packUpIcon.svg") no-repeat center;
  background-size: 16px 16px;
  cursor: pointer;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border-radius: 2px; }
  .scenatorStyle .iconBtn_packUp:hover {
    background-color: #E5F0FA;
    border: 1px solid #0854A1; }
  .scenatorStyle .iconBtn_packUp:active {
    background: url("../images/scenator_packUpActiveIcon.svg") no-repeat center;
    background-color: #0854A1;
    border: 1px solid #0854A1; }

/**
	img_：图标
	dot：圆点
	remove: 删除图标（主题色）
**/
.scenatorStyle .img_dot {
  background: url("../images/scenator_tree_file.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_remove {
  background: url("../images/scenator_removeIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_arrowUp {
  background: url("../images/scenator_arrowUp.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_edit {
  background: url("../images/scenator_edit.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_delete {
  background: url("../images/scenator_delete.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_seting {
  background: url("../images/scenator_seting.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_magnify {
  background: url("../images/scenator_magnify.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_reduce {
  background: url("../images/scenator_reduce.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_more {
  background: url("../images/scenator_more.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_circulAdd {
  background: url("../images/scenator_circulAdd.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_circulMinus {
  background: url("../images/scenator_circulMinus.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_expend {
  background: url("../images/scenator_expend.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_shrink {
  background: url("../images/scenator_shrink.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_circulArarrowRight {
  background: url("../images/scenator_circulArarrowRight.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_circulArarrowLeft {
  background: url("../images/scenator_circulArarrowLeft.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_save {
  background: url("../images/scenator_save.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_ask {
  background: url("../images/scenator_askIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_success {
  background: url("../images/scenator_successIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_info {
  background: url("../images/scenator_infoIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_warn {
  background: url("../images/scenator_warnIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_error {
  background: url("../images/scenator_errorIncon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_repeat {
  background: url("../images/scenator_repeatIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_position {
  background: url("../images/scenator_position.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_date {
  background: url("../images/scenator_dateIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_draw {
  background: url("../images/scenator_drawIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.scenatorStyle .img_packUp {
  background: url("../images/scenator_packUpIcon.svg") no-repeat center;
  background-size: 100% 100%; }

.window-mask {
  background: #000;
  opacity: 0.5; }

.scenatorStyle .scenator_easyUI_window.window {
  background: #FFFFFF;
  border-radius: 2px;
  border: 0px;
  padding: 0px; }
  .scenatorStyle .scenator_easyUI_window.window.ask .panel-title {
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #0A6ED1; }
  .scenatorStyle .scenator_easyUI_window.window.warn .panel-title {
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #E9730C; }
  .scenatorStyle .scenator_easyUI_window.window.error .panel-title {
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #BB0000; }
  .scenatorStyle .scenator_easyUI_window.window.info .panel-title {
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #0A6ED1; }
  .scenatorStyle .scenator_easyUI_window.window.ask .panel-tool, .scenatorStyle .scenator_easyUI_window.window.warn .panel-tool, .scenatorStyle .scenator_easyUI_window.window.error .panel-tool, .scenatorStyle .scenator_easyUI_window.window.info .panel-tool {
    display: none; }
  .scenatorStyle .scenator_easyUI_window.window .panel-icon {
    width: 16px;
    height: 16px;
    left: 16px;
    margin-top: -8px; }
  .scenatorStyle .scenator_easyUI_window.window .panel-title.panel-with-icon {
    padding-left: 40px;
    text-indent: 0px; }
  .scenatorStyle .scenator_easyUI_window.window .window-body {
    background: #FFFFFF;
    border-radius: 2px;
    border: 0px;
    padding: 0px;
    height: auto !important; }
  .scenatorStyle .scenator_easyUI_window.window .panel-title {
    width: 100%;
    height: 44px;
    line-height: 44px;
    box-shadow: 0px 1px 0px 0px #D9D9D9, 0px 0px 4px 0px rgba(0, 0, 0, 0.15);
    font-size: 16px;
    text-align: left;
    text-indent: 16px;
    color: #182A4E;
    font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
    font-weight: 400; }
  .scenatorStyle .scenator_easyUI_window.window .window-header {
    padding: 0px;
    display: flex;
    align-items: center;
    border: 0px; }
    .scenatorStyle .scenator_easyUI_window.window .window-header .panel-tool {
      width: 20px;
      height: 20px;
      margin-right: 16px;
      right: 0px;
      margin-top: -10px; }
  .scenatorStyle .scenator_easyUI_window.window .panel-tool-close {
    background: none;
    width: 100%;
    height: 100%;
    margin: 0px; }
  .scenatorStyle .scenator_easyUI_window.window .scenator_easyUI_window_content {
    padding: 16px; }
  .scenatorStyle .scenator_easyUI_window.window.confirmPrompt .scenator_easyUI_window_content {
    padding: 24px 16px;
    color: #182A4E; }
  .scenatorStyle .scenator_easyUI_window.window .scenator_easyUI_window_footer {
    height: 56px;
    width: 100%;
    box-shadow: inset 0px 1px 0px 0px #D9D9D9;
    display: flex;
    align-items: center;
    padding: 0px 16px;
    justify-content: flex-end;
    background-color: #FFFFFF; }

.panel.messager-extend-success, .panel.messager-extend-info, .panel.messager-extend-error, .panel.messager-extend-warning {
  overflow: visible; }
  .panel.messager-extend-success .messager-body, .panel.messager-extend-info .messager-body, .panel.messager-extend-error .messager-body, .panel.messager-extend-warning .messager-body {
    min-width: 192px;
    max-width: 536px;
    margin: 0;
    padding: 8px 16px;
    line-height: 14px;
    font-size: 14px;
    border: none;
    background-color: #FFFFFF;
    -webkit-box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center; }
    .panel.messager-extend-success .messager-body .messager-icon, .panel.messager-extend-info .messager-body .messager-icon, .panel.messager-extend-error .messager-body .messager-icon, .panel.messager-extend-warning .messager-body .messager-icon {
      position: static;
      flex-shrink: 0;
      margin: 8px 8px 8px 0; }
    .panel.messager-extend-success .messager-body .messager-msg, .panel.messager-extend-info .messager-body .messager-msg, .panel.messager-extend-error .messager-body .messager-msg, .panel.messager-extend-warning .messager-body .messager-msg {
      max-width: 480px;
      display: inline-block;
      line-height: 20px;
      color: #182A4E;
      padding: 5px 0 7px 0; }
    .panel.messager-extend-success .messager-body .messager-success.messager-icon, .panel.messager-extend-info .messager-body .messager-success.messager-icon, .panel.messager-extend-error .messager-body .messager-success.messager-icon, .panel.messager-extend-warning .messager-body .messager-success.messager-icon {
      background: url("../images/scenator_successIcon.svg") no-repeat center; }
    .panel.messager-extend-success .messager-body .messager-info.messager-icon, .panel.messager-extend-info .messager-body .messager-info.messager-icon, .panel.messager-extend-error .messager-body .messager-info.messager-icon, .panel.messager-extend-warning .messager-body .messager-info.messager-icon {
      background: url("../images/scenator_infoIcon.svg") no-repeat center; }
    .panel.messager-extend-success .messager-body .messager-error.messager-icon, .panel.messager-extend-info .messager-body .messager-error.messager-icon, .panel.messager-extend-error .messager-body .messager-error.messager-icon, .panel.messager-extend-warning .messager-body .messager-error.messager-icon {
      background: url("../images/scenator_errorIncon.svg") no-repeat center; }
    .panel.messager-extend-success .messager-body .messager-warning.messager-icon, .panel.messager-extend-info .messager-body .messager-warning.messager-icon, .panel.messager-extend-error .messager-body .messager-warning.messager-icon, .panel.messager-extend-warning .messager-body .messager-warning.messager-icon {
      background: url("../images/scenator_warnIcon.svg") no-repeat center; }
    .panel.messager-extend-success .messager-body .icon-close, .panel.messager-extend-info .messager-body .icon-close, .panel.messager-extend-error .messager-body .icon-close, .panel.messager-extend-warning .messager-body .icon-close {
      position: static;
      background: none;
      width: 74px;
      height: 32px;
      font-size: 14px;
      line-height: 32px;
      margin-left: 5px; }
      .panel.messager-extend-success .messager-body .icon-close:hover, .panel.messager-extend-info .messager-body .icon-close:hover, .panel.messager-extend-error .messager-body .icon-close:hover, .panel.messager-extend-warning .messager-body .icon-close:hover {
        background: none; }
        .panel.messager-extend-success .messager-body .icon-close:hover::after, .panel.messager-extend-info .messager-body .icon-close:hover::after, .panel.messager-extend-error .messager-body .icon-close:hover::after, .panel.messager-extend-warning .messager-body .icon-close:hover::after {
          background: #EFEFEF;
          border-radius: 2px;
          border: 1px solid #0854A1; }
      .panel.messager-extend-success .messager-body .icon-close::after, .panel.messager-extend-info .messager-body .icon-close::after, .panel.messager-extend-error .messager-body .icon-close::after, .panel.messager-extend-warning .messager-body .icon-close::after {
        display: inline-block;
        content: "知道了";
        width: 74px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
        font-weight: 400;
        color: #0854A1; }
      .panel.messager-extend-success .messager-body.messager-body-us .icon-close::after, .panel.messager-extend-info .messager-body.messager-body-us .icon-close::after, .panel.messager-extend-error .messager-body.messager-body-us .icon-close::after, .panel.messager-extend-warning .messager-body.messager-body-us .icon-close::after {		
        display: inline-block;
        content: "I See";
        width: 74px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
        font-weight: 400;
        color: #0854A1; }
      .panel.messager-extend-success .messager-body.messager-body-ru .icon-close::after, .panel.messager-extend-info .messager-body.messager-body-ru .icon-close::after, .panel.messager-extend-error .messager-body.messager-body-ru .icon-close::after, .panel.messager-extend-warning .messager-body.messager-body-ru .icon-close::after {		
        display: inline-block;
        content: "Я понял.";
        width: 74px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
        font-weight: 400;
        color: #0854A1; }
      .panel.messager-extend-success .messager-body .icon-close:active::after, .panel.messager-extend-info .messager-body .icon-close:active::after, .panel.messager-extend-error .messager-body .icon-close:active::after, .panel.messager-extend-warning .messager-body .icon-close:active::after {
        color: #FFFFFF;
        background: #114171;
        border-radius: 2px; }

.scenatorStyle .textbox.combo {
  border-radius: 2px;
  border: 1px solid #89919A; }

.scenatorStyle .textbox:hover,
.scenatorStyle .textbox-focused.textbox {
  border-color: #0854A1; }

.scenatorStyle .combo-arrow,
.scenatorStyle .textbox:hover .combo-arrow {
  width: 32px !important;
  height: 32px;
  background: url("../images/scenator_treeOpen.svg") no-repeat center;
  opacity: 1; }

.scenatorStyle .textbox-focused .combo-arrow,
.scenatorStyle .textbox-focused.textbox:hover .combo-arrow {
  background: url("../images/scenator_arrowUpActive.svg") no-repeat center;
  transform: rotateZ(180deg);
  background-color: #0854A1; }

.scenatorStyle .textbox:hover .combo-arrow {
  background-color: rgba(0, 129, 255, 0.12); }

.scenatorStyle .textbox-text {
  height: 32px;
  font-size: 14px; }

.scenatorStyle .combo-panel {
  max-height: 192px;
  background: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px #D9D9D9;
  border-color: #D9D9D9; }
  .scenatorStyle .combo-panel .combobox-item {
    color: #182A4E;
    font-size: 14px;
    padding: 6px 16px; }
  .scenatorStyle .combo-panel .combobox-item.combobox-item-selected {
    background: #E5F0FA;
    box-shadow: inset 0px -1px 0px 0px #0854A1;
    color: #182A4E; }
  .scenatorStyle .combo-panel .combobox-item.combobox-item-hover {
    background: #EFF2F4; }

/*#region easyui滑动条文字样式*/
.slider-rulelabel span {
  color: #5D697A; }

/*#endregion*/
/* -----------------------------场景导航样式 start------------------------------*/
#system_body {
  display: flex; }
  #system_body #nav_menu_container {
    width: 216px;
    position: relative;
    overflow: hidden;
    background-color: #FFFFFF; }
    #system_body #nav_menu_container .top_area {
      height: calc(100% - 44px); }
      #system_body #nav_menu_container .top_area .nav_bottom {
        background-color: #FFFFFF; }
    #system_body #nav_menu_container .bottom_area {
      height: 44px;
      box-shadow: inset 1px 1px 0px 0px #D9D9D9; }
  #system_body #scene_container {
    width: calc(100% - 216px);
    border-left: 1px solid #D9D9D9; }
  #system_body.scene_shrink #nav_menu_container {
    width: 48px;
    overflow: hidden; }
  #system_body.scene_shrink #scene_container {
    width: calc(100% - 48px); }
  #system_body.scene_shrink .scene-nav-workspace-selected[level="1"] > .workspace_expend {
    background: #E5F0FA;
    box-shadow: inset 0px -1px 0px 0px #0854A1; }
    #system_body.scene_shrink .scene-nav-workspace-selected[level="1"] > .workspace_expend:hover {
      background: rgba(0, 129, 255, 0.12); }
  #system_body.scene_shrink .scene-nav-list {
    width: 216px; }

.scenatorStyle .sceneNavigator-structure {
  height: 44px;
  box-shadow: inset 0px -1px 0px 0px #D9D9D9;
  display: flex;
  position: relative;
  cursor: pointer;}
.scenatorStyle .sceneNavigator-structure:hover {
  background-color: #E5F0FA;}
  .scenatorStyle .sceneNavigator-structure span,
  .scenatorStyle .sceneNavigator-structure div,
  .scenatorStyle .sceneNavigator-structure p {
    color: #182A4E; }
  .scenatorStyle .sceneNavigator-structure .workspace-workspace {
    background: url(../images/scenator_structureIcon.svg) no-repeat center center; }
  .scenatorStyle .sceneNavigator-structure .workspace-recover {
    position: absolute;
    right: 0; }
  .scenatorStyle .sceneNavigator-structure.nav-normal .workspace-title {
    width: calc(100% - 96px); }
  .scenatorStyle .sceneNavigator-structure.nav-seting .workspace-title {
    width: calc(100% - 136px); }
  .scenatorStyle .sceneNavigator-structure .workspace-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 44px;
    cursor: inherit; }

.scenatorStyle .sceneNavigator-scene {
  height: 44px;
  position: relative; }
  .scenatorStyle .sceneNavigator-scene span,
  .scenatorStyle .sceneNavigator-scene div,
  .scenatorStyle .sceneNavigator-scene p {
    color: #182A4E; }
  .scenatorStyle .sceneNavigator-scene .menu-item-content {
    height: 44px;
    line-height: 44px;
    position: relative; }
  .scenatorStyle .sceneNavigator-scene .nav-menu-img {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%); }
  .scenatorStyle .sceneNavigator-scene .nav-menu-text {
    margin-left: 48px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 48px - 36px); }
  .scenatorStyle .sceneNavigator-scene.scene-edit .nav-menu-text {
    max-width: calc(100% - 48px - 36px); }
  .scenatorStyle .sceneNavigator-scene:hover {
    background-color: #EFF2F4; }
  .scenatorStyle .sceneNavigator-scene.selected {
    background-color: rgba(0, 129, 255, 0.12);
    box-shadow: inset 0px -1px 0px 0px #0854A1; }
    .scenatorStyle .sceneNavigator-scene.selected .nav-menu-text {
      color: #182A4E; }
  .scenatorStyle .sceneNavigator-scene .editDownMenu {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%); }

.scenatorStyle .stretchContainer:hover, .scenatorStyle .stretchContainer:active {
  border-right: 2px solid #0854A1; }

.shrinkMenuRootTitle {
  width: 100%; }

.workspace-shrink-img {
  width: 48px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: none; }

.menu.shrinkMenu {
  min-width: 88px;
  max-width: 172px; }
  .menu.shrinkMenu.scenatorScene-shrinkmenu {
    margin-top: 0; }

.menu.shrinkMenu .shrinkMenuRootTitle {
  height: 44px;
  padding: 0 16px; }
  .menu.shrinkMenu .shrinkMenuRootTitle div {
    display: inline-block; }

.menu.shrinkMenu .shrinkMenuRootTitle div:first-child {
  max-width: 100%;
  text-indent: 0px;
  height: 44px;
  line-height: 44px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: default; }

.menu.shrinkMenu.with-seting .shrinkMenuRootTitle div:first-child {
  max-width: calc(100% - 36px); }

.menu.shrinkMenu.with-seting .shrinkMenuRootTitle .iconBtn_seting {
  float: right;
  margin: 14px 0 0 16px; }

.menu.shrinkMenu .menu-item {
  padding: 0 16px;
  height: 44px !important; }
  .menu.shrinkMenu .menu-item .menu-text {
    padding: 0;
    height: 44px !important;
    line-height: 44px !important;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 100%; }

.menu-item.shrinkMenuItem.with-rightarrow div:first-child {
  max-width: calc(100% - 29px); }

.menu-item.shrinkMenuItem.with-rightarrow:hover {
  background-color: #EFF2F4; }

.menu.shrinkMenu .menu-item .menu-rightarrow {
  margin-right: 0 !important; }

#nav_menu_container .structure-property.child-selected > .workspace-title,
.menu.shrinkMenu .menu-item.shrinkMenuItem.child-selected > .menu-text,
.scenatorScene-shrinkmenu.child-selected > .shrinkMenuRootTitle > div:first-child {
  color: #0854A1; }

/* -----------------------------场景导航样式 end------------------------------*/
.scenatorToolTip {
  position: relative;
  display: inline-block;
  font-size: 14px;
  overflow: visible; }

.scenatorToolTip .scenatorTooltipContent {
  width: auto;
  padding: 8px 0px;
  display: none;
  background-color: #FFFFFF;
  font-size: 14px;
  color: #182A4E;
  text-align: left;
  border-radius: 2px;
  position: absolute;
  z-index: 1;
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px #D9D9D9; }

.scenatorToolTip .scenatorTooltipContent::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #FFFFFF transparent transparent transparent; }

.scenatorToolTip .scenatorTooltipContent::before {
  content: "";
  position: absolute;
  top: calc(100% + 1px);
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.3) transparent transparent transparent; }

.scenatorToolTip.focus .scenatorTooltipContent {
  display: block; }

.scenatorTooltipContent li {
  height: 44px;
  line-height: 44px;
  padding: 0 18px;
  white-space: nowrap; }
  .scenatorTooltipContent li:hover {
    background-color: rgba(0, 129, 255, 0.12); }
  .scenatorTooltipContent li.select {
    box-shadow: inset 0px -1px 0px 0px #EFF2F4;
    background-color: rgba(0, 129, 255, 0.12); }

.scene-nav-workspace[level="1"] > .workspace_expend {
  box-shadow: inset 0px -1px 0px 0px #D9D9D9; }

/*------------------------scenator 内部样式 start------------------------*/
#nav_menu_container span,
#nav_menu_container div,
#nav_menu_container p {
  color: #182A4E; }

#nav_menu_container div.menu-item.selected > .menu-item-content > .nav-menu-text {
  color: #0854A1; }

.menu.shrinkMenu {
  background-color: #FFFFFF; }
  .menu.shrinkMenu .menu-item.shrinkMenuItem {
    color: #182A4E; }
    .menu.shrinkMenu .menu-item.shrinkMenuItem:hover {
      background-color: #EFF2F4; }
    .menu.shrinkMenu .menu-item.shrinkMenuItem.selected {
      background-color: #E5F0FA;
      border-bottom: 1px solid #0854A1; }
      .menu.shrinkMenu .menu-item.shrinkMenuItem.selected .menu-text {
        color: #0854A1; }
    .menu.shrinkMenu .menu-item.shrinkMenuItem.menu-active {
      border-radius: 0; }
    .menu.shrinkMenu .menu-item.shrinkMenuItem .menu-text {
      color: #182A4E; }

.workspace-shrink-img:after {
  content: " ";
  width: 0px;
  height: 0px;
  position: absolute;
  bottom: -5px;
  right: -5px;
  border: 5px solid transparent;
  border-top: 5px solid #0854A1;
  transform: rotate(-45deg); }

.scene-nav-list > .menu-item .nav-menu-img {
  background-repeat: no-repeat;
  background-position: center center; }

.scene-nav-workspace .workspace-workspace {
  background: url("../images/scenator_structureIcon.svg") no-repeat center center; }

.slide-line {
  background: url("../images/scenator_line.svg") no-repeat center; }

.workspace-edit-tip {
  height: 176px;
  background: #FFFFFF;
  border-radius: 4px;
  overflow: hidden; }
  .workspace-edit-tip li {
    color: #182A4E;
    cursor: pointer;
    padding: 0 16px; }
    .workspace-edit-tip li:hover {
      background-color: #EFF2F4; }
    .workspace-edit-tip li.is-disabled {
      color: rgba(24, 42, 78, 0.4); }

/* 收缩后的工作空间列表 */
.shrink-workspace-list {
  background: #FFFFFF;
  box-shadow: 0px 10px 30px 2px rgba(0, 0, 0, 0.12), 0px 0px 0px 1px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 4px 4px; }

.scene-nav-workspace .scene_shrink {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); }
  .scene-nav-workspace .scene_shrink .structure-property.active::after {
    border-color: transparent transparent #0854A1 transparent; }

.scene-nav-workspace[level="1"] > .workspace_expend {
  box-shadow: inset 0px -1px 0px 0px #D9D9D9; }

.scene-nav-workspace[level="1"][shrink="open"] > .workspace_list {
  box-shadow: inset 0px -1px 0px 0px #D9D9D9; }

.scene-nav-workspace[level="1"].scene_shrink > .structure-property {
  cursor: pointer; }
  .scene-nav-workspace[level="1"].scene_shrink > .structure-property:hover {
    background-color: #EFF2F4; }

/* 场景菜单宽度变化后，弹出层宽度变化 */
.panel.window.float-window.open,
.panel.window.float-window.open .panel-body {
  left: 216px !important;
  width: calc(100vw - 216px) !important; }

.edit-workspace-page table tr.drag-highlight {
  background-color: #0854A1; }

.edit-workspace-btn {
  color: #0854A1; }
  .edit-workspace-btn:hover, .edit-workspace-btn:active {
    color: #0854A1; }
  .edit-workspace-btn.disabled {
    color: rgba(8, 84, 161, 0.4); }

.edit-workspace-button {
  height: 56px;
  box-shadow: inset 0px 1px 0px 0px #D9D9D9; }

.scene-nav-list > .menu-item {
  border-bottom: 1px solid #D9D9D9; }

.nav_bottom .menu-item, .top_area .menu-item {
  border-bottom: 1px solid #D9D9D9; }

.container-item > .container-header {
  background: #EFEFEF;
  box-shadow: inset 0px -1px 0px 0px rgba(24, 42, 78, 0.12); }
  .container-item > .container-header .container-item-tab.container-item-active {
    background-color: #FFFFFF;
    border-radius: 2px 2px 0px 0px;
    color: #0854A1; }

.tabLayout-stack-dropmenu.menu {
  width: 256px !important;
  max-height: 264px;
  height: auto !important;
  overflow-y: auto !important;
  padding: 0px; }
  .tabLayout-stack-dropmenu.menu .tabLayout-invisible-tab {
    overflow: hidden;
    padding: 11px 0;
    color: #182A4E;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    -webkit-transition: all .3s;
    transition: all .3s;
    display: flex;
    align-items: center; }
    .tabLayout-stack-dropmenu.menu .tabLayout-invisible-tab .tab-item-title {
      flex: 1;
      padding: 0 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap; }
    .tabLayout-stack-dropmenu.menu .tabLayout-invisible-tab .tab-item-close-tab {
      width: 16px;
      height: 16px;
      margin-left: 8px;
      margin-right: 8px; }
    .tabLayout-stack-dropmenu.menu .tabLayout-invisible-tab:hover {
      background: #EFF2F4; }
    .tabLayout-stack-dropmenu.menu .tabLayout-invisible-tab:active {
      background: #E5F0FA;
      box-shadow: inset 0px -1px 0px 0px #0854A1; }

.suspend {
  background-color: rgba(255, 255, 255, 0.75);
  border-radius: 2px;
  box-shadow: -1px 0px 0px 0px rgba(24, 42, 78, 0.12), 0px -1px 0px 0px rgba(24, 42, 78, 0.12), 0px 1px 0px 0px rgba(24, 42, 78, 0.12), 1px 0px 0px 0px rgba(24, 42, 78, 0.12); }

.suspend::before {
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  border-radius: 2px; }

.suspend > .container-item.container-stack > .container-header {
  background: rgba(255, 255, 255, 0.75);
  box-shadow: -1px -1px 0px 0px rgba(24, 42, 78, 0.12), 1px 1px 0px 0px rgba(24, 42, 78, 0.12); }
  .suspend > .container-item.container-stack > .container-header .container-item-tabs {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(4px); }
    .suspend > .container-item.container-stack > .container-header .container-item-tabs .container-item-tab {
      border-bottom: 1px solid rgba(24, 42, 78, 0.12); }
      .suspend > .container-item.container-stack > .container-header .container-item-tabs .container-item-tab.container-item-active {
        border-color: #0854A1;
        background: rgba(0, 129, 255, 0.12); }
        .suspend > .container-item.container-stack > .container-header .container-item-tabs .container-item-tab.container-item-active::after {
          display: none; }

.nav-menu-container .stretchContainer:hover,
.nav-menu-container .stretchContainer:active {
  border-right: 2px solid #0854A1; }

/* 分割线样式 */
.container-item > .container-header .container-item-tab > .container-item-tab-separator {
  background: #D9D9D9; }

.container-row > .container-items > .container-item {
  /* 只控制下面一级的样式 */
  border-top: 1px solid #D9D9D9; }

.rightclick-stack-menu {
  box-shadow: 0px 6px 20px 2px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px #D9D9D9;
  border-radius: 4px;
  overflow: hidden; }
  .rightclick-stack-menu .rightClick-item {
    height: 32px;
    font-size: 14px;
    padding: 0 16px;
    line-height: 32px; }
    .rightclick-stack-menu .rightClick-item:hover {
      background: #EFF2F4;
      cursor: pointer; }

.area-title {
  border-bottom: 1px solid rgba(24, 42, 78, 0.12); }

.area-slide-container {
  box-shadow: inset -1px 0px 0px 0px #D9D9D9, inset 1px 0px 0px 0px #D9D9D9; }

.workspace-edit-tip {
  box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08), 0px 0px 0px 1px #D9D9D9; }

.side-panel {
  background-color: #F7F7F7; }

.shrinkMenuRootTitle {
  box-shadow: inset 0px -1px 0px 0px #D9D9D9; }

.switch-system-tab {
  padding: 0;
  color: #182A4E;
  background: #FFFFFF;
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px #D9D9D9; }
  .switch-system-tab .perSystem:hover {
    background-color: #EFF2F4; }

.markdown > table th {
  background: #EFF2F4; }

.switch-system-tab .perSystem:hover {
  background: #EFF2F4; }

.userInformation-tab span:hover {
  background: #EFF2F4; }

#system_head_button .menu-item-content > .nav-menu-icon:hover {
  background-color: #244F7D; }

#system_head_button .menu-item-content > .nav-menu-icon:active {
  background-color: #143F6C; }

/*------------------------scenator 内部样式 end------------------------*/
.scenatorStyle .triangle:before {
  border-color: #FFFFFF; }

.scenatorStyle .triangle:after {
  border-color: #FFFFFF; }

body .render3DColorPicker table.jPicker {
  background-color: rgba(255, 255, 255, 0.75); }

body .render3DColorPicker table.jPicker::before {
  color: #182A4E;
  background-color: rgba(255, 255, 255, 0.4);
  box-shadow: inset 0px -1px 0px 0px rgba(24, 42, 78, 0.12); }

body .render3DColorPicker .colorSetterPickerFooter {
  border-top: 1px solid rgba(24, 42, 78, 0.12); }

body .render3DColorPicker table.jPicker tbody tr {
  height: auto;
  background-color: transparent; }
  body .render3DColorPicker table.jPicker tbody tr td {
    height: auto;
    background-color: transparent; }

.switch-system-tab {
  position: absolute;
  z-index: 100;
  width: auto;
  height: auto;
  padding: 0;
  display: block;
  background: #FFFFFF;
  font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
  color: #182A4E;
  font-size: 14px;
  cursor: default;
  border-radius: 4px;
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px #D9D9D9; }
  .switch-system-tab .perSystem {
    position: relative; }
  .switch-system-tab .perSystem:hover {
    background-color: #EFF2F4;
    z-index: 102; }
  .switch-system-tab .perSystem:nth-child(1):hover {
    background-color: #EFF2F4;
    border-radius: 2px;
    z-index: 102; }

#switchSystemPanel::before {
  background-color: #FFFFFF;
  border-color: #D9D9D9 transparent transparent #D9D9D9; }

.popoverTriangle::before {
  width: 10px;
  height: 10px;
  transform: rotate(45deg);
  z-index: 101;
  border: 1px solid;
  background-color: #FFFFFF;
  border-color: #D9D9D9 transparent transparent #D9D9D9; }

.userInformation-tab {
  position: absolute;
  background: #FFFFFF;
  color: #182A4E;
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px #D9D9D9; }

.userInformation-tab span {
  display: block;
  height: 44px;
  padding: 0 16px;
  text-align: center;
  line-height: 44px;
  font-size: 14px;
  color: #182A4E;
  font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
  font-weight: 400;
  cursor: pointer; }

.userInformation-tab span {
  z-index: 103;
  position: relative; }
  .userInformation-tab span:hover {
    background-color: #EFF2F4;
    border-radius: 2px; }

.scenatorStyle_empty_container {
  position: absolute;
  top: calc(100% / 4);
  left: 50%;
  transform: translateX(-50%); }
  .scenatorStyle_empty_container .empty_text {
    margin-top: 16px;
    color: #5D697A;
    font-size: 14px;
    text-align: center; }
  .scenatorStyle_empty_container.big_container > div {
    background: url("../images/scenatorStyle_empty_container_big.svg") no-repeat center center;
    width: 313px;
    height: 198px;
    margin: auto; }
  .scenatorStyle_empty_container.medium_container > div {
    background: url("../images/scenatorStyle_empty_container_medium.svg") no-repeat center center;
    width: 120px;
    height: 90px;
    margin: auto; }
  .scenatorStyle_empty_container.small_container > div {
    background: url("../images/scenatorStyle_empty_container_small.svg") no-repeat center center;
    width: 120px;
    height: 90px;
    margin: auto; }

/* 浮动窗口样式 */
.scenatorStyle .float-window.panel {
  background-color: #FFFFFF;
  padding: 0;
  border-radius: 2px; }
  .scenatorStyle .float-window.panel .panel-header {
    padding: 0px;
    display: flex;
    align-items: center;
    border: 0px;
    width: 100% !important;
    box-shadow: 0px 1px 0px 0px rgba(24, 42, 78, 0.12), 0px 0px 4px 0px rgba(0, 0, 0, 0.15); }
  .scenatorStyle .float-window.panel.no-header .panel-header {
    display: none; }
  .scenatorStyle .float-window.panel.no-header .panel-body {
    padding: 0; }
  .scenatorStyle .float-window.panel .panel-title {
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    text-align: left;
    text-indent: 16px;
    color: #182A4E;
    font-family: PingFangSC-Regular, PingFang SC, "Microsoft YaHei", "微软雅黑";
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: calc(100% - 52px); }
  .scenatorStyle .float-window.panel .panel-tool {
    width: 20px;
    height: 20px;
    margin-right: 16px;
    right: 0px;
    margin-top: -10px; }
    .scenatorStyle .float-window.panel .panel-tool .panel-tool-close {
      background: none; }
  .scenatorStyle .float-window.panel .panel-body {
    width: 100% !important;
    padding: 16px; }
  .scenatorStyle .float-window.panel.transparent-window {
    background-color: rgba(255, 255, 255, 0.75);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(24, 42, 78, 0.12);
    box-shadow: 1px 0px 0px 0px rgba(24, 42, 78, 0.12), 0px 1px 0px 0px rgba(24, 42, 78, 0.12), 0px 0px 1px 0px rgba(24, 42, 78, 0.12), 0px 0px 0px 1px rgba(24, 42, 78, 0.12); }
    .scenatorStyle .float-window.panel.transparent-window .panel-header {
      background-color: rgba(255, 255, 255, 0.4); }
    .scenatorStyle .float-window.panel.transparent-window .panel-body {
      background-color: transparent; }
