<?xml version="1.0" encoding="UTF-8"?>
<svg width="320px" height="288px" viewBox="0 0 320 288" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>插件配图</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F8F9FD" offset="0%"></stop>
            <stop stop-color="#EEF1F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#F7F6FF" offset="0%"></stop>
            <stop stop-color="#E7F4FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#DCD6F2" offset="0%"></stop>
            <stop stop-color="#B7ADE0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#E8ECFB" offset="0%"></stop>
            <stop stop-color="#CCD4F5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#D8D8F3" offset="0%"></stop>
            <stop stop-color="#B0B0E4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="62.3016508%" y1="8.14785793%" x2="47.9647464%" y2="60.4967318%" id="linearGradient-6">
            <stop stop-color="#AFB5E4" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#AFB5E4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#C2C4EB" offset="0%"></stop>
            <stop stop-color="#AEB4E4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-8">
            <stop stop-color="#DFE6FF" offset="0%"></stop>
            <stop stop-color="#BBCBF0" offset="100%"></stop>
        </linearGradient>
        <path d="M181.17792,146.967869 L181.17792,139.554268 C181.17792,127.652941 171.039945,118 158.528533,118 C146.017358,118 135.879382,127.652862 135.879382,139.554268 L135.879382,146.84448 C130.356016,147.22425 126,151.609296 126,156.963365 L126,184.857627 C126,190.457523 130.771063,195 136.658247,195 L179.341753,195 C185.228937,195 190,190.457523 190,184.857627 L190,156.963365 C190,151.955897 186.18979,147.796205 181.17792,146.967869 Z M163.254217,172.961141 L163.254217,182.354766 L155.260018,182.354766 L155.260018,173.112065 C152.583005,171.78668 150.755613,169.123056 150.755613,166.052841 C150.755613,161.660495 154.494435,158.100135 159.111575,158.100135 C163.728794,158.100135 167.467774,161.660416 167.467774,166.052841 C167.467774,169.010458 165.771932,171.591082 163.254217,172.961141 Z M173.99579,146.817342 L142.982298,146.817342 L142.982298,140.87505 C142.982298,132.726032 149.92291,126.114819 158.491139,126.114819 C167.050909,126.114819 173.99579,132.726032 173.99579,140.87505 L173.99579,146.817342 L173.99579,146.817342 Z" id="path-9"></path>
        <filter x="-3.1%" y="-3.9%" width="112.5%" height="106.5%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="4" dy="-1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.68627451   0 0 0 0 0.709803922   0 0 0 0 0.894117647  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#E6EDFB" offset="0%"></stop>
            <stop stop-color="#C8D6F5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-12" cx="214.493811" cy="209.454545" rx="35.3846154" ry="35.4224599"></ellipse>
        <filter x="-2.8%" y="-8.5%" width="111.3%" height="111.3%" filterUnits="objectBoundingBox" id="filter-13">
            <feOffset dx="4" dy="-4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.68627451   0 0 0 0 0.709803922   0 0 0 0 0.894117647  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <path d="M223.178583,194.327709 C224.981003,192.527215 227.901742,192.528776 229.702236,194.331196 C231.50756,196.138451 231.50756,199.058061 229.707466,200.860081 L220.998951,209.577359 L229.707466,218.294873 C231.50756,220.096892 231.50756,223.016503 229.707466,224.818523 C227.901742,226.626178 224.981003,226.627739 223.178583,224.827244 L214.471951,216.111359 L205.769391,224.823758 C203.963485,226.627739 201.042746,226.626178 199.242252,224.823758 C197.436927,223.016503 197.436927,220.096892 199.237022,218.294873 L207.944951,209.577359 L199.237022,200.860081 C197.436927,199.058061 197.436927,196.138451 199.237022,194.336431 C201.042746,192.528776 203.963485,192.527215 205.765904,194.327709 L214.471951,203.042359 L223.175097,194.331196 Z" id="path-14"></path>
        <filter x="-6.0%" y="-9.0%" width="124.1%" height="115.1%" filterUnits="objectBoundingBox" id="filter-15">
            <feOffset dx="4" dy="-1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.68627451   0 0 0 0 0.709803922   0 0 0 0 0.894117647  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-36.1%" y="-25.8%" width="172.1%" height="151.6%" filterUnits="objectBoundingBox" id="filter-16">
            <feOffset dx="1" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.597640355   0 0 0 0 0.614549228   0 0 0 0 0.840000871  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="6.16297582e-31%" id="linearGradient-17">
            <stop stop-color="#51C6FF" offset="0%"></stop>
            <stop stop-color="#2894FD" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="新工作台合并第一版" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="暂无访问权限" transform="translate(-560.000000, -288.000000)">
            <g id="编组-6" transform="translate(560.000000, 288.000000)">
                <g id="编组-5" transform="translate(140.647657, 0.000000)">
                    <ellipse id="椭圆形" fill="#FFCD5A" cx="40" cy="15.4010695" rx="15.3846154" ry="15.4010695"></ellipse>
                    <ellipse id="椭圆形" fill="#F6F8FC" cx="22.3076923" cy="25.4117647" rx="10" ry="10.0106952"></ellipse>
                    <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="24.6417112" width="56.9230769" height="16.9411765" rx="8.47058824"></rect>
                </g>
                <path d="M32.6219631,142.459893 C27.9144593,157.499776 -4.07587621,192.666901 0.43440371,217.812372 C4.94468363,242.957843 19.9038857,260.330685 49.124626,262.75289 C78.3453663,265.175094 82.7157379,258.366288 103.471868,262.75289 C124.227999,267.139491 167.530554,290.539732 188.301079,286.022962 C209.071604,281.506192 215.109673,269.610521 235.667932,262.75289 C256.226191,255.895259 287.532112,270.15941 301.998498,257.723481 C316.464884,245.287552 325.930845,213.86212 311.280741,188.568769 C296.630637,163.275418 288.433061,160.135497 296.955522,133.521126 C305.477982,106.906755 308.749328,64.1699931 289.633393,47.1329244 C270.517459,30.0958556 227.955324,33.1189431 208.128201,47.1329244 C188.301079,61.1469056 177.301942,68.7934151 147.110415,64.9701603 C116.918889,61.1469056 95.2176968,36.6759375 61.5046173,43.2584894 C27.7915379,49.8410413 16.1531072,78.5306397 22.8124956,98.9735531 C29.4718839,119.416467 37.3294669,127.42001 32.6219631,142.459893 Z" id="路径-12" fill="url(#linearGradient-2)"></path>
                <g id="编组-2" transform="translate(18.005857, 144.708552)">
                    <line x1="12.7509122" y1="75.2152213" x2="12.7509122" y2="8.10262085" id="路径-14" stroke="#B9BCE0" stroke-width="1.53846154"></line>
                    <path d="M13.1933407,0 C2.99463158,29.054212 -1.29122708,46.6930546 0.33576476,52.916528 C2.77625252,62.251738 13.6383677,63.3866827 19.5314025,60.8861712 C25.4244373,58.3856597 26.7019152,54.9125688 26.310674,47.3684781 C26.0498466,42.3390844 21.6774021,26.5495917 13.1933407,0 Z" id="路径-15" fill="url(#linearGradient-3)"></path>
                </g>
                <g id="编组-2备份" transform="translate(249.878426, 129.368984)">
                    <line x1="7.43754969" y1="43.1229947" x2="7.43754969" y2="4.64545965" id="路径-14" stroke="#B0B3CC" stroke-width="0.897377692"></line>
                    <path d="M7.69561629,0 C1.74675513,16.6575941 -0.753166944,26.770437 0.195850074,30.3385287 C1.6193756,35.6906663 7.95519851,36.3413619 11.3925792,34.9077486 C14.8299599,33.4741353 15.575107,31.4829149 15.3468978,27.1576762 C15.1947583,24.2741838 12.6443311,15.2216251 7.69561629,0 Z" id="路径-15" fill="url(#linearGradient-4)"></path>
                </g>
                <ellipse id="椭圆形" fill="url(#linearGradient-5)" cx="289.878426" cy="200.213904" rx="6.15384615" ry="6.16042781"></ellipse>
                <line x1="289.878426" y1="202.404389" x2="289.878426" y2="214.629187" id="路径-16" stroke="#BFC6D5" stroke-width="1.53846154"></line>
                <path d="M249.878426,221.331018 C249.878426,221.331018 255.788573,201.599763 254.666563,192.166072 C253.544552,182.732381 243.503069,178.695325 246.142285,174.525773 C247.901762,171.746071 250.964591,170.368734 255.330772,170.393761 L246.812422,161.71123 C234.361623,159.088344 225.324615,160.778106 219.7014,166.780518 C211.266577,175.784135 249.878426,221.331018 249.878426,221.331018 Z" id="路径-24" fill="url(#linearGradient-6)"></path>
                <path d="M91.0737663,114.257759 C91.0737663,148.922173 91.0737663,169.383395 91.0737663,175.641425 C91.0737663,185.02847 100.719648,203.786817 118.77638,217.060605 C130.814201,225.909796 143.763263,232.339624 157.623566,236.350087 C179.368418,228.751666 193.743538,222.321839 200.748926,217.060605 C211.25701,209.168754 224.9983,182.012301 224.9983,177.67226 C224.9983,174.7789 224.9983,153.640733 224.9983,114.257759 C225.632484,105.613278 224.534748,100.360329 221.705093,98.4989123 C217.460609,95.7067867 202.840312,94.9052156 188.289619,89.2855948 C173.738926,83.665974 166.889949,74.6388573 159.782881,74.6388573 C152.675813,74.6388573 141.209976,84.7742312 128.84836,89.2855948 C116.486743,93.7969585 94.4865403,96.1307755 92.715875,99.328676 C91.5354316,101.46061 90.988062,106.436971 91.0737663,114.257759 Z" id="路径-23" fill="url(#linearGradient-7)"></path>
                <path d="M90.4348461,119.566954 C90.4348461,152.286716 90.4348461,171.600077 90.4348461,177.507035 C90.4348461,186.367473 99.7331453,204.073489 117.139216,216.602625 C128.743264,224.955383 141.225716,231.024501 154.586574,234.80998 C175.547866,227.637826 189.404989,221.568707 196.157944,216.602625 C206.287376,209.153502 219.53351,183.520509 219.53351,179.423942 C219.53351,176.692897 219.53351,156.740568 219.53351,119.566954 C220.144841,111.407423 219.086661,106.449161 216.35897,104.692168 C212.267434,102.056679 198.173968,101.300075 184.147598,95.9957117 C170.121228,90.6913481 163.519049,82.1706468 156.66808,82.1706468 C149.81711,82.1706468 138.764435,91.7374327 126.84826,95.9957117 C114.932085,100.253991 93.7246433,102.456882 92.0177826,105.475382 C90.8798756,107.487716 90.35223,112.184906 90.4348461,119.566954 Z" id="路径-23" fill="url(#linearGradient-8)"></path>
                <g id="形状" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                    <use fill="#FFFFFF" xlink:href="#path-9"></use>
                </g>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                    <use fill="url(#linearGradient-11)" fill-rule="evenodd" xlink:href="#path-12"></use>
                </g>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-15)" xlink:href="#path-14"></use>
                    <use fill="#FFFFFF" xlink:href="#path-14"></use>
                </g>
                <g id="密码-(2)" transform="translate(128.000000, 114.000000)"></g>
                <g id="人" filter="url(#filter-16)" transform="translate(56.258098, 160.270056)">
                    <g id="编组-4" transform="translate(0.000000, 68.861706)">
                        <path d="M22.3105065,4.84133317 C21.5379891,5.19682331 18.788008,5.859559 14.060563,6.82954023 L13.8192513,2.69741228 L21.792067,1.94617726 C22.9102107,3.52079105 23.0830239,4.48584302 22.3105065,4.84133317 Z" id="路径-21" fill="#F5C9B4"></path>
                        <path d="M17.1075006,6.82954023 C16.7646551,6.82954023 6.84895375,9.09831395 4.28751767,8.51644521 C1.72608159,7.93457648 -0.162342333,5.63235514 0.0110406822,3.323114 C0.184423698,1.01387285 1.59556994,0.399551509 3.0352895,0.128533525 C4.47500905,-0.142484459 11.1383244,-0.00441564711 14.0243135,0.729845077 C16.9103027,1.4641058 17.4503461,6.82954023 17.1075006,6.82954023 Z" id="路径-22" fill="#FFD15D"></path>
                    </g>
                    <path d="M22.0730852,7.7791825 C21.5952999,11.4273802 21.5952999,13.9859963 22.0730852,15.4550307 C22.789763,17.6585825 23.3524574,18.0878186 23.3524574,19.468952 C23.3524574,20.3897075 23.3524574,22.3866451 23.3524574,25.4597645 L32.5095706,24.6597341 C31.9873553,21.181254 31.7262477,19.0828345 31.7262477,18.3644757 C31.7262477,17.2869375 34.1767483,14.7298954 34.1767483,13.0209246 C34.1767483,11.8816106 33.3599148,9.24235032 31.7262477,5.10314359 L26.5899406,4.6548735 L22.0730852,7.7791825 Z" id="路径-18" fill="#F5C9B4"></path>
                    <path d="M22.7599207,20.726904 C21.970015,20.726904 16.0259028,19.4060174 12.8136799,22.7517173 C9.601457,26.0974172 7.59840664,35.3374327 7.59840664,39.3549612 C7.59840664,43.3724897 8.10749744,47.0558109 9.34593904,48.2742346 C10.1715668,49.0865171 22.0862005,49.0865171 45.0898403,48.2742346 C46.6236784,42.3057551 46.883892,37.4309226 45.870481,33.6497372 C44.3503646,27.9779591 43.6996814,22.0461068 39.9856133,21.2266444 C37.5095679,20.6803361 35.0367967,20.6803361 32.5672999,21.2266444 C32.4456783,23.5142513 30.7652391,24.6580547 27.5259824,24.6580547 C22.6670972,24.6580547 23.5498264,20.726904 22.7599207,20.726904 Z" id="路径-19" fill="url(#linearGradient-17)"></path>
                    <path d="M26.6333238,7.7791825 C25.6297262,7.7791825 24.1096467,7.7791825 22.0730852,7.7791825 L21.5781185,13.0209246 C21.2292238,10.6929386 20.9225607,9.23731837 20.6581292,8.65406402 C20.261482,7.7791825 19.0031328,6.38000659 19.0031328,5.02402615 C19.0031328,3.66804572 21.2446246,2.50367483 22.0730852,2.50367483 C22.9015457,2.50367483 25.1761588,0 26.6333238,0 C28.0904887,0 29.6988744,1.58867435 30.2197314,2.04617459 C30.7405885,2.50367483 33.360032,2.11374988 33.925451,3.15240805 C34.49087,4.19106623 33.925451,6.9784299 33.925451,8.65406402 C33.925451,9.77115344 33.925451,10.9453721 33.925451,12.1767201 C32.9810025,10.4115371 32.2982218,9.23731837 31.8771087,8.65406402 C31.4559957,8.07080967 31.0771556,7.13412667 30.7405885,5.84401502 C29.0060096,7.13412667 27.6369213,7.7791825 26.6333238,7.7791825 Z" id="路径-17" fill="#425175"></path>
                    <path d="M13.0088004,41.483955 L41.9241643,41.483955 C43.623502,41.483955 45.0010874,42.8615404 45.0010874,44.5608781 C45.0010874,44.8282674 44.9662332,45.0945161 44.8974057,45.3528954 L40.3828319,62.3006535 C40.0239296,63.6479767 38.803897,64.5855593 37.4095906,64.5855593 L17.5233742,64.5855593 C16.1290677,64.5855593 14.9090351,63.6479767 14.5501328,62.3006535 L10.0355591,45.3528954 C9.59814001,43.7108195 10.5747073,42.0250558 12.2167832,41.5876367 C12.4751624,41.5188092 12.7414111,41.483955 13.0088004,41.483955 Z" id="矩形" fill="#165081"></path>
                    <path d="M20.3071911,53.5507725 C17.7438191,54.0610825 15.8761821,59.6867029 18.0916866,62.0396228 C20.3071911,64.3925428 27.9632629,69.1515289 34.3382667,70.8277342 C38.5882692,71.9452044 45.0165871,73.1826012 53.6232203,74.5399246 L55.4766894,68.7628292 C49.7688153,64.1072381 43.7394315,60.4797513 37.3885382,57.8803685 C27.8621981,53.9812944 22.8705632,53.0404625 20.3071911,53.5507725 Z" id="路径-20" fill="#394A6E" transform="translate(36.292095, 63.981673) scale(-1, 1) translate(-36.292095, -63.981673) "></path>
                    <g id="编组-4备份" transform="translate(43.699681, 73.167651) scale(-1, 1) translate(-43.699681, -73.167651) translate(32.307692, 68.861706)">
                        <path d="M22.3105065,4.84133317 C21.5379891,5.19682331 18.788008,5.859559 14.060563,6.82954023 L13.8192513,2.69741228 L21.792067,1.94617726 C22.9102107,3.52079105 23.0830239,4.48584302 22.3105065,4.84133317 Z" id="路径-21" fill="#F5C9B4"></path>
                        <path d="M17.1075006,6.82954023 C16.7646551,6.82954023 6.84895375,9.09831395 4.28751767,8.51644521 C1.72608159,7.93457648 -0.162342333,5.63235514 0.0110406822,3.323114 C0.184423698,1.01387285 1.59556994,0.399551509 3.0352895,0.128533525 C4.47500905,-0.142484459 11.1383244,-0.00441564711 14.0243135,0.729845077 C16.9103027,1.4641058 17.4503461,6.82954023 17.1075006,6.82954023 Z" id="路径-22" fill="#FFD15D"></path>
                    </g>
                    <path d="M3.3841142,53.5507725 C0.820742147,54.0610825 -1.04689481,59.6867029 1.16860969,62.0396228 C3.3841142,64.3925428 11.040186,69.1515289 17.4151898,70.8277342 C21.6651923,71.9452044 28.0935102,73.1826012 36.7001434,74.5399246 L38.5536125,68.7628292 C32.8457383,64.1072381 26.8163546,60.4797513 20.4654612,57.8803685 C10.9391212,53.9812944 5.94748625,53.0404625 3.3841142,53.5507725 Z" id="路径-20" fill="#41547D"></path>
                </g>
            </g>
        </g>
    </g>
</svg>