<?xml version="1.0" encoding="UTF-8"?>
<svg width="1px" height="65px" viewBox="0 0 1 65" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Rectangle</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#0854A0" offset="0%"></stop>
            <stop stop-color="#0854A0" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="控件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="LAYOUTS,-FLOORPLANS-&amp;-FRAMEWORKS/FLEXIBLE-COLUMN-LAYOUT/BUIDLING-BLOCKS/Arrow-Left" transform="translate(-8.000000, 0.000000)" fill="url(#linearGradient-1)">
            <g id="Flex-Arrow" transform="translate(8.000000, 81.000000) rotate(-180.000000) translate(-8.000000, -81.000000) translate(0.000000, 0.000000)">
                <rect id="Rectangle" x="7" y="97" width="1" height="65"></rect>
            </g>
        </g>
    </g>
</svg>