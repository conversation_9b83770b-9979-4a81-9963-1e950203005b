import {getCurrentUser} from "../../services/server";

class UserManager{
	constructor() {
		this.currentUser = null;
	}

	/**
	 * 获取当前用户信息
	 * @param isRefresh 重新从后端请求，不管有没有缓存
	 * @returns {Promise<void>}
	 */
	async getCurrentUser(isRefresh){
		if (!this.currentUser || isRefresh){
			this.currentUser = await getCurrentUser();
		}
		return this.currentUser;
	}
}
export const userManager = new UserManager();