import { Notification } from 'element-ui'
import i18n from "i18next"

const constructDetail = (detail) => {
  if (!Array.isArray(detail)) {
    return JSON.stringify(detail)
  } else if (typeof detail[0] === 'string') {
    return detail.join('<br/><br/>')
  } else if (typeof detail[0] === 'object') {
    return detail.map(detail => {
      return parseErrorMessage(detail)
    }).join('<br/><br/>')
  }
}

const parseErrorMessage = (errorMsg) => {
  if (errorMsg.detail) {
    const detail = constructDetail(errorMsg.detail)
    return `<div>${errorMsg.message}</div>
            <br/>
            <div>${detail}</div>`
  } else if (errorMsg.message) {
    return errorMsg.message
  } else {
    return errorMsg
  }
}

export default class ErrorHandler {
  /**
   * @param error
   * @param {string=} customClass 调用时自定义的类名
   * @example
   * customClass 'improveZIndex' 提升z-index至10001
   */
  static formatError (error, customClass) {
    // 没有项目空间id时，在接口处返回noProjectSpaceId，当捕获到这个错误时，不需要弹出错误提示
    // 取消前一次重复请求不需要弹出错误提示
    if (error.message === 'noProjectSpaceId' || error.message === 'cancel') {
      return
    }
    Notification.closeAll()
    if (!error.response || (error.response && error.response.status !== 401)) {
      Notification.error({
        title: i18n.t('操作失败'),
        dangerouslyUseHTMLString: true,
        message: error.response ? parseErrorMessage(error.response.data) : parseErrorMessage(error),
        duration: 0,
        customClass
      })
    }
  }
}
