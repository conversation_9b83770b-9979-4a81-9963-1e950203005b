export default class ArrayUtil {
  static getSecondLastValue = (array) => {
    if (Array.isArray(array)) {
      return array.length > 1 ? array[array.length - 2] : null
    }
    return null
  }

  static elementEquals = (arr1, arr2) => {
    if (Array.isArray(arr1) && Array.isArray(arr2)) {
      return arr1.length === arr2.length && arr1.every(a => arr2.some(b => a === b)) && arr2.every(_b => arr1.some(_a => _a === _b))
    }
    return false
  }

  static elementAndIndexEquals = (arr1, arr2) => {
    if (Array.isArray(arr1) && Array.isArray(arr2)) {
      if (arr1.length !== arr2.length) {
        return false
      }
      for (let i = 0; i < arr1.length; i++) {
        if (arr1[i] !== arr2[i]) {
          return false
        }
      }
      return true
    }
    return false
  }

  /**
   * 是数组类型并且有值
   * @param array
   * @returns {boolean}
   */
  static hasValue (array) {
    return Array.isArray(array) && array.length > 0
  }

  /**
   * 是数组并且存在不为null和undefined的值
   * @param array
   * @returns {boolean}
   */
  static hasLegalValue (array) {
    return Array.isArray(array) && !!array.find(elem => elem != null)
  }

  /**
   * 过滤数组中的null和undefined，返回一个新数组
   * @param array
   * @returns {[]}
   */
  static deleteNothingValue (array) {
    return ArrayUtil.hasValue(array) ? array.filter(elem => elem != null) : []
  }

  /**
   * 数组去重，不保证顺序
   */
  static deleteRepeat (array) {
    return ArrayUtil.hasValue(array) ? Array.from(new Set(array)) : []
  }
}
