import axios from 'axios'
import { Notification, MessageBox } from 'element-ui'

const instance = axios.create({
  baseURL: '',
  timeout: 100000,
  timeoutErrorMessage: '连接超时'
})
instance.interceptors.response.use(
  response => {
    return response
  },
  error => {
    if (error.response.status.toString().startsWith('50')) {
      MessageBox.alert('内部服务异常', '错误', {
        type: 'error'
      })
    } else if (error.response.status === 401) {
      Notification.closeAll()
      Notification.warning({
        title: '登录超时****',
        message: '三秒后跳转到登录界面',
        duration: 3000,
        onClose: () => { 
          window.location.reload() }
      })
    }
    return Promise.reject(error.response)
  })
export default instance

instance.interceptors.request.use((config) => {
  config.headers.common = {
    // gateway不能处理axios请求，设置XMLHttpRequest模拟成ajax
    'X-Requested-With': 'XMLHttpRequest'
  }

  config.url = encodeURI(config.url)
  return config;
}, (error) => {
  return Promise.reject(error)
})

const createAxios = (baseURL, timeout) => {
  const instance = axios.create({
    baseURL,
    timeout: timeout ? timeout : null,
    timeoutErrorMessage: '连接超时',
  })
  instance.interceptors.response.use(
      response => {
        return response
      },
      error => {
        if (error.response.status.toString().startsWith('50')) {
          MessageBox.alert('内部服务异常', '错误', {
            type: 'error'
          })
        } else if (error.response.status === 401) {
          Notification.closeAll()
          Notification.warning({
            title: '登录超时。。。',
            message: '三秒后跳转到登录界面',
            duration: 3000,
            onClose: () => { window.location.reload() }
          })
        }
        return Promise.reject(error.response)
      })

  instance.interceptors.request.use((config) => {
    config.headers.common = {
      // gateway不能处理axios请求，设置XMLHttpRequest模拟成ajax
      'X-Requested-With': 'XMLHttpRequest'
    }

    config.url = encodeURI(config.url)
    return config;
  }, (error) => {
    return Promise.reject(error)
  })
  return instance
}

export { createAxios }
