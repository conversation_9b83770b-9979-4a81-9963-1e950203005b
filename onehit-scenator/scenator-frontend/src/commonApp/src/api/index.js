import {createAxios} from "./axios";
import qs from "qs";
import baseUrls from "./baseUrlConfig";

const axios = createAxios(baseUrls.contextPath)

export const Api= {

  //TODO:--------------------------------------------------UAMS
  getCurrentUser: () => axios.get('/UAMS/users/currentUser'),
  changePwd: (param) => axios.put('/UAMS/users/changePwd', param),
  getKey: () => axios.get('/UAMS/getKey'),
  getSceneAuth: (roleId) => axios.get(`/OneHit/sceneAuth/getSceneAuth/${roleId}`),

  //TODO:--------------------------------------------------Scenator
  getUserId: () => axios.get('/Scenator/userId'),
  getUserScene: () => axios.get('/Scenator/userScenes'),
  getScenePermissions: () => axios.get('/Scenator/labels/?type=defaultViewpoint'),
  saveScene: (scene) => axios.post(`/Scenator/saveUserScene`, JSON.stringify(scene), {
    headers: {'Content-Type': 'application/json'}
  }),
  saveBaseLabel: (label) => axios.post('/Scenator/label', JSON.stringify(label), {headers: {'Content-Type': 'application/json'}}),
  getBaseLabel: (type) => axios.get(`/Scenator/labels/?type=${type}`),
  deleteBaseLabel: (ids) => axios.delete(`/Scenator/labels`, {data: ids}),

  //TODO:--------------------------------------------------Config
  getPipelineConfig: () => axios.get(`/Scenator/resources/apps/Renderer3D/config.json`),
  getSandBoxConfig: () => axios.get(`/Scenator/resources/apps/OneHit/config/sandBoxConfig.json`),
  getCommonConfig: () => axios.get(`/Scenator/resources/apps/OneHit/config/config.json`),

  //TODO:--------------------------------------------------AIMS
  /**
   * 通过ES查询获取文档
   * @param {String} structureNodeId - 组织节点Id
   */
  getRepositories: () => axios.get(`/AIMS/repositories`),
  getDocumentCategories: () => axios.get(`/AIMS/classLib/documentCategories`),
  getDocumentIds: (structureNodeId) => axios.get(`/AIMS/structures/structureNodes/${structureNodeId}/documents`),
  getReferences: (repositoryId, formData) => axios.get(`/AIMS/repositories/${repositoryId}/versions/latest/references`, { params: formData }),
  getBulkReferences: (param) => axios.post(`/AIMS/repositories/${param.repositoryId}/references/bulk`, qs.stringify({ targetItemIds: JSON.stringify(param.targetItemIds) })),
  getReferences2: (repositoryId, formData) => axios.post(`/AIMS/repositories/${repositoryId}/versions/latest/references/bulk`, formData),
  getDocuments: (repositoryId, documentIds) => axios.post(`/AIMS/repositories/${repositoryId}/documents/bulk`, qs.stringify({ documentIds: JSON.stringify(documentIds) })),
  getAllRepositories: () => axios.get(`/AIMS/repositories`),
  getPidDocuments: (param) => axios.get(`/AIMS/repositories/${param.repositoryId}/versions/latest/documents?documentCategory=pidintellectdwg`),
  batchProperties: (param) => axios.post(`/AIMS/repositories/${param.repositoryId}/versions/latest/items/batchProperties`, qs.stringify({itemIds: JSON.stringify(param.targetIds)})),
  getDocumentProperty: (repositoryId, filters) => axios.post(`/${repositoryId}_v1_document/_search`, {
    "size":9999,
    "query":{
      "bool":{
        "minimum_should_match": 1,
        "should":filters
      }
    }
  }),

  batchPropertiesForEs: (param) => axios.post(`/AIMS/repositories/${param.repositoryId}/versions/latest/es/items`,
      qs.stringify({
        itemIds: JSON.stringify(param.targetIds),
        scroll: "1ms",
        size: param.size
      }),
  ),

  scrollBatchPropertiesForEs: (scrollId) => axios.post(`/AIMS/es/scroll/items`,
      qs.stringify({
        scroll: "1ms",
        scrollId: scrollId,
      }),
  ),
  searchDocumentsByText: (param) => axios.post(`/AIMS/repositories/${param.repositoryId}/versions/latest/documents/fulltext`, qs.stringify({
    keywords: JSON.stringify(param.keywords),
    options: JSON.stringify({}),
  })),

  getPsdata: (repositoryId) => axios.get(`/AIMS/repositories/${repositoryId}/versions/latest/psdata`),
  getBulkPsdata: (repositoryIds) => axios.post(`/AIMS/psData/bulk`, repositoryIds),
  getAmisNode: (param) => axios.get(`/AIMS/repositories/${param.repositoryId}/versions/${param.versionId}/structures/documentId/${param.documentId}/structureNodes?templateId=${param.templateId}`),
  getRepositoryPermissions: () => axios.get(`/AIMS/currentUserPrivileges`),
  getRootNode: (repositoryId, taskId, templateId) => axios.get(`/AIMS/repositories/${repositoryId}/versions/latest/structures/structureNodes/root`, {params: {
      taskId: taskId,
      templateId: templateId
  }}),
  // getTreeNodeByItemIds: (repositoryId, itemIds) => axios.post(`/AIMS/repositories/${repositoryId}/versions/latest/structures/structureNodes/bulk`, itemIds),
  getTreeNodeByItemIds: (repositoryId, formData) => axios.post(`/AIMS/repositories/${repositoryId}/versions/latest/structures/structureNodes/by-items-bulk`, formData),
  getTreeNodePosterity: (repositoryId, structureNodeId) => axios.get(`/AIMS/repositories/${repositoryId}/versions/latest/structures/structureNodes/${structureNodeId}/posterity`),
  getItemIdsByStructureNodeIds: (repositoryId, formData) => axios.post(`/AIMS/repositories/${repositoryId}/versions/latest/structures/structureNodes/items/bulk`, formData),
  getItemsByPosition: (repositoryId, positions) => axios.get(`/AIMS/repositories/${repositoryId}/versions/latest/structures/overPlaneProportion`, {params: {
    minOverPlaneProportion: positions.split(',')[0],
    maxOverPlaneProportion: positions.split(',')[1]
  }}),
  getClassLibUnits: () => axios.get(`/AIMS/classLib/units`),
  downloadDoc: (repositoryId, documentId) => axios.get(`/AIMS/repositories/${repositoryId}/versions/latest/documents/${documentId}/stream`),


  //TODO:---------------------------------------------------------------------------ES
  getEsIndices: () => axios.get(`/_cat/indices?v&h=i`),
  getEsIndices2: () => axios.get(`/_cat/indices`),
  getDocumentById: (repositoryId, docId) => axios.get(`/${repositoryId}_v1_document/_doc/${docId}`),
  searchNode: (param) => axios.post(`/${param.repositoryId}_v1_document/_search`, param.data),
  getFilterResults: (repositoryId, filterObj) => axios.post(`/${repositoryId}_v1_document/_search`, filterObj),
  getCategoryFilterResults: (repositoryId, filterObj) => axios.post(`/${repositoryId}_item/_search?scroll=1m`, filterObj),
  // 该方法在筛选工厂对象、设备目录、超级检索处均使用
  getCategoryFilterResultsByScroll: (scrollId) => axios.post('/_search/scroll', {scroll: '1m', scroll_id: scrollId}),
  getEsDocuments: (url, params) => axios.post(`/${url}/_search`, params),
  getItemsFromES: (indexs, searchCondition) => axios.post(`/${indexs}/_search?scroll=1m`, searchCondition),
  getItemByTag: (repositoryId, params) => axios.post(`/${repositoryId}_item/_search`, params),

  // TODO:---------------------------------------------------------------------------APPS
  deleteFilterData: (userId, workSpaceId) => axios.post(`/OneHit/ff/data/del/${workSpaceId}`),
  saveFilterData: (params) => {
    let url = null;
    if(params.dissociate) {
      url = `/OneHit/ff/data/set/${params.workSpaceId}?dissociate=${params.dissociate}`
    } else {
      url = `/OneHit/ff/data/set/${params.workSpaceId}`
    }
    return axios.post(url, {
      filterData: JSON.stringify(params.filterData),
      filterConditions: JSON.stringify(params.filterConditions),
      positions: params.selectPositions
    })
  },
  getFilterData: (userId, workSpaceId, dissociate) => {
    let url = null;
    if(dissociate) {
      url = `/OneHit/ff/data/${workSpaceId}?dissociate=${dissociate}`
    } else {
      url = `/OneHit/ff/data/${workSpaceId}`
    }
    return axios.get(url)
  },
  copyFilterData: (params) => axios.post(`/OneHit/ff/data/copy/${params.workSpaceId}`, JSON.stringify(params.workSpaceList), {
    headers: {'Content-Type': 'application/json'}
  }),

  DwgFileToPdfFile: (data) => axios.post(`/OneHit/PidExport/exportPdfFromDoc/byte`, data, {responseType: 'blob'}),
  addNotes: (params) => axios.post('/OneHit/handyNote/', params),
  getNotesList: (params) => axios.get(`/OneHit/handyNote/list`, {params: params}),
  editNoteInfo: (params) => axios.put(`/OneHit/handyNote/`, params),
  deleteNote: (id) => axios.delete(`/OneHit/handyNote/?id=${id}`),
  deleteBatchByIds: (params) => axios.delete(`/OneHit/handyNote/batch?ids=${params.ids}`),
  getLabelModelById: (params) => axios.get(`/OneHit/label/label/queryById/?id=${params.id}`),
  getLabelClassificationById: (params) => axios.get(`/OneHit/label/classification/queryById?id=${params.id}`),
  getLabelTypeById: (param) => axios.get(`/OneHit/label/classification/queryById`, {params: param}),
  getLabelAccessoryByLabelId: (params) => axios.get(`/OneHit/label/attachment/label/${params.labelId}`),
  getExcelTemplate: (type) => axios.get(`/OneHit/common/excel/download/template?type=${type}`, {responseType: 'blob'}),
  getHelpTip: (params) => axios.get(`/OneHit/common/excel/download/helpTip?identifier=${params.identifier}&fileName=${params.fileName}&type=${params.type}`, {responseType: 'blob'}),
  parseExcel: (params) => axios.post(`/OneHit/common/excel`, params,{timeout:1000 * 60 * 10}),
  deleteFile: (id) => axios.delete(`/OneHit/common/file/${id}`),
  saveNewFeatureDescriptionLog: (params) => axios.put(`/OneHit/newFeatureDescriptionLog`, params),
  getNewFeatureDescriptionLog: () => axios.get(`/OneHit/newFeatureDescriptionLog`),
  // 合并分片
  commonMergeFile: (data) => {
    const form = new FormData();
    Object.keys(data).forEach((item) => {
      form.append(item, data[item]);
    });
    return axios.post('/OneHit/common/file/merge', form)
  },

  // 创建关联关系
  createRelation: (data) => {
    const form = new FormData();
    Object.keys(data).forEach((item) => {
      form.append(item, data[item]);
    });
    return axios.post('/OneHit/common/file/relation', form)
  },
  // 校验文件是否存在
  checkFile: (data) => {
    const form = new FormData();
    Object.keys(data).forEach((item) => {
      form.append(item, data[item]);
    });
    return axios.post('/OneHit/common/file/check', form)
  },

  // 删除关联关系
  deleteRelation: ( params ) => axios.delete('/OneHit/common/file/relation', { params }),
  getAIMSDocument: (params) => axios.get(`/OneHit/related/document/repositories/${params.repositoryId}/item?itemIds=${params.itemIds}`),
  /**获取关联文档*/
  getUploadFiles: (itemId, params) => axios.get(`/OneHit/related/document/item/${itemId}`, {params: params}),
  addLabel: (param) => axios.post('/OneHit/label/label/add', param),
  getNoteInfoById: (id) => axios.get(`/OneHit/handyNote/${id}`),
  exportLabels: () => axios.get('/OneHit/label/classification/exportExcel', {responseType: 'blob'}),
  editLabel: (param) => axios.put('/OneHit/label/label/edit', param),
  deleteLabel: (param) => axios.delete(`/OneHit/label/label/delete`, {params: param}),
  // 获取最近使用的标注分类
  getRecentlyLabels: (param) => axios.get('/OneHit/label/classification/recently', {params: param}),
  // 获取标注类型列表
  getLabelTypeList: (param) => axios.get('/OneHit/label/classification/list', {params: param}),

  getEnabledPlugins: (param) => axios.get('/OneHit/plugin', {params: param}),
  getRecentlyUsedPlugins: () => axios.get('/OneHit/plugin/recent/use'),
  getTempPlugin: () => axios.get('/OneHit/plugin/recent/temp'),
  recordLog: (id) => axios.post(`/OneHit/plugin/log/${id}`),
  chgPluginStatus: (status, id) => axios.put(`/OneHit/plugin/status/${status}`, id),
  addTempPlugin: (id) => axios.post(`/OneHit/plugin/temp/log/${id}`),
  deleteTempPlugin: (id) => axios.delete(`/OneHit/plugin/temp/log/${id}`),

  // 设备管理 EDC接口
  selectObjectModelList: (params) => axios.post('/EDC/plantObjects/models', JSON.stringify(params), {headers: {'Content-Type': 'application/json'}}),
  selectObjectModelCategories: (params) => axios.post('/EDC/plantObjects/models/categories', JSON.stringify(params), {headers: {'Content-Type': 'application/json'}}),
}
