import Vue from 'vue'
import Element from 'element-ui'
import index from './Index.vue'
import '../../../../../../../onehit-ui/src/assets/element-variables.scss'
import '../../../../../../../onehit-ui/src/assets/edc-global.scss'
import '../../../../../../../onehit-ui/src/assets/edc-icon.css'
import '../../../../../../../onehit-ui/src/assets/edc-icon.js'
import i18n from '../../../../../../../onehit-ui/src/languages'

Vue.config.productionTip = false

Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value)
})

export class HeaderUserExtension {
  getPointName () {
    return 'headFunNavMenuItem'
  }

  getImplements () {
    return {
      id: 'header-user',
      index: 99,
      onset: ($element) => {
        const dom = $element[0]
        dom.innerHTML = "<div id = '" + dom.id + "_extension'></div>"
        new Vue({
          i18n,
          render: h => h(index)
        }).$mount('#' + dom.id + '_extension')
      }
    }
  }
}
