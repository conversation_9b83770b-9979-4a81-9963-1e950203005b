<template>
  <div class="functional-box">
    <personal-center></personal-center>
  </div>
</template>

<script>
import PersonalCenter from 'src/commonApp/src/components/header/PersonalCenter.vue'

export default {
  name: 'index',
  components: { PersonalCenter }
}
</script>

<style scoped lang="scss">
.functional-box {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  & * {
    margin-right: 8px;
  }
}
</style>
