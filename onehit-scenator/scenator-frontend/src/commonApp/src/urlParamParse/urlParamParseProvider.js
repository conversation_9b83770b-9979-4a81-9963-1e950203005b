import {urlParamListener} from "./urlParamListener";

/**
 * 场景初始化时解析URL参数
 * 1、如果是三位场景，根据位号定位
 * 2、如果是PID场景，则定位打开图纸
 * 将代码写在 getViewType 里，为了不影响后续逻辑，getViewType里的代码应该异步执行
 */

class UrlParamParseProvider{
    getViewType(){
        setTimeout(() => {
            urlParamListener.init();
        }, 0);
        return "UrlParamParseProvider"
    }
    request(){
        return {};
    }
}
export const urlParamParseProvider = new UrlParamParseProvider();