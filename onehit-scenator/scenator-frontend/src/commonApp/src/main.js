import { HeaderUserExtension } from 'src/commonApp/src/extension/header-user/app.js'
import { UserViaSelectExtensionPoint } from 'src/commonApp/src/extension-point/user-via-select'
import { urlParamParseProvider } from "src/commonApp/src/urlParamParse/urlParamParseProvider";
import { headFunMenuItemExtension } from "src/commonApp/src/extension/systemSetting/HeadFunMenuItemExtension";

class CommonApp {
	constructor() {
		console.log("==============通用App======================")
		this.name = "CommonApp";
		this.viewProviders = [urlParamParseProvider];
		this.extensionPoints = [new UserViaSelectExtensionPoint()];
		this.extensions = [headFunMenuItemExtension, new HeaderUserExtension()];
	}
}
export default new CommonApp();