<template>
  <el-dialog
    width="480px"
    append-to-body
    :title="$t('header.changePassword')"
    :visible.sync="editDialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :before-close="handleClose">
    <el-form ref="form" :model="form" :rules="rules" label-width="110px" size="small" >
              <el-form-item prop="userOldPwd" :label="$t('header.oldPassword') + '：'" style="position: relative" id="userOldPwd">
                  <el-input :type="userOldPwdflag ? 'text' : 'password'" v-model.trim="form.userOldPwd" :placeholder="$t('请输入')" @change="changeUserOldPwd">
          <i
            slot="suffix"
            class="icon-style"
            :class="userOldPwdflag ? 'edc-icon-xianshimima' : 'edc-icon-yincangmima'"
            autocomplete="auto"
            @click="userOldPwdflag = !userOldPwdflag"
          />
        </el-input>
        <span v-if="iserr" class="err_msg">{{iserr}}</span>
      </el-form-item>
              <el-form-item prop="userNewPwd" :label="$t('header.newPassword') + '：'"  style="position: relative" id="userNewPwd">
        <span v-if="isRepeat" class="err_msg">{{isRepeat}}</span>
        <el-popover
          placement="right"
          popper-class="editPwd"
          title=""
          width="348"
          trigger="focus">
          <div>
            <i v-if="form.userNewPwd.length >= minPasswordLength && form.userNewPwd.length <= maxPasswordLength" class="edc-icon-chenggong" style="color: #107f3e"></i>
            <i v-else class="edc-icon-cuowu" style="color: #e00000" ></i>
            <span style="margin-left: 8px">{{$t('header.passwordLengthTip') + ' ' + minPasswordLength + '-' + maxPasswordLength + ' ' + $t('header.charactersBetween')}}</span>
          </div>
          <div>
            <i v-if="isRight" class="edc-icon-chenggong" style="color: #107f3e"></i>
            <i v-else class="edc-icon-cuowu" style="color: #e00000"></i>
            <span style="margin-left: 8px">{{$t('header.passwordRequirements') + ' ' + passwordTip}}</span>
          </div>
          <el-input
            @blur="diffPwd"
            :type="userNewPwdflag ? 'text' : 'password'"
            slot="reference"
            v-model.trim="form.userNewPwd"
            :placeholder="$t('请输入')"
            @input="inputCheck"
            @change="changeUserNewPwd">
            <i
              slot="suffix"
              class="icon-style"
              :class="userNewPwdflag ? 'edc-icon-xianshimima' : 'edc-icon-yincangmima'"
              autocomplete="auto"
              @click="userNewPwdflag = !userNewPwdflag"
            />
          </el-input>
        </el-popover>
      </el-form-item>
              <el-form-item prop="checkUserPwd" :label="$t('header.confirmPassword') + '：'" id="checkUserPwd" style="position: relative">
                  <el-input  @blur="diffPwd" v-model.trim="form.checkUserPwd" :placeholder="$t('请输入')" :type="checkUserPwdflag ? 'text' : 'password'">
          <i
            slot="suffix"
            class="icon-style"
            :class="checkUserPwdflag ? 'edc-icon-xianshimima' : 'edc-icon-yincangmima'"
            autocomplete="auto"
            @click="checkUserPwdflag = !checkUserPwdflag"
          />
        </el-input>
        <span v-if="isDifference" class="err_msg">{{ $t('header.passwordMismatch') }}</span>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleClose">{{ $t('cancel') }}</el-button>
        <el-button size="small" type="primary" :disabled='commitDisabled' @click="save">{{ $t('确定') }}</el-button>
      </span>
  </el-dialog>
</template>
<script>
import { userApi } from 'src/commonApp/src/api/uams'
import $ from 'jquery'
import ErrorHandler from 'src/commonApp/src/utils/ErrorHandler'
import CryptoJS from 'crypto-js'

export default {
  data () {
    return {
      maxPasswordLength: 100,
      minPasswordLength: 12,
      passwordTip: this.$t('header.twoTypes'),
      passwordReg: /^(?![\d]+$)(?![a-z]+$)(?![A-Z]+$)(?![~!@#^&*?、/_.-]+$)[\da-zA-z~!@#^&*?、/_.-]+$/,
      commitDisabled: false,
      isDifference: false,
      userNewPwdflag: false,
      checkUserPwdflag: false,
      userOldPwdflag: false,
      editDialogVisible: false,
      isRight: false,
      iserr: '',
      isRepeat: '',
      form: {
        userOldPwd: '',
        userNewPwd: '',
        checkUserPwd: ''
      },
      rules: {
        userOldPwd: [
          { required: true, message: this.$t('header.oldPasswordEmpty'), trigger: 'blur' }
        ],
        userNewPwd: [
          { required: true, message: this.$t('header.newPasswordEmpty'), trigger: 'blur' },
          { validator: this.validateNewPwd, trigger: 'blur' }
        ],
        checkUserPwd: [
          { required: true, message: this.$t('header.confirmPasswordEmpty'), trigger: 'blur' }
        ]
      }
    }
  },
  mounted () {
    this.getPasswordLevel()
    this.getPasswordLengthLimit()
  },
  methods: {
    getPasswordLengthLimit () {
      userApi.getPasswordLengthLimit().then(({ data }) => {
        this.maxPasswordLength = data.maxPasswordLength
        this.minPasswordLength = data.minPasswordLength
      })
    },
    getPasswordLevel () {
      userApi.getPasswordLevel().then(({ data }) => {
        switch (data) {
          case 'LOW':
            this.passwordTip = this.$t('header.twoTypes')
            this.passwordReg = /^(?![\d]+$)(?![a-z]+$)(?![A-Z]+$)(?![~!@#^&*?、/_.-]+$)[\da-zA-z~!@#^&*?、/_.-]+$/
            break
          case 'NORMAL':
            this.passwordTip = this.$t('header.threeTypes')
            this.passwordReg = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z~!@#^&*?、/_.-]+$)(?![a-z0-9]+$)(?![a-z~!@#^&*?、/_.-]+$)(?![0-9~!@#^&*?、/_.-]+$)[a-zA-Z0-9~!@#^&*?、/_.-]+$/
            break
          case 'HIGH':
            this.passwordTip = '需包含数字、大写字母、小写字母和符号 (~!@#^&*?、/_.-)'
            this.passwordReg = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[~!@#^&*?、/_.-])[0-9A-Za-z~!@#^&*?、/_.-]*$/
            break
        }
      })
    },
    open () {
      this.userNewPwdflag = false
      this.checkUserPwdflag = false
      this.userOldPwdflag = false
      this.editDialogVisible = true
    },
    handleClose () {
      this.$refs.form.resetFields()
      this.iserr = ''
      this.isRepeat = ''
      this.isDifference = false
      this.form = {
        userOldPwd: '',
        userNewPwd: '',
        checkUserPwd: ''
      }
      this.editDialogVisible = false
    },
    /**
     * 加密请求
     */
    async postPassWord ({ userOldPwd, userNewPwd }) {
      // 获取Key
      const key = await this.encryption()
      // 加密密码
      userApi.changePwd({
        userOldPwd: this.encrypt(userOldPwd.trim(), key),
        userNewPwd: this.encrypt(userNewPwd.trim(), key)
      }).then(res => {
        this.$message.success('密码修改成功')
        this.handleClose()
        this.$emit('logout')
      }).catch((err) => {
        if (err.response.status === 400) {
          console.log(err.response)
          if (err.response.data.message.includes(this.$t('header.oldPasswordError'))) {
            this.iserr = err.response.data.message
            this.$nextTick(() => {
              $('#userOldPwd').addClass('is-error')
            })
          } else if (err.response.data.message.includes('密码重复')) {
            const number = err.response.data.message.split('').filter(item => !isNaN(parseInt(item)))
            this.isRepeat = '不支持设置最近' + number[0] + '次用过的密码'
            this.$nextTick(() => {
              $('#userNewPwd').addClass('is-error')
            })
          }
        } else {
          ErrorHandler.formatError(err)
        }
      }).finally(() => {
        this.commitDisabled = false
      })
    },

    /**
     * 获取加密key
     */
    async encryption () {
      const result = await userApi.getKey()
      return result.data
    },
    /**
     * 通过key加密密码
     */
    encrypt (content, key) {
      const sKey = CryptoJS.enc.Utf8.parse(key)
      const sContent = CryptoJS.enc.Utf8.parse(content)
      const encrypted = CryptoJS.AES.encrypt(sContent, sKey, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 })
      return encrypted.toString()
    },
    save () {
      this.$refs.form.validate((valid) => {
        if (!valid || this.isDifference || this.iserr || this.isRepeat) {
          this.saveIng = false
          return
        }
        this.commitDisabled = true
        this.postPassWord(this.form)
      })
    },
    changeUserOldPwd () {
      this.iserr = ''
      this.$nextTick(() => {
        $('#userOldPwd').removeClass('is-error')
      })
    },
    changeUserNewPwd () {
      this.isRepeat = ''
      this.$nextTick(() => {
        $('#userNewPwd').removeClass('is-error')
      })
    },
    diffPwd () {
      if (this.form.userNewPwd && this.form.checkUserPwd) {
        this.isDifference = this.form.userNewPwd !== this.form.checkUserPwd
        this.$nextTick(() => {
          this.form.userNewPwd !== this.form.checkUserPwd ? $('#userNewPwd').addClass('is-error') : $('#userNewPwd').removeClass('is-error')
          this.form.userNewPwd !== this.form.checkUserPwd ? $('#checkUserPwd').addClass('is-error') : $('#checkUserPwd').removeClass('is-error')
        })
      } else {
        this.isDifference = false
      }
    },
    inputCheck () {
      if (this.form.userNewPwd?.includes('\\')) {
        this.isRight = false
        return
      }
      this.isRight = this.passwordReg.test(this.form.userNewPwd)
    },
    validateNewPwd (rule, value, callback) {
      if (!this.passwordReg.test(value) || value.length < this.minPasswordLength || value.length > this.maxPasswordLength || value.includes('\\')) {
        callback(new Error(this.$t('密码格式错误')))
      } else if (value === this.form.userOldPwd) {
        callback(new Error(this.$t('header.newPasswordSameAsOld')))
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped lang="scss">
.err_msg{
  position: absolute;
  top: 100%;
  left: 0;
  font-size: 12px;
  padding-top: 8px;
  line-height: 1px;
  color: #BB0000;
}
::v-deep .el-input__suffix{
  line-height: 30px!important;
}
::v-deep .el-form-item__label{
  color:rgba(24, 42, 78, 1) !important;
}
</style>
