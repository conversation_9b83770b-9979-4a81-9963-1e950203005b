<template>
  <el-dialog
    width="480px"
    append-to-body
    :title="$t('header.personalInfo')"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false">
    <el-form ref="form" size="small"  label-width="132px" style="color: rgba(24, 42, 78, 1) !important;">
      <el-form-item :label="$t('username') + '：'">
        <span>{{currentUser.username}}</span>
      </el-form-item>
      <el-form-item :label="$t('name') + '：'">
        <span>{{currentUser.name}}</span>
      </el-form-item>
      <el-form-item :label="$t('mobilePhone') + '：'">
        <span>{{currentUser.mobilePhone || '-'}}</span>
      </el-form-item>
      <el-form-item :label="$t('email') + '：'">
        <span>{{currentUser.email || '-'}}</span>
      </el-form-item>
      <el-form-item :label="$t('职位') + '：'" v-show="currentUser.position !== undefined">
        <span>{{currentUser.position || '-'}}</span>
      </el-form-item>
      <el-form-item :label="(currentUser.departments ? currentUser.departments.some(i => i.type === '-1' || i.type === '-2') ? $t('department') : $t('company') : $t('department')) + '：'">
        <el-tooltip :open-delay="500" effect="dark" :content="currentUser.departments ? currentUser.departments.filter(item => item.type.indexOf('COOPERATE') === -1 && item.type.indexOf('ORGANIZATION') === -1).map(item => item.name).join('、') : ''" placement="bottom">
          <span class="department">{{currentUser.departments && currentUser.departments.filter(item => item.type.indexOf('COOPERATE') === -1 && item.type.indexOf('ORGANIZATION') === -1).map(item => item.name).join('、')}}</span>
        </el-tooltip>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
          <el-button style="color: rgba(8, 84, 161, 1)!important;" size="small" @click="dialogVisible = false">{{ $t('关闭') }}</el-button>
      </span>
  </el-dialog>
</template>
<script>
export default {
  props: ['currentUser'],
  data () {
    return {
      dialogVisible: false
    }
  },
  methods: {
    open () {
      this.dialogVisible = true
    }
  }
}
</script>
<style scoped>
.department{
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  overflow:hidden;
  line-height: 22px;
  margin-top: 6px;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
}
</style>
