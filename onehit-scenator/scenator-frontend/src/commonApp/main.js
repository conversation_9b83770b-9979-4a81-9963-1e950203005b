import { HeaderUserExtension } from './extension/header-user/app.js'
import { UserViaSelectExtensionPoint } from './extension-point/user-via-select'
import { urlParamParseProvider } from "./urlParamParse/urlParamParseProvider";
import { headFunMenuItemExtension } from "./extension/systemSetting/HeadFunMenuItemExtension";

class CommonApp {
	constructor() {
		console.log("==============通用App======================");
		this.name = "CommonApp";
		this.viewProviders = [urlParamParseProvider];
		this.extensionPoints = [new UserViaSelectExtensionPoint()];
		this.extensions = [headFunMenuItemExtension, new HeaderUserExtension()];
	}
}
export default new CommonApp();