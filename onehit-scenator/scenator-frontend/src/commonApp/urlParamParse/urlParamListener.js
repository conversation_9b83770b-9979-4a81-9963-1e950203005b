import Param from "./model/Param";
import {sceneManager} from "finger";
import _ from "lodash";

class UrlParamListener{
	constructor() {
		this.param = null;
		this.documentPreviewView = null;
	}

	init(){
		try {
			this.param = new Param(new URLSearchParams(window.location.search));
			console.log("解析出的地址栏信息", this.param);
		}catch (e) {
			return;
		}
		const this_ = this;
		sceneManager.watchCurrentScene((currentScene) => {
			if (currentScene.name === this_.param.sceneName){
				// 通过setTimeout异步，不阻塞其他的回调
				setTimeout(async() => {
					// 是参数传递的目标场景，获取要定位的对象
					const itemModel = await this_.param.getModel(currentScene);
					if (itemModel){
						if (this_.param.sceneType === "2D"){
							await this_.getDocumentPreview(currentScene);
							this_.documentPreviewView.addModel(itemModel.docModel);
						}
						currentScene.state.set("currentModel", [itemModel]);
					} else if (this_.param.repositoryId && this_.param.documentId && this_.param.sceneType === "2D"){
						// 定位打开图纸
						const repoModels = currentScene.modelMap.RepoModel;
						const repoModel = repoModels.find(repoModel => repoModel.repositoryId
							=== this_.param.repositoryId);
						let docModel = await repoModel.requestDOCModel(this_.param.documentId);
						if (docModel){
							await this_.getDocumentPreview(currentScene);
							this.documentPreviewView.addModel(docModel);
						}
					}
				}, 0);
			}
		});
	}

	async getDocumentPreview(scene) {
		if (!this.documentPreviewView) {
			this.documentPreviewView = await new Promise((resolve, reject)=>{
				this.interVal = setInterval(()=>{
					if(this.documentPreviewView){
						clearInterval(this.interVal);
						resolve(this.documentPreviewView);
					}
					const views = scene.getViews();
					this.documentPreviewView = _.find(views, (view) => {
						return view.getType && view.getType() === "DocumentPreviewView";
					});
				}, 100);
			});
		}
		return this.documentPreviewView;
	}
}
export const urlParamListener = new UrlParamListener();