import {Api} from "../../api";

export default class Param{
	constructor(param) {
		this.tag = param.has("tag") ? param.get("tag") : null;
		this.sceneName = param.has("sceneName") ? param.get("sceneName") : null;
		this.repositoryId = param.has("repositoryId") ? param.get("repositoryId") : null;
		this.itemId = param.has("itemId") ? param.get("itemId") : null;
		this.propertyKey = param.has("propertyKey") ? param.get("propertyKey") : null;
		this.propertyValue = param.has("propertyValue") ? param.get("propertyValue") : null;
		// 3D or 2D
		this.sceneType = param.has("sceneType") ? param.get("sceneType") : null;
		this.documentId = param.has("documentId") ? param.get("documentId") : null;
		this.repositories = null;
	}

	/**
	 * 获取要定位的ItemModel或者RepoModel
	 * @param currentScene
	 */
	async getModel(currentScene){
		const repoModels = currentScene.modelMap.RepoModel;
		if (!(this.sceneType === "3D" || this.sceneType === "2D")){
			return null;
		}
		const index = await this.getIndex(repoModels);
		if (this.repositoryId && this.itemId){
			const itemModel = await this.createItemModel(repoModels, this.repositoryId, [this.itemId]);
			if (itemModel){
				return itemModel;
			}
		}else if (this.tag || (this.propertyKey && this.propertyValue)){
			const itemMap = await this.getEsItems(index, this.tag, this.propertyKey, this.propertyValue);
			for (let [key, value] of itemMap) {
				const itemModel = await this.createItemModel(repoModels, key, value);
				if (itemModel){
					return itemModel;
				}
			}
		}else if (this.repositoryId && this.sceneType === "3D"){
			const repoModel = repoModels.find(repoModel => repoModel.repositoryId === this.repositoryId);
			return repoModel;
		}else {
			console.log("输入URL参数错误");
		}
	}

	async createItemModel(repoModels, repositoryId, targetItemIds){
		const repoModel = repoModels.find(repoModel => repoModel.repositoryId === repositoryId);
		const result = await Api.getBulkReferences({
			repositoryId : repositoryId,
			targetItemIds : targetItemIds
		});
		for (let i = 0; i < result.data.length; i++) {
			const item = result.data[i];
			if (this.sceneType === "3D" && repoModel.psData.id === item.sourceDocumentId) {
				return await repoModel.requestItemModel(item.targetItemId);
			}
			if (this.sceneType === "2D"){
				const itemModel = await repoModel.requestItemModel(item.targetItemId);
				let docModel = await repoModel.requestDOCModel(item.sourceDocumentId);
				docModel.itemid = itemModel.itemId;
				itemModel.docModel = docModel;
				itemModel.documentId = item.sourceDocumentId;
				return itemModel;
			}
		}
		return null;
	}

	// 排除掉没有构建索引的对象
	async getIndex(repoModels) {
		if(!this.repositories){
			const result = await Api.getRepositories();
			this.repositories = result.data;
		}
		const repoIds = [];
		repoModels.forEach(repoModel => {
			const targetRepo = this.repositories.find(repo => repo.id === repoModel.repositoryId);
			const targetTasks = targetRepo.tasks.filter(task => task.type === "BUILD_INDEX_TASK"
				&& task.status === "SUCCESS");
			if (targetTasks && targetTasks.length > 0){
				repoIds.push(repoModel.repositoryId);
			}
		});
		return repoIds.map(repoId => repoId + "_item").join(",");
	}

	async getEsItems(index, tag, propertyKey, propertyValue){
		const propertySearch = "{\"must\":[{\"bool\":{\"should\":[{\"match_phrase\":{\"properties."
			+ propertyKey + ".value.keyword\":\"" + propertyValue + "\"}}]}}]}";
		const tagSearch = "{\"filter\": [{\"terms\": {\"tag.keyword\": [\"" + tag + "\"]}}]}";
		const params = {
			size : 9999,
			query : {
				bool : tag ? JSON.parse(tagSearch) : JSON.parse(propertySearch)
			},
			from : 0
		};
		const result = await Api.getEsDocuments(index, params);
		const hits = result && result.data && result.data.hits && result.data.hits.hits;
		const itemIdsMap = new Map();
		if (hits && hits.length > 0){
			hits.forEach(hit => {
				let ids = itemIdsMap.get(hit._index.split("_")[0]);
				if (ids){
					ids.push(hit._id);
				}else {
					ids = [hit._id];
				}
				itemIdsMap.set(hit._index.split("_")[0], ids);
			});
			return itemIdsMap;
		}else {
			return null;
		}
	}
}