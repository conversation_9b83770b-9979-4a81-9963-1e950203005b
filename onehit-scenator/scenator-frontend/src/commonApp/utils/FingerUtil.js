import { modelManager, sceneManager, extensionManager } from 'finger'
import ArrayUtil from './ArrayUtil'

export default class FingerUtil {
  static SCENES = {
    THREE_D: 'ThreeDModel',
    PID: 'PID',
    DRAWINGS: 'DigitalDrawings',
    QUESTION: 'ProjectQuestionFeedback',
    ENGINEERING: 'EngineeringProgress',
    DELIVERY: 'DeliveryProgress',
    DOWNLOAD: 'DownloadCenter',
    SOURCE_DATA: 'SourceData',
    QUALITY_PROBLEM_LIST: 'QualityProblemList'
  }

  /**
   * 创建RepoModels
   * @param repositoryIds
   * @returns {Promise<[]|[RepoModel]>}
   */
  static async createRepoModels (repositoryIds, version) {
    if (Array.isArray(repositoryIds) && repositoryIds.length > 0) {
      return modelManager.request('RepoModel', {
        version: version,
        filters: JSON.stringify([{
          type: 'IdsFilter',
          ids: repositoryIds
        }])
      })
    } else {
      return Promise.resolve([])
    }
  }

  /**
   * 创建itemModel
   * @param repositoryId
   * @param itemId
   * @returns {Promise<null|ItemModel>}
   */
  static async createItemModel (repositoryId, itemId, versionId = '') {
    if (repositoryId && itemId) {
      const repoModels = await FingerUtil.createRepoModels([repositoryId])
      const itemModel = ArrayUtil.hasValue(repoModels) ? repoModels[0].requestItemModel(itemId, versionId) : null
      return Promise.resolve(itemModel)
    } else {
      return Promise.resolve(null)
    }
  }

  /**
   * 创建docModel
   * @param repositoryId
   * @param docId
   * @returns {Promise<null|DocModel>}
   */
  static async createDocModel (repositoryId, docId, version) {
    if (repositoryId && docId) {
      const repoModels = await FingerUtil.createRepoModels([repositoryId], version)
      const docModel = ArrayUtil.hasValue(repoModels) ? repoModels[0].requestDOCModel(docId, '', version) : null
      return Promise.resolve(docModel)
    } else {
      return Promise.resolve(null)
    }
  }

  /**
   * 获取当前场景
   * @returns {scene}
   */
  static getCurrentScene () {
    return sceneManager.getCurrentScene()
  }

  /**
   * 根据场景名称获取场景
   * @param name
   * @returns {Promise<scene>}
   */
  static async getScene (name) {
    return sceneManager.request({ name })
  }

  /**
   * 根据场景名称跳转场景
   * @param name 需要跳转目标场景的名称
   * @param jumpTempData 向目标场景添加的临时数据
   * @returns {*}
   */
  static jumpScene (name, jumpTempData) {
    return sceneManager.jumpScene({ name, jumpTempData })
  }

  /**
   * 获取场景中的View
   * @param scene
   * @param viewType
   * @returns {null|view}
   */
  static getViewInScene (scene, viewType) {
    return scene && viewType ? scene.getViews().find(view => {
      return typeof view.getType === 'function' && view.getType() === viewType
    }) : null
  }

  /**
   * 获取currentModel
   * @returns {[model]}
   */
  static getCurrentModels () {
    return FingerUtil.getCurrentScene().state.get('currentModel')
  }

  /**
   * 获取currentModels中的第一个model
   * @param currentModels
   * @returns {{}|model}
   */
  static getModelByCurrentModels (currentModels) {
    return Array.isArray(currentModels) && currentModels[0] ? currentModels[0] : {}
  }

  /**
   * 设置currentModel
   * @param scene
   * @param models
   * @param options
   */
  static setCurrentModels (scene, models, options) {
    if (scene) {
      scene.state.set('currentModel', models, options)
    }
  }

  /**
   * watch current scene
   */
  static watchCurrentScene (callback) {
    if (typeof callback === 'function') {
      sceneManager.watchCurrentScene(callback)
    }
  }

  /**
   * 获取扩展点
   * @param pointName
   * @returns {null|extensionPoint}
   */
  static getExtensionPoint (pointName) {
    return pointName ? extensionManager.extensionPoints[pointName] : null
  }

  static reloadScenes (platform) {
    sceneManager.reloadScenes(platform)
  }
}
