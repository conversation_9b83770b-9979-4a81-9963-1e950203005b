export class UserViaSelectExtensionPoint {
  constructor () {
    this.extensions = []
  }

  getName () {
    return 'EDC.UserViaSelect.Option.ExtensionPoint'
  }

  hasExtension () {
    return this.extensions.length !== 0
  }

  getExtensionImplementResults () {
    if (this.extensions.length !== 0) {
      return this.extensions.flatMap(extension => extension.getImplements())
    } else {
      throw new Error('没有EDC.UserViaSelect.Option.ExtensionPoint扩展')
    }
  }

  addExtension (extension) {
    this.extensions.push(extension)
  }
}
