import { createAxios } from './axios'
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.uams)

export const userApi = {
  /**
   * 获取用户
   * @returns {Promise<AxiosResponse<any>>}
   */
  list () {
    return axios.get('/users').then(({ data }) => {
      return data
    })
  },
  /**
   * 是否开启短信登录
   * @returns {Promise<AxiosResponse<any>>}
   */
  enableSmsLoginApi () {
    return axios.get('/sms/enableSmsLogin')
  },
  /**
   * 修改密码
   * @param formData
   * @returns {Promise<AxiosResponse<any>>}
   */

  changePwd(formData) {
    const params = new URLSearchParams();
    params.append("userOldPwd", formData.userOldPwd);
    params.append("userNewPwd", formData.userNewPwd); 
    return axios.put('/users/changePwd', params, { headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' } })
  },
  /**
   * 获取key
   * @returns {Promise<AxiosResponse<any>>}
   */
  getKey () {
    return axios.get('/getKey')
  },
  /**
   * 获取密码等级
   * @returns {Promise<AxiosResponse<any>>}
   */
  getPasswordLevel () {
    return axios.get('/config/passwordLevel')
  },
  /**
   * 获取密码长度配置接口
   * @returns {Promise<AxiosResponse<any>>}
   */
  getPasswordLengthLimit () {
    return axios.get('/config/passwordLengthLimit')
  },
  /**
   * 获取用户名最大长度配置接口
   * @returns {Promise<AxiosResponse<any>>}
   */
  getUserNameLengthLimit () {
    return axios.get('/config/userNameMaxLength')
  }
}

export const groupApi = {
  list () {
    return axios.get('/groups').then(({ data }) => {
      return data
    })
  }
}

export const eventApi = {
  list (params) {
    return axios.get('/events', {
      params
    }).then(({ data }) => {
      return data
    })
  }
}

export const departmentsApi = {
  getAllDepartments: () => axios.get('/departments')
}

export const statisticalApi = {
  getLoginRat: (param) => {
    const formData = new FormData()
    formData.append('departmentIds', param.departmentIds)
    formData.append('startTime', param.startTime)
    formData.append('endTime', param.endTime)
    return axios.post('/statistical/login_rat', formData)
  }
}
