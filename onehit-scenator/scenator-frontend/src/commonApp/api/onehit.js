import { createAxios } from "./axios"
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.onehit)

export const clickLogApi = {
    addClickLog: (params) => axios.post(`/clickLog`, params),
    getVisitLog: (params) => axios.get('/clickLog', { params: params }),
    exportVisitLog: (params) => axios({
        url: '/clickLog/export',
        method: 'GET',
        params: params,
        responseType: 'blob'
    }),
    getVisitedPer: (params) => axios.get('/clickLog/statistics', { params: params }),
    updateClickLogApi: (id) => axios.post(`/clickLog/duration/${id}`)
}
