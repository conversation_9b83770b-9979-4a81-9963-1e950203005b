import {createAxios} from "./axios";
import qs from "qs";
import baseUrls from "./baseUrlConfig";

const axios = createAxios(baseUrls.contextPath)

export const Api= {

  //TODO:--------------------------------------------------UAMS
  getCurrentUser: () => axios.get('/UAMS/users/currentUser'),
  changePwd: (param) => axios.put('/UAMS/users/changePwd', param),
  getKey: () => axios.get('/UAMS/getKey'),
  getRepositories: () => axios.get(`/AIMS/repositories`),
  getBulkReferences: (param) => axios.post(`/AIMS/repositories/${param.repositoryId}/references/bulk`, qs.stringify({ targetItemIds: JSON.stringify(param.targetItemIds) })),
  getEsDocuments: (url, params) => axios.post(`/${url}/_search`, params),
  recordLog: (id) => axios.post(`/OneHit/plugin/log/${id}`),
}
