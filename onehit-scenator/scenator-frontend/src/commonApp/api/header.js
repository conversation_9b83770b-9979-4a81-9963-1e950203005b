import { createAxios } from './axios'
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.edc)
/**
 * 页面头部
 */

// 提取关联关系
export const extractRelationApi = {
  /**
   * 获取文档列表
   * @param projectSpaceId
   * @param taskId
   * @returns {Promise<AxiosResponse<any>>}
   */
  getDocList (projectSpaceId, taskId) {
    return axios.get(`/projectSpaces/${projectSpaceId}/tasks/ocr/docRelation/${taskId}/documents`, { cancelKey: 'cancelKey' })
  },
  /**
   * 更新文档关联结果的已读状态
   * @param projectSpaceId
   * @param taskId
   * @param taskContentId
   * @returns {Promise<AxiosResponse<any>>}
   */
  uploadPORelationReadStatus (projectSpaceId, taskId, taskContentId) {
    return axios.put(`/projectSpaces/${projectSpaceId}/tasks/ocr/docRelation/${taskId}/contents/${taskContentId}/readStatus`,
      { isFormData: false })
  },
  /**
   * 获取文档关联对象
   * @param projectSpaceId
   * @param taskId
   * @param taskContentId
   * @returns {Promise<AxiosResponse<any>>}
   */
  getPORelation (projectSpaceId, taskId, taskContentId) {
    return axios.get(`/projectSpaces/${projectSpaceId}/tasks/ocr/docRelation/${taskId}/contents/${taskContentId}/relationPOs`,
      { cancelKey: 'cancelKey' })
  },
  /**
   * 保存文档关联对象
   * @param projectSpaceId
   * @param taskId
   * @param data
   * @returns {Promise<AxiosResponse<any>>}
   */
  savePORelation (projectSpaceId, taskId, data) {
    return axios.post(`/projectSpaces/${projectSpaceId}/tasks/ocr/docRelation/${taskId}/relationPOs`, data, { isFormData: false })
  },
  /**
   * 获取关联对象位号下拉框数据
   * @param projectSpaceId
   * @param data
   * @returns {Promise<AxiosResponse<any>>}
   */
  getRelationPlantObject (projectSpaceId, data) {
    return axios.post(`/projectSpaces/${projectSpaceId}/plantObjects/legal`, data, { isFormData: false })
  },
  /**
   * 新增文档关联对象
   * @param projectSpaceId
   * @param taskId
   * @param taskContentId
   * @param data
   * @returns {Promise<AxiosResponse<any>>}
   */
  addPORelation (projectSpaceId, taskId, taskContentId, data) {
    return axios.put(`/projectSpaces/${projectSpaceId}/tasks/ocr/docRelation/${taskId}/contents/${taskContentId}/relationPOs`,
      data, { isFormData: false })
  },
  /**
   * 删除文档关联对象
   * @param projectSpaceId
   * @param taskId
   * @param taskContentId
   * @param data
   * @returns {Promise<AxiosResponse<any>>}
   */
  deletePORelation (projectSpaceId, taskId, taskContentId, data) {
    return axios.delete(`/projectSpaces/${projectSpaceId}/tasks/ocr/docRelation/${taskId}/contents/${taskContentId}/relationPOs`,
      { data }, { isFormData: false })
  }
}

// 用户平台权限
export const userAuthApi = {
  getUserPermission: () => axios.get('/user/auth')
}
