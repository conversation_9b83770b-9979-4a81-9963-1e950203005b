import Vue from "vue";
import SystemSetting from "@/components/systemSetting/SystemSetting.vue";

class HeadFunMenuItemExtension{
	constructor() {
		this.domId = Date.now().toString(36);
	}

	getPointName() {
		return "headFunNavMenuItem";
	}

	getImplements() {
		return {
			id: 'system-setting',
			index: 12,
			onset: ($element) => {
				const dom = $element[0]
				dom.innerHTML = `<div id='systemSetting_${this.domId}' class="systemSetting">
					<div id='systemSetting_vue${this.domId}'></div></div>`;
				new Vue({
					render: h => h(SystemSetting)
				}).$mount(`#systemSetting_vue${this.domId}`)
			}
		}
	}
}

export const headFunMenuItemExtension = new HeadFunMenuItemExtension();