import Vue from 'vue'
import Element from 'element-ui'
import index from './Index.vue'
import i18n from 'i18next'

Vue.config.productionTip = false

Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value)
})

export class HeaderUserExtension {
  getPointName () {
    return 'headFunNavMenuItem'
  }

  getImplements () {
    return {
      id: 'header-user',
      index: 99,
      onset: ($element) => {
        const dom = $element[0]
        dom.innerHTML = "<div id = '" + dom.id + "_extension'></div>"
        new Vue({
          i18n,
          render: h => h(index)
        }).$mount('#' + dom.id + '_extension')
      }
    }
  }
}
