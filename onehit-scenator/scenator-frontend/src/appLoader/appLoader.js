/* eslint-disable no-import-assign */
/* eslint-disable no-undef */

import * as finger from "finger";
import * as _ from "lodash";
import * as echarts from "echarts";
import * as axios from "axios";
import * as elementUi from "element-ui";
import Vue from "vue";
import VueI18n from "vue-i18n";
import VueRouter from "vue-router";
import Vuex from "vuex";
import i18next from "i18next";
import {recordLog} from "../services/server";
// 引入ztree和easyui的样式

import "@ztree/ztree_v3/js/jquery.ztree.all";
import "@ztree/ztree_v3/js/jquery.ztree.exhide.js";
import "@ztree/ztree_v3/css/zTreeStyle/zTreeStyle.css";
import "jquery-easyui/jquery.easyui.min";
import "jquery-easyui/themes/default/easyui.css";
require("jqgrid/jqgrid/js/i18n/grid.locale-cn");
require("jqgrid/jqgrid/js/jquery.jqGrid.min");
require("jqgrid/jqgrid/themes/ui.jqgrid.css");
require("jqgrid/jqgrid/themes/cupertino/jquery-ui.min.css");
require("ui-style");

const ONEHIT_CONTEXT_PATH = window.contextPathManager.getContextPath();
const baseUrl = ONEHIT_CONTEXT_PATH + "/Scenator/resources/apps";
const {funNavExtensionPoint} = require("../UI/navigationMenu/Model/funNavMenuItem.extensionPoint");
const {headFunExtensionPoint} = require("../UI/navigationMenu/Model/HeadFunMenuItem.extensionPoint");
const {commonApp} = require("../commonApp/main");
const navMenuApp = {
	extensionPoints : [funNavExtensionPoint, headFunExtensionPoint]
};
finger.registerApps([navMenuApp, commonApp]);
/**
 * 动态加载模块
 * @param {String} url 模块的在线访问地址
 * @param {String} moduleName 模块名称
 * @param {Function} callback 加载后的回调
 */
const dynamicLoadModule = async (url, moduleName, callback) => {
	try{
		const module = await SystemJS.import(url);
		if(moduleName){
			SystemJS.set(moduleName, SystemJS.newModule(module.default));
			// TODO 未生效
			SystemJS.delete(baseUrl + url);
		}
		if (_.isFunction(callback)) {
			callback(module.default);
		}
	}catch(error){
		recordLog("error", `${moduleName}:app加载失败,在apps无法找到${moduleName}`);
		console.error(i18next.t("error.appLoadFail"));
		throw new Error(error);
	};
};

/**
 * 动态生成link标签加载css
 * @param {String} url CSS访问地址
 */ 
function loadStyle(url){
	var link = document.createElement("link");
	link.type = "text/css";
	link.rel = "stylesheet";
	link.href = url;
	var head = document.getElementsByTagName("head")[0];
	head.appendChild(link);
}

/**
 * 异步引入APP
 * @param {array<app>} appList app列表
 * @param {Function} callback 引入app后的回调
 * @param {Number} [index=0] app在列表中的下标
 */
async function importAppsAsynchronously(appList, callback, index) {
	if (_.isEmpty(appList)) {
		if (_.isFunction(callback)) {
			callback();
		}
		return false;
	}
	if (!index) {
		index = 0;
	}
	for(const app of appList){
		try{
			dynamicLoadModule(baseUrl + app.JSURL, app.name, function (module){
				try{
					finger.registerApps([module]);
				}catch(error){
					recordLog("error", `在app: ${app.name}中出错,${error.message}`);
				}
				index++;
				// 判断当len为1时候循环完毕
				if(index === appList.length){
					callback();
				}
			});
		}catch(error){
			throw new Error(error);
		}
	};
}
/**
 * 同步引入APP
 * @param {array<app>} appList app列表
 * @param {Function} callback 引入app后的回调
 * @param {Number} [index=0] app在列表中的下标
 */
async function importAppsSynchronously(appList, callback, index) {
	if (_.isEmpty(appList)) {
		if (_.isFunction(callback)) {
			callback();
		}
		return false;
	}
	if (!index) {
		index = 0;
	}

	const app = appList[index];
	if (app.JSURL) {
		try{
			await dynamicLoadModule(baseUrl + app.JSURL, app.name, function (module){
				let registedMsg = null;
				// 注册APP
				try{
					registedMsg = finger.registerApps([module]);
				}catch(error){
					recordLog("error", `在app: ${app.name}中出错,${error.message}`);
				}
				index++;
				if (index <= appList.length - 1) {
					importAppsSynchronously(appList, callback, index);
				} else if (_.isFunction(callback)){
					// 记录最终还注册失败的extension
					// TODO 由finger的api抛出异常
					if (registedMsg && registedMsg !== true && !_.isEmpty(registedMsg.unRegisteredExtensions)) {
						_.map(registedMsg.unRegisteredExtensions, (extension) => {
							recordLog("error", `extensionPoint : ${extension.pointName} 未定义，相关的extension无法注册`);
						});
					}
					callback();
				}
			});
		}catch(error){
			throw new Error(error);
		}
	}
}

function initSystemJs() {
	// 基础配置-Scenator不做二次编译(es6-es5)
	SystemJS.config({
		meta : {
			format : "global",
			"*.js" : {
				babelOptions : {
					es2015 : false
				}
			}
		}
	});

	// 绕过SystemJs的model机制，直接注入原始的jQuery
	$[Symbol.toStringTag] = "module";
	axios[Symbol.toStringTag] = "module";
	elementUi[Symbol.toStringTag] = "module";
	Vue[Symbol.toStringTag] = "module";
	VueI18n[Symbol.toStringTag] = "module";
	VueRouter[Symbol.toStringTag] = "module";
	Vuex[Symbol.toStringTag] = "module";
	finger[Symbol.toStringTag] = "module";
	echarts[Symbol.toStringTag] = "module";
	SystemJS.set("jquery", $);
	SystemJS.set("lodash", SystemJS.newModule(_));
	SystemJS.set("finger", finger);
	SystemJS.set("echarts", echarts);
	SystemJS.set("vue", Vue);
	SystemJS.set("vue-i18n", VueI18n);
	SystemJS.set("vue-router", VueRouter);
	SystemJS.set("vuex", Vuex);
	SystemJS.set("axios", axios);
	SystemJS.set("element-ui", elementUi);
}

export {
	importAppsAsynchronously,
	importAppsSynchronously,
	initSystemJs,
	loadStyle
};