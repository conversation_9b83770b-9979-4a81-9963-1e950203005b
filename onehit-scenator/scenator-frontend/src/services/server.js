import $ from "jquery";
import _ from "lodash";
import SceneData from "../sceneNavigator/SceneData";
import i18next from "i18next";

const contextPath = window.contextPathManager.getContextPath();
function getUserName() {
	return $.ajax({
		type : "GET",
		url : contextPath + "/Scenator/username",
		cache : false,
		error : function (XMLHttpRequest){
			console.log(XMLHttpRequest.responseText);
		}
	});
};

function getCurrentUser(){
	return $.ajax({
		type : "GET",
		url : contextPath + "/UAMS/users/currentUser",
		cache : false,
		error : function (XMLHttpRequest){
			console.log(XMLHttpRequest.responseText);
		}
	});
}

function getUserId() {
	return $.ajax({
		type : "GET",
		url : contextPath + "/Scenator/userId",
		cache : false,
		error : function (XMLHttpRequest){
			console.log(XMLHttpRequest.responseText);
		}
	});
};

export function getSessionId() {
	return $.ajax({
		type : "GET",
		url : contextPath + "/Scenator/sessionId",
		cache : false,
		error : function (XMLHttpRequest){
			console.log(XMLHttpRequest.responseText);
		}
	});
};

// 获取用户id获取用户组
function getUserGroups(userId){
	return $.ajax({
		type : "GET",
		url : contextPath + `/UAMS/users/${userId}/groups`,
		cache : false,
		error : function (XMLHttpRequest){
			console.log(XMLHttpRequest.responseText);
		}
	});
};

// 根据用户id获取角色
function getUserRole(userId){
	return $.ajax({
		type : "GET",
		url : contextPath + `/UAMS/users/${userId}/roles`,
		cache : false,
		error : function (XMLHttpRequest){
			console.log(XMLHttpRequest.responseText);
		}
	});
};

// 通过角色id获取对应有权限的场景
function getScenePermissions(roleId){
	return $.ajax({
		type : "GET",
		url : contextPath + `/Scenator/permissions/scenes/${roleId}`,
		cache : false,
		success : function (data) {
			console.log("获取场景权限成功:", data);
		},
		error : function (XMLHttpRequest){
			console.log("获取场景权限错误：", XMLHttpRequest.responseText);
		}
	});
};

const getSwitchSystems = () => {
	let lang = localStorage.getItem("lang") ? localStorage.getItem("lang") : "zh_CN";
	return $.ajax({
		type : "GET",
		url : contextPath + "/systems?lang=" + lang
	});
};

function recordLog(logType, message){
	return $.ajax({
		type : "POST",
		url : contextPath + "/Scenator/log",
		data : {
			type : logType,
			message : message
		}
	});
}

const getUserStatus = () => {
	var promise = new $.Deferred();
	$.ajax({
		type : "GET",
		url : contextPath + "/Scenator/login/polling",
		cache : false,
		success : function (response) {
			promise.resolve(response);
		},
		error : function (response){
			promise.reject(response);
		}
	});
	return promise;
};

const saveScene = (scene) => {
	return $.ajax({
		type : "POST",
		url : contextPath + "/Scenator/saveUserScene",
		contentType : "application/json; charset=utf-8",
		data : JSON.stringify(scene)
	});
};

const updateScene = (scene) => {
	return $.ajax({
		type : "POST",
		url : contextPath + "/Scenator/updateUserScene",
		contentType : "application/json; charset=utf-8",
		data : JSON.stringify(scene)
	});
};

const deleteScene = (_sceneId) => {
	let promise = new $.Deferred();
	$.ajax({
		type : "POST",
		url : contextPath + `/Scenator/deleteScene?sceneId=${_sceneId}`,
		success : function (response) {
			promise.resolve(response);
		},
		error : function (response){
			promise.reject(response);
		}
	});
	return promise;
};

const resetDefaultScene = (scene) => {
	return $.ajax({
		type : "POST",
		url : contextPath + "/Scenator/resetDefaultScene",
		contentType : "application/json; charset=utf-8",
		data : JSON.stringify(scene)
	});
};

const resetSceneBaseData = (scenes) => {
	return $.ajax({
		type : "POST",
		url : contextPath + "/Scenator/resetSceneBaseData",
		contentType : "application/json; charset=utf-8",
		data : JSON.stringify(scenes)
	});
};

const getSceneStructureData = () => {
	return $.ajax({
		type : "GET",
		url : contextPath + "/Scenator/findSceneStructure",
		cache : false,
		// headers : {
		// 	"platform" : "OneHit-BusinessWorkbench"
		// },
		error : function (XMLHttpRequest){
			console.log(XMLHttpRequest.responseText);
		}
	});
};

const getCommonConfig = () => {
	let result = {};
	$.ajax({
		type : "GET",
		url : contextPath + "/Scenator/config/common",
		cache : false,
		async : false,
		contentType : "application/json; charset=utf-8",
		success : function (data) {
			result = data;
		}
	});
	return result;
};

const getAllConfig = () => {
	let result = {};
	$.ajax({
		type : "GET",
		url : contextPath + "/Scenator/app/config",
		cache : false,
		async : false,
		contentType : "application/json; charset=utf-8",
		success : function (data) {
			result = data;
		}
	});
	return result;
};


const getSceneDataByUserRoles = (userRoles) =>{
	var promise = new $.Deferred();
	$.ajax({
		type : "POST",
		url : contextPath + "/Scenator/userScenes",
		cache : false,
		contentType : "application/json; charset=utf-8",
		data : userRoles,
		success : function (data) {

			if (!_.isEmpty(data)) {
				let sceneDatas = [];
				_.map(data, (sceneData) => {
					sceneDatas.push(new SceneData(sceneData));
				});
				data = sceneDatas;
			} else {
				data = [];
			}
			promise.resolve(data);
		},
		error : function (response) {
			const message = JSON.parse(response.responseText);
			recordLog("warn", "获取用户场景：" + i18next.t(message.message));
			promise.reject(response);
		}
	});
	return promise;
};


const getTemplateBySceneName = (sceneName) => {
	return $.ajax({
		type : "GET",
		cache : false,
		url : encodeURI(contextPath + `/Scenator/resources/scenes/${sceneName}/scene.html`),
		error : function () {
			const msg = i18next.t("notFindTemplate", {sceneName : sceneName});
			recordLog("error", "获取场景模板：" + msg);
		}
	});
};

export {
	getSwitchSystems,
	getUserId,
	getUserName,
	getUserGroups,
	getUserRole,
	getScenePermissions,
	recordLog,
	getUserStatus,
	deleteScene,
	saveScene,
	updateScene,
	resetDefaultScene,
	resetSceneBaseData,
	getSceneStructureData,
	getCommonConfig,
	getAllConfig,
	getSceneDataByUserRoles,
	getTemplateBySceneName,
	getCurrentUser
};