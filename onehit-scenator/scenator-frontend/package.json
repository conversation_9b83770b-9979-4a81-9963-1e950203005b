{"name": "scenator", "version": "1.4.0", "description": "快速搭建Web应用的系统和二次开发平台", "main": "main.js", "scripts": {"build": "rimraf dist && npm run build-front && npm run build-back && npm run build-api", "build-contextPath": "rimraf dist && npm run build-front-contextPath && npm run build-back && npm run build-api ", "build-api": "npm run deleteApiDoc && jsdoc -c jsdoc.config.json -r -d APIDoc", "build-back": "webpack --mode production --config config/webpack.back.config.js", "build-front": "webpack --mode production --config config/webpack.front.config.js", "build-front-contextPath": "webpack --mode production --config config/webpack.front.contextpath.config.js", "build-user": "webpack --mode production --config config/webpack.userinfo.config.js", "build-innerSystem": "webpack --mode production --config config/webpack.innerSwitchSystem.config.js", "build-otherSystem": "webpack --mode production --config config/webpack.otherSwitchSystem.config.js", "deleteApiDoc": "rimraf APIDoc", "start-back": "babel-node src/nodeServer/APPServer.js", "start-dev": "webpack serve --mode development --config config/webpack.front.dev.config.js", "test": "jest --coverage", "test-dev": "jest --watchAll", "eslint-fix": "eslint --fix --ext .js,.jsx ."}, "author": "", "license": "ISC", "dependencies": {"@babel/polyfill": "^7.12.1", "@panter/vue-i18next": "^0.15.2", "@scenator/systemjs": "0.22.0", "@ztree/ztree_v3": "3.5.46", "amqplib": "^0.8.0", "axios": "^0.19.2", "body-parser": "^1.19.0", "buffer": "^6.0.3", "condicio": "^2.0.0", "echarts": "4.1.0", "element-ui": "2.15.6", "exceljs": "^4.2.1", "file-saver": "^2.0.5", "handlebars": "4.0.6", "i18n-extend": "0.3.1", "i18next": "^19.6.3", "i18next-xhr-backend": "^3.2.2", "jqgrid": "4.6.0", "jquery": "3.4.1", "jquery-easyui": "1.7.0", "jquery-form": "4.2.2", "jquery-minicolors": "2.2.4", "jquery.nicescroll": "3.7.6", "jsdoc": "^3.6.4", "lodash": "3.10.1", "log4js": "^4.0.2", "ui-style": "1.8.0", "uuid": "3.4.0", "vue": "2.6.11", "vue-axios": "^3.2.4", "vue-i18n": "8.20.0", "vue-router": "3.3.2", "vuex": "3.4.0", "web-socket-js": "0.1.0", "webuploader": "^0.1.8", "ws": "^8.6.0"}, "peerDependencies": {"finger": "2.1.0"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/node": "^7.12.6", "cross-env": "^7.0.3", "@babel/plugin-transform-arrow-functions": "^7.12.1", "@babel/plugin-transform-async-to-generator": "^7.12.1", "@babel/plugin-transform-modules-commonjs": "^7.12.1", "@babel/plugin-transform-template-literals": "^7.12.1", "@babel/preset-env": "^7.12.1", "@testing-library/jest-dom": "^5.11.4", "@webpack-cli/serve": "^1.1.0", "babel-jest": "^26.3.0", "babel-loader": "^8.1.0", "css-loader": "^5.0.0", "docdash": "^1.2.0", "eslint": "^7.11.0", "eslint-html-reporter": "^0.5.2", "eslint-loader": "^4.0.2", "express": "^4.17.1", "file-loader": "^6.1.1", "filemanager-webpack-plugin": "^2.0.5", "forever": "^3.0.4", "get-local-ip": "^0.1.1", "handlebars-loader": "^1.4.0", "html-webpack-plugin": "^4.5.0", "jest": "^26.6.0", "jest-css-modules": "^2.1.0", "jest-handlebars": "^1.0.1", "less": "^4.1.1", "less-loader": "^7.3.0", "mini-css-extract-plugin": "^1.1.1", "optimize-css-assets-webpack-plugin": "^5.0.4", "rimraf": "^3.0.2", "semver": "^5.7.1", "speed-measure-webpack-plugin": "^1.3.3", "style-loader": "^2.0.0", "uglifyjs-webpack-plugin": "1.3.0", "vue-loader": "^15.9.6", "vue-template-compiler": "2.6.11", "webpack": "^4.0.0", "webpack-cli": "^4.2.0", "webpack-dev-server": "^3.11.0"}, "engines": {"node": ">= 6.2.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}