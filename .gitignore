# Compiled class files
*.class

# Log files
*.log
*.log.*

# Package files
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Maven
target/
.mvn/
mvnw
mvnw.cmd

# IDE files
.idea/
*.iml
*.iws
.project
.classpath
.settings/
.history
.vscode/

# Frontend
node_modules/
dist/
.npm/
.yarn/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
tools/
# static/
# apps/
APIDoc/
src/main/resources/frontend/
logs
base-app/Renderer3D/package-lock.json
onehit-scenator/scenator-frontend/package-lock.json
onehit-scenator/scenator-frontend/package-lock.json
onehit-scenator/scenator-frontend/package-lock.json
