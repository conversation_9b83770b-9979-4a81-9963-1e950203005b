<template>
  <div class="home" ref="home">
    <div class="header">
      <div class="title">{{this.title}}</div>
    </div>
    <div class="main">
      <router-view/>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PluginHome',
  data () {
    return {
      title: this.$t('全部插件')
    }
  },
  mounted () {
    // this.resize()
    // const that = this
    // window.addEventListener('resize', () => that.resize())
    // const timer = setTimeout(() => {
    //   if (document.readyState === 'complete') {
    //     this.resize()
    //     clearTimeout(timer)
    //   }
    // }, 100);
  },
  updated() {
    // this.resize()
  },
  methods: {
    resize() {
      const clientHeight = document.documentElement.clientHeight
      const contentDom = document.querySelector(".content>div")
      if (contentDom) {
        // 48:content上下边距,48:header高度
        const needHeight = contentDom.offsetHeight + 48 + 48
        if (clientHeight > needHeight) {
          this.$refs.home.style.height = clientHeight + 'px'
        } else {
          this.$refs.home.style.height = needHeight + 'px'
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.home {
  min-width: 1040px;
  width: 100%;
  height: 100%;
  font-family: PingFangSC-Medium, PingFang SC, Microsoft YaHei;
  .header {
    width: 100%;
    height: 48px;
    background-color: #0A2440;
    padding: 14px 16px;
    box-sizing: border-box;
    color: #A9C1D8;
    display: flex;
    .title {
      height: 20px;
      line-height: 20px;
      font-size: 16px;
      font-weight: 400;
      position: sticky;
      left: 16px;
      z-index: 1000;
    }
  }
  .main {
    width: 100%;
    height: calc(100% - 48px);
  }
}
</style>