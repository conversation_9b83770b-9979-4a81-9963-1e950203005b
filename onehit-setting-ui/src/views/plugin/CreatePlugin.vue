<template>
  <div class="upload">
    <div class="form">
        <div>{{ $t('提交前请先部署好相应的场景') }}</div>
        <div>
            {{ $t('名称') }}:<el-input :placeholder="$t('请勿超过20字符')" v-model.trim="plugin.name"></el-input>
        </div>
        <el-divider></el-divider>
        <div>
            {{ $t('描述') }}:<el-input :placeholder="$t('请勿超过20字符')" v-model.trim="plugin.description"></el-input>
        </div>
        <el-divider></el-divider>
        <div>
            {{ $t('业务类型') }}:
            <el-radio-group v-model="plugin.businessType">
                <el-radio label="生产力">{{ $t('生产力') }}</el-radio>
                <el-radio label="可视化">{{ $t('可视化') }}</el-radio>
            </el-radio-group>
        </div>
        <el-divider></el-divider>
        <div>
            {{ $t('插件管理图标') }}：
            <el-upload ref="pmIcon" :limit="1" :auto-upload="false" accept="image/*" action="#" :on-change="pmIconBeforeUpload" :http-request="pmIconRequest">
                <el-button>{{ $t('选择图片') }}</el-button>
                <div slot="tip">{{ $t('上传图片需为38px') }}</div>
            </el-upload>
        </div>
        <el-divider></el-divider>
        <div>
            {{ $t('插件列表(Scenator弹窗)图标') }}：
            <el-upload ref="pdIcon" :limit="1" :auto-upload="false" accept="image/*" action="#" :on-change="pdIconBeforeUpload" :http-request="pdIconRequest">
                <el-button>{{ $t('选择图片') }}</el-button>
                <div slot="tip">{{ $t('上传图片需为16px') }}</div>
            </el-upload>
        </div>
        <el-divider></el-divider>
        <div>
            <el-button @click="submit" :disabled="isAllowSubmit">{{ $t('确定') }}</el-button>
        </div>
    </div>
  </div>
</template>

<script>
import {pluginApi} from "@/api/onehit";
export default {
    name: 'UploadPlugin',
    data () {
        return {
            plugin: {
                name: '',
                description: '',
                businessType: '生产力'
            },
            pmFile: null,
            pdFile: null,
            pmIconIsAllow: false,
            pdIconIsAllow: false
        }
    },
    computed: {
        isAllowSubmit() {
            let pluginPropertyFinish = true
            for (const key in this.plugin) {
                if (this.plugin[key] === '') {
                    pluginPropertyFinish = false
                }
            }
            return !this.pmIconIsAllow || !this.pdIconIsAllow || !pluginPropertyFinish
        }
    },
    methods: {
        pmIconBeforeUpload(file) {
            const index = file.raw.type.indexOf('image')
            this.pmIconIsAllow = index !== -1;
        },
        pdIconBeforeUpload(file) {
            const index = file.raw.type.indexOf('image')
            this.pdIconIsAllow = index !== -1;
        },
        pmIconRequest(param) {
            this.pmFile = param.file
        },
        pdIconRequest(param) {
            this.pdFile = param.file
        },
        submit() {
            this.$refs['pmIcon'].submit()
            this.$refs['pdIcon'].submit()
            const formData = new FormData()
            formData.append('name', this.plugin.name)
            formData.append('description', this.plugin.description)
            formData.append('businessType', this.plugin.businessType)
            formData.append('pmIconFile', this.pmFile)
            formData.append('pdIconFile', this.pdFile)
            pluginApi.insertPlugin(formData).then(res => {
                this.$message({
                    type: 'success',
                    message: this.$t('新增成功')
                })
                this.plugin.name = ''
                this.plugin.description = ''
                this.plugin.businessType = ''
                this.pmFile = null
                this.pdFile = null
                this.pmIconIsAllow = false
                this.pdIconIsAllow = false
                this.$refs['pmIcon'].clearFiles()
                this.$refs['pdIcon'].clearFiles()
            }).catch(error => {
                this.$message.error(this.$t('新增失败'))
            })
        }
    }
}
</script>

<style lang="less" scoped>
.upload {
    width: 100%;
    height: 100%;
    .form {
        width: 50%;
        height: 50%;
        margin: 0 auto;
    }
}
</style>
