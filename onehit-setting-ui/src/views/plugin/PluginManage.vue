<template>
  <div class="container scenatorStyle">
    <div class="aside">
      <div class="search">
        <el-input class="scenatorStyle" clearable prefix-icon="search-icon" :placeholder="this.inputPlaceHolder"
                  @focus="inputPlaceHolder = ''" 
                  v-model.trim="searchParam.pluginName" @clear="filterPlugin"
                  @keyup.enter.native="filterPlugin"></el-input>
      </div>
      <div class="type">
        <div class="label">{{ $t('类别') }}</div>
        <div class="scenatorStyle radio" v-for="(type, index) in this.types" :key="index">
          <el-radio class="scenatorStyle" v-model="searchParam.typeName" :label="type.value">{{ type.name }}</el-radio>
        </div>
      </div>
    </div>
    <div class="content" ref="content">
      <div ref="contentBox">
        <div class="option">
          <el-button type="secondary--button" class="scenatorStyle" :disabled="this.isDisabledOn"
                     @click="openDialog(true)">{{ $t('全部启用') }}
          </el-button>
          <el-button type="secondary--button" class="scenatorStyle" :disabled="this.isDisabledOff"
                     @click="openDialog(false)">{{ $t('全部停用') }}
          </el-button>
        </div>
        <div class="contentContainer">
          <div class="cardContainer" v-show="this.pluginData.length !== 0" ref="cardContainer">
            <Card :ref="'card' + index" v-for="(plugin, index) in this.pluginData" :searchText="searchText"
                  :key="plugin.id"
                  :plugin="plugin" @togglePluginStatus="togglePluginStatus" @menuClick="handleMenuClick"></Card>
          </div>
          <Empty v-if="!selectLoading" v-show="this.pluginData.length === 0" :text="$t('暂无插件，敬请期待')"
                 :imgSrc="searchEmptyImg" imgClass="emptyImg"></Empty>
        </div>
      </div>
      <Loading class="loading" v-if="selectLoading" :text="selectLoadingText"></Loading>
      <Loading class="loading" v-if="updateLoading" :text="updateLoadingText"></Loading>
    </div>
    <el-dialog :visible.sync="isShowDialog" width="480px" top="0" custom-class="selfDialog" :show-close="false">
      <div slot="title">
        <div class="icon onehit-icon onehit-icon-submitIcon"></div>
        <div class="title">{{ $t('confirmTips') }}</div>
      </div>
      <div class="body">{{ dialog.text }}</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="secondary--button" class="scenatorStyle" @click="closeDialog">{{ $t('取消') }}</el-button>
        <el-button type="main--button" class="scenatorStyle" @click="submit">{{ $t('确定') }}</el-button>
      </div>
    </el-dialog>
    <EditInfo ref="editInfo" @updatePlugin="handleUpdatePlugin"></EditInfo>
  </div>
</template>
<script>
import Empty from '@/components/common/Empty.vue';
import Loading from '@/components/common/Loading.vue';
import Card from '@/components/plugin/Card.vue';
import EditInfo from "@/components/plugin/EditInfo";
import {userApi} from '@/api/uams';
import {pluginApi} from "@/api/onehit";

export default {
  name: 'PluginManage',
  components: {
    Empty,
    Loading,
    Card,
    EditInfo
  },
  data() {
    return {
      searchParam: {
        pluginName: '',
        typeName: '全部'
      },
      searchText: '',
      inputPlaceHolder: this.$t('请输入插件名称'),
      types: [
        {
          value: '全部',
          name: this.$t('全部')
        },
        {
          value: '生产力',
          name: this.$t('生产力')
        },
        {
          value: '可视化',
          name: this.$t('可视化')
        },
        {
          value: '自定义',
          name: this.$t('自定义')
        }
      ],
      pluginData: [],
      plugins: [],
      updateLoading: false,
      selectLoading: false,
      isUpdateIng: false,
      updateLoadingText: '',
      selectLoadingText: '',
      isShowDialog: false,
      searchEmptyImg: require('@/assets/pluginManage/searchEmpty.svg'),
      broadcastChannel: null,
      currentUser: null,
      dialog: {
        type: '',
        text: ''
      }
    }
  },
  computed: {
    isDisabledOn() {
      const disabledPluginStatus = ['OFF']
      const disabledPlugins = this.pluginData.filter(plugin => disabledPluginStatus.includes(plugin?.status))
      return disabledPlugins.length === 0 || this.pluginData?.length === 0 || this.isUpdateIng
    },
    isDisabledOff() {
      const enablePluginStatus = ['ON', 'FIXED']
      const enablePlugins = this.pluginData.filter(plugin => enablePluginStatus.includes(plugin?.status))
      return enablePlugins.length === 0 || this.pluginData.length === 0 || this.isUpdateIng
    }
  },
  watch: {
    'searchParam.typeName'(newVal) {
      console.log(newVal)
      this.filterPlugin()
    },
    'searchParam.pluginName'(newVal) {
      if (newVal === '') {
        this.filterPlugin()
      }
    }
  },
  created() {
    this.initWatchEvent()
    this.getCurrentUser()
    this.initBroadcastChannel()
  },
  async mounted() {
    this.selectLoadingText = this.$t('加载中')
    this.selectLoading = true
    await this.getPlugins()
    this.resizeCardContainer()
    const timer = setTimeout(() => {
      if (document.readyState === 'complete') {
        this.resizeCardContainer()
        clearTimeout(timer)
      }
    }, 100);
    this.selectLoadingText = ''
    this.selectLoading = false
  },
  updated() {
    this.resizeCardContainer()
  },
  methods: {
    initWatchEvent() {
      const that = this
      window.addEventListener('resize', () => that.resizeCardContainer())
    },
    resizeCardContainer() {
      const content = this.$refs.content
      const contentBox = this.$refs.contentBox
      // 48:content区域左右两侧边距
      const contentBoxWidth = content.offsetWidth - 48
      const cardNum = contentBoxWidth / 376
      let cardNeedTotalWidth = 0
      let num = Math.floor(cardNum)
      if (num <= 2) {
        num = 2
        cardNeedTotalWidth = 376 * 2 + 24
      } else {
        cardNeedTotalWidth = num * 376 + (num - 1) * 24
      }
      // 最小宽度1280，num最小值2
      if (num <= 2) {
        contentBox.style.width = cardNeedTotalWidth + 'px'
      } else if (cardNeedTotalWidth > contentBoxWidth) {
        num = num - 1
        const cardTotalWidth = num * 376 + (num - 1) * 24
        contentBox.style.width = cardTotalWidth + 'px'
      } else {
        contentBox.style.width = cardNeedTotalWidth + 'px'
      }
      if (this.pluginData.length !== 0) {
        for (let i = 0; i < this.pluginData.length; i++) {
          const card = this.$refs['card' + i]
          if ((i + 1) % num !== 0) {
            card[0].$el.style.marginRight = '24px'
          } else {
            card[0].$el.style.marginRight = '0'
          }
        }
      }
      // if (this.$refs.cardContainer) {
      //     // 48:header区
      //     const contentHeight = document.documentElement.clientHeight - 48
      //     console.log(contentHeight)
      //     // 104:按钮区32+边距24+上内填充24+下内填充24
      //     const needHeight = this.$refs.cardContainer.offsetHeight + 104
      //     if (needHeight > contentHeight) {
      //         this.$refs.content.style.height = needHeight + 'px'
      //     } else {
      //         this.$refs.content.style.height = contentHeight + 'px'
      //     }
      // }
    },
    initBroadcastChannel() {
      this.broadcastChannel = new BroadcastChannel('OneHit-Plugin')
      const that = this
      this.broadcastChannel.onmessage = async (event) => {
        const data = event.data
        if (data.active === 'CREATE' && data.username === that.currentUser.username) {
          that.getPlugins()
          that.clearFilterCondition()
        }
      }
      this.broadcastChannel.onmessageerror = (event) => {
        console.error(event, 'BroadcastChannel消息消费出错了')
      }
      window.addEventListener('unload', () => {
        console.log('页签即将关闭')
        that.broadcastChannel.close()
      })
    },
    getCurrentUser() {
      userApi.getCurrentUser().then(res => {
        this.currentUser = res.data
      })
    },
    async getPlugins() {
      const param = {
        isIgnoreOff: false
      }
      await pluginApi.getPlugins(param).then(res => {
        this.plugins = []
        this.pluginData = []
        this.plugins = res?.data?.result
        this.pluginData = res?.data?.result
      })
    },
    togglePluginStatus(param) {
      console.log(param)
      const plugins = [param?.plugin]
      if (param?.status) {
        this.batchUpdatePluginStatus('ON', plugins)
      } else {
        this.batchUpdatePluginStatus('OFF', plugins)
      }
    },
    batchUpdatePluginStatus(status, plugins, isBatch) {
      const that = this
      if (plugins?.length !== 0) {
        plugins.forEach(p => p.status = status)
        const ids = plugins.map(p => p.id)
        const param = {
          status: status,
          ids: ids
        }
        this.isUpdateIng = true
        if (isBatch) {
          if (status === 'ON') {
            this.updateLoadingText = this.$t('全部启用中，请稍后')
          } else {
            this.updateLoadingText = this.$t('全部停用中，请稍后')
          }
          this.updateLoading = true
        }
        pluginApi.updatePluginStatus(param).then(res => {
          console.log(res, '保存成功')
          const message = {
            username: this.currentUser?.username,
            active: status,
            plugins: plugins
          }
          this.broadcastChannel.postMessage(message)
          setTimeout(async function () {
            await that.getPlugins()
            await that.filterPlugin()
          })
        }).catch(error => {
          if (error?.response?.data?.message.includes('未找到指定插件')) {
            this.$message({
              dangerouslyUseHTMLString: true,
              duration: 3000,
              customClass: 'scenatorStyle scenator_complexMsg warn',
              message: '<div><article>' + this.$t('此插件不存在') + '</article></div>',
            });
            setTimeout(async function () {
              await that.getPlugins()
              await that.filterPlugin()
            })

          } else {
            let message = ''
            if (status === 'ON') {
              message = this.$t('启用失败')
            } else {
              message = this.$t('停用失败')
            }
            this.$message({
              dangerouslyUseHTMLString: true,
              duration: 3000,
              customClass: 'scenatorStyle scenator_complexMsg error',
              message: `<div><article>${message}</article></div>`,
            });
          }
          console.error(error, '保存失败')
        }).finally(() => {
          if (this.updateLoading) {
            this.updateLoading = false
            this.updateLoadingText = ''
          }
          this.isUpdateIng = false
        })
      }
    },
    clearFilterCondition() {
      for (const key in this.searchParam) {
        if (key === 'typeName') {
          this.searchParam[key] = '全部'
        } else {
          this.searchParam[key] = ''
        }
      }
      this.searchText = ''
    },
    async filterPlugin() {
      this.searchText = this.searchParam.pluginName
      if (this.searchParam.typeName === '全部') {
        if (this.searchParam.pluginName !== '') {
          this.pluginData = this.filterPluginByName(this.plugins)
        } else {
          this.pluginData = this.plugins
        }
      } else {
        let temp = this.plugins.filter(plugin => plugin?.businessType === this.searchParam.typeName)
        if (this.searchParam.pluginName !== '') {
          this.pluginData = this.filterPluginByName(temp)
        } else {
          this.pluginData = temp
        }
      }
    },
    filterPluginByName(plugins) {
      return plugins.filter(plugin => {
        const reg = new RegExp(this.searchParam.pluginName, 'gi')
        const matchResult = reg.exec(plugin.name)
        return matchResult !== null;
      })
    },
    openDialog(isOn) {
      this.isShowDialog = true
      if (isOn) {
        this.dialog.type = 'ON'
        this.dialog.text = this.$t('是否启用当前页面内所有插件')
      } else {
        this.dialog.type = 'OFF'
        this.dialog.text = this.$t('是否停用当前页面内所有插件')
      }
    },
    closeDialog() {
      this.isShowDialog = false
      this.dialog.type = ''
      this.dialog.text = ''
    },
    submit() {
      this.isShowDialog = false
      if (this.dialog.type === 'ON') {
        const disabledPluginStatus = ['OFF']
        const disabledPlugins = this.pluginData.filter(plugin => disabledPluginStatus.includes(plugin?.status))
        this.batchUpdatePluginStatus(this.dialog.type, disabledPlugins, true)
      } else {
        const enablePluginStatus = ['ON', 'FIXED']
        const enablePlugins = this.pluginData.filter(plugin => enablePluginStatus.includes(plugin?.status))
        this.batchUpdatePluginStatus(this.dialog.type, enablePlugins, true)
      }
    },
    handleMenuClick(params) {
      const that = this
      if (params.command === 'DELETE') {
        this.$msgbox({
          title: this.$t('删除插件'),
          customClass: "scenator_confirmPrompt error",
          closeOnClickModal: false,
          closeOnPressEscape: false,
          showClose: false,
          message: this.$t('删除插件MSG'),
          showConfirmButton: true,
          confirmButtonText: this.$t('删除'),
          showCancelButton: true,
          cancelButtonText: this.$t('取消'),
          confirmButtonClass: 'el-button el-button--main--button',
          cancelButtonClass: 'el-button el-button--secondary--button'
        }).then(() => {
          const message = {
            active: 'DELETE',
            username: this.currentUser.username,
            plugins: [params.plugin]
          }
          pluginApi.deletePlugin(params.plugin.id).then(res => {
            this.$message({
              dangerouslyUseHTMLString: true,
              duration: 3000,
              customClass: 'scenatorStyle scenator_briefMsg success',
              message: '<div><article>' + this.$t('插件删除成功') + '</article></div>',
            });
            this.broadcastChannel.postMessage(message)
            setTimeout(async function () {
              await that.getPlugins()
              await that.filterPlugin()
            })
          }).catch(error => {
            if (error?.response?.data?.message.includes('未找到指定插件')) {
              this.$message({
                dangerouslyUseHTMLString: true,
                duration: 3000,
                customClass: 'scenatorStyle scenator_complexMsg warn',
                message: '<div><article>' + this.$t('此插件不存在') + '</article></div>',
              });
              setTimeout(async function () {
                await that.getPlugins()
                await that.filterPlugin()
              })
            } else {
              this.$message({
                dangerouslyUseHTMLString: true,
                duration: 3000,
                customClass: 'scenatorStyle scenator_complexMsg error',
                message: '<div><article>' + this.$t('插件删除失败') + '</article></div>',
              });
            }
          })
        }).catch(error => {
          console.log(error, '取消')
        });
      } else if (params.command === 'EDIT') {
        this.$refs.editInfo.currentPlugin = params.plugin
        this.$refs.editInfo.errorMsg = {}
        this.$refs.editInfo.isClearIcon = false
        this.$refs.editInfo.initSelectedIcon()
        this.$refs.editInfo.isShowInfoDialog = true
      }
    },
    handleUpdatePlugin(param) {
      const that = this
      const message = {
        active: 'EDIT',
        username: this.currentUser.username,
        prePlugin: param.prePlugin,
        nextPlugin: param.nextPlugin
      }
      this.broadcastChannel.postMessage(message)
      setTimeout(function () {
        that.getPlugins()
      })
      this.filterPlugin()
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  height: 100%;
  display: flex;
  box-sizing: border-box;

  .aside {
    width: 216px;
    height: 100%;
    background-color: #FFFFFF;
    padding: 24px 16px;
    box-sizing: border-box;
    border-right: 1px solid #D9D9D9;
    position: sticky;
    left: 0;
    z-index: 1000;

    .search {
      margin-bottom: 24px;

      /deep/ .el-input__inner {
        padding: 0 15px;
        padding-left: 30px;
        height: 32px;
        line-height: 32px;
        border: 1px solid #89919A;
      }

      /deep/ input::placeholder {
        color: #94999D;
      }

      /deep/ .el-input__inner:focus {
        border: 1px solid #0854A1;
      }

      /deep/ .el-input__inner::placeholder {
        color: #94999D;
      }

      /deep/ .el-input__prefix {
        left: 0;
      }

      /deep/ .el-input__icon {
        line-height: 32px;
      }

      /deep/ .search-icon {
        display: inline-block;
        width: 32px;
        height: 32px;
        font-family: "onehit-icon" !important;
        content: "\e61c" !important;
        color: #0854a1;
      }

      /deep/ .el-input__suffix {
        right: 0;
        height: 30px;
        display: inline-block;
      }

      /deep/ .el-icon-close:before {
        font-family: "onehit-icon" !important;
        content: "\e615";
        color: #0854a1;
      }
    }

    .type {
      margin-left: 8px;
    }

    .label {
      height: 20px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #182A4E;
      margin-bottom: 16px;
    }

    .radio {
      /deep/ .el-radio {
        margin-bottom: 16px;
      }

      /deep/ .el-radio__inner::after {
        width: 8px;
        height: 8px;
      }
    }
  }

  .content {
    width: calc(100% - 216px);
    height: 100%;
    background-color: #F7F7F7;
    box-sizing: border-box;
    padding: 24px 24px;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;

    & > div {
      width: 100%;
      height: 100%;
      margin: 0 auto;
      box-sizing: border-box;
    }

    .option {
      margin-bottom: 24px;

      .el-button--secondary--button {
        font-weight: 400;
        font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei;
      }
    }

    .contentContainer {
      width: 100%;
      height: calc(100% - 80px);
      margin: 0 auto;

      .cardContainer {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
}

/deep/ .selfDialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/deep/ .selfDialog .el-dialog__header {
  padding: 10px 16px 12px;
  height: 44px;
  box-sizing: border-box;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.15), inset 0 -1px 0 0 #0A6ED1;
  border-radius: 4px 4px 0 0;

  & > div {
    height: 22px;
    line-height: 22px;
    display: flex;
  }

  .icon {
    width: 16px;
    height: 22px;
    color: #0b6fd1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 3px;
    padding-bottom: 3px;
    margin-right: 8px;
    box-sizing: border-box;
  }

  .title {
    height: 22px;
    line-height: 22px;
    font-size: 16px;
    font-weight: 400;
    color: #182A4E;
  }
}

/deep/ .selfDialog .el-dialog__body {
  padding: 24px 16px;

  .body {
    font-size: 14px;
    font-weight: 400;
    color: #182A4E;
    height: 20px;
    line-height: 20px;
  }
}

/deep/ .selfDialog .el-dialog__footer {
  height: 56px;
  padding: 12px 16px;
  box-sizing: border-box;
  box-shadow: inset 0 1px 0 0 #D9D9D9;
  border-radius: 0 0 4px 4px;

  /deep/ .el-button--secondary--button {
    font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei;
    font-weight: 400;
  }

  /deep/ .el-button--main--button {
    font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei;
    font-weight: 400;
  }
}

.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-track {
  background-color: #F7F7F7;
}

.content::-webkit-scrollbar-thumb {
  background-color: #B8BDC5;
}

/deep/ .empty {
  img {
    width: 260px;
    height: 190px;
    margin-bottom: 8px;
  }
}

///deep/ .el-button {
//    height: 32px;
//    border-radius: 2px;
//    padding: 6px 16px;
//    border: 1px solid #0854A0;
//}
///deep/ .el-button--primary {
//    background-color: #0854A1;
//}
///deep/ .el-button--primary span {
//    color: #FFFFFF;
//}
///deep/ .el-button--default span {
//    height: 20px;
//    line-height: 20px;
//    color: #0854A0;
//}
</style>
