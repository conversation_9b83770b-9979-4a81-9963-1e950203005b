/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-01-21 16:49:26
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-08-26 11:23:31
 */
// 导入axios
import axios from "axios";
import { Message } from "element-ui";

// 公共路由(网络请求地址)
// axios.defaults.baseURL = "http://************:9999/";
// 请求响应超时时间
axios.defaults.timeout = 15000;

const httpService = axios.create({
	// url前缀-'https://*************/api/'
	baseURL: "/", // 需自定义
	// 请求超时时间
	// timeout: 5000, // 需自定义
});

// 添加请求拦截器
httpService.interceptors.request.use(
	config => {
		// 判断请求的 URL
		if (config.url.includes('UAMS') || config.url.includes('Scenator')) {
			// 如果是不想添加请求头的请求，可以删除请求头
			delete config.baseURL;
		}
		return config;
	},
	error => {
		return Promise.reject(error);
	}
);

// document.cookie = "FULONGTECH_SESSION=50a7715e-a846-4837-b3e0-0264176eaf3a";
// request拦截器
httpService.interceptors.request.use(
	(config) => {
		return config;
	},
	(error) => {
		// 请求错误处理
		Promise.reject(error);
	}
);

// respone拦截器
// httpService.interceptors.response.use(
// 	(response) => {
// 		// 统一处理状态, 根据实际后端框架返回模板来设置
// 		return response;
// 	},
// 	// 处理处理
// 	(error) => {
// 		if (error && error.response) {
// 			switch (error.response.status) {
// 				case 400:
// 					error.message = "错误请求";
// 					break;
// 				case 401:
// 					error.message = "未授权，请重新登录";
// 					break;
// 				case 403:
// 					error.message = "拒绝访问";
// 					break;
// 				case 405:
// 					error.message = "请求方法未允许";
// 					break;
// 				case 408:
// 					error.message = "请求超时";
// 					break;
// 				case 500:
// 					error.message = "服务器端出错";
// 					break;
// 				case 501:
// 					error.message = "网络未实现";
// 					break;
// 				case 502:
// 					error.message = "网络错误";
// 					break;
// 				case 503:
// 					error.message = "服务不可用";
// 					break;
// 				case 504:
// 					error.message = "网络超时";
// 					break;
// 				case 505:
// 					error.message = "http版本不支持该请求";
// 					break;
// 				default:
// 					error.message = `未知错误${error.response.status}`;
// 			}
// 		} else {
// 			error.message = "连接到服务器失败";
// 		}
//
// 		MessageUtil({
// 			type: "error",
// 			message: error.message,
// 		});
// 		return Promise.reject(error);
// 	}
// );

// 封装自己的get/post方法
export default {
	get: function (path = "", data = {}, config = {}) {
		return new Promise((resolve, reject) => {
			httpService({
				url: path,
				method: "GET",
				params: data,
				...config,
			})
				.then((response) => {
					// 按需求来，这里我需要的是response，所以返回response，一般直接返回response
					resolve(response);
				})
				.catch((error) => {
					reject(error);
				});
		});
	},
	post: function (path = "", data = {}, config = {}) {
		return new Promise((resolve, reject) => {
			httpService({
				url: path,
				method: "post",
				data: data,
				...config,
			})
				.then((response) => {
					resolve(response);
				})
				.catch((error) => {
					reject(error);
				});
		});
	},
	put: function (path = "", data = {}) {
		return new Promise(function (resolve, reject) {
			httpService({
				url: path,
				method: "put",
				data: data,
			})
				.then(function (response) {
					resolve(response);
				})
				.catch(function (error) {
					reject(error);
				});
		});
	},
	delete: function (path = "", data = {}) {
		return new Promise((resolve, reject) => {
			httpService({
				url: path,
				method: "delete",
				data: data,
			})
				.then((response) => {
					resolve(response);
				})
				.catch((error) => {
					reject(error);
				});
		});
	},
};
