export default class ArrayUtil {
    /**
     * 用户数组排序
     * @param array
     * @param prop 按照哪个属性排序
     * @returns {boolean}
     */
    static userSort (array, prop) {
        return ArrayUtil.sortArray(array, prop,"name", (item) => {
            // 将 admin 排在最前面
            if (item === 'admin' || item === '超级管理员') {
                return -1;
            }
            return 0;
        });
    }

    /**
     * 角色数组排序
     * @param array
     * @param prop 按照哪个属性排序
     * @returns {boolean}
     */
    static roleSort (array, prop) {
        const frontRowsValues = ['BEFE2FF1A20F4C419C8A94B7213C5221', 'BEFE2FF1A20F4C419C8A94B7213C5220'];
        return ArrayUtil.sortArray(array, prop, "id", (item) => {
            if (frontRowsValues.includes(item)) {
                return -1;
            }
            return 0;
        });
    }

    /**
     * 通用数组排序方法
     * @param array
     * @param prop 按照哪个属性排序
     * @param customSortFn 自定义排序逻辑的回调函数
     * @returns {boolean}
     */
    static sortArray(array, prop, prop2, customSortFn) {
        // 类型排序顺序
        const typeOrder = ['symbol', 'number', 'english', 'chinese', 'other'];

        // 根据字符判断类型
        function getType(value) {
            if (/^[!@#$%^&*()]/.test(value)) {
                return 'symbol';
            }
            if (/^\d/.test(value)) {
                return 'number';
            }
            if (/^[a-zA-Z]/.test(value)) {
                return 'english';
            }
            if (/^[\u4e00-\u9fa5]/.test(value)) {
                return 'chinese';
            }
            return 'other';
        }

        array.sort((a, b) => {
            // 执行自定义排序逻辑
            if(customSortFn) {
                const customSortResult = customSortFn(a[prop2]) - customSortFn(b[prop2]);
                if (customSortResult !== 0) {
                    return customSortResult;
                }
            }
            // 按照符号、数字、英文、中文的顺序进行排序
            const aType = getType(a[prop]);
            const bType = getType(b[prop]);
            if (aType !== bType) {
                return typeOrder.indexOf(aType) - typeOrder.indexOf(bType);
            }

            // 如果类型相同，则按照首字母进行排序
            return a[prop].localeCompare(b[prop]);
        });
        return array;
    }
}
