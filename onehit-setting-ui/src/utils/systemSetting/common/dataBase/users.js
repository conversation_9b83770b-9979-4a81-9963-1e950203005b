import ArrayUtil from '@/utils/systemSetting/common/ArrayUtil';
/*
 * @Descriptin: 用于处理用户数据的前端模拟库
 */
export default class UsersBase {
	constructor() {}

	/**
	 * @descripton: 根据分页等查询条件获取用户数据
	 * @return {*}
	 * @author: Gary
	 */
	getUsersByParams(allUser,{ pageNo = 1, pageSize = 20, department = '', role = '', userName = '' }) {
		return new Promise(async function (resolve, reject) {
			try {
				let params = {};
				let filterData = [...ArrayUtil.userSort(allUser, "username")];
				department && (params['department'] = department);
				role && (params['role'] = role);
				userName && (params['userName'] = userName);
				if (Object.keys(params).length > 0) {
					if (params['department']) {
						filterData = filterData.filter((user) => {
							let deps = user.departments
								? user.departments.find((dep) => {
										if (dep.id === params['department']) {
											return dep;
										}
									})
								: null;
							if (deps) {
								return user;
							}
						});
					}

					if (params['role']) {
						filterData = filterData.filter((user) => {
							let ros = user.roles
								? user.roles.find((ro) => {
										if (ro.id === params['role']) {
											return ro;
										}
									})
								: null;
							if (ros) {
								return user;
							}
						});
					}

					if (params['userName']) {
						filterData = filterData.filter((user) => {
							if (
								user.name.indexOf(params['userName']) >= 0 ||
								user.username.indexOf(params['userName']) >= 0
							) {
								return user;
							}
						});
					}
				}
				let result = [];
				let total = 0;
				total = filterData.length;
				result = filterData.splice((pageNo - 1) * pageSize, pageSize);
				resolve({
					data: result,
					total: total,
				});
			} catch (error) {
				reject({});
			}
		});
	}
}
