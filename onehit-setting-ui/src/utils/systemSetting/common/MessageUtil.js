export default class MessageUtil {

    showSuccessMessage(vue, message) {
        vue.$message({
            dangerouslyUseHTMLString: true,
            duration: 3000,
            customClass: 'scenatorStyle scenator_briefMsg success',
            message: `<div><article>${message}</article></div>`,
        });
    }

    showErrorMessage(vue, message) {
        vue.$message({
            showClose: true,
            dangerouslyUseHTMLString: true,
            duration: 3000,
            iconClass: '',
            customClass: 'scenatorStyle scenator_complexMsg error',
            message: `<div><article>${message}</article></div>`,
        });
    }
}