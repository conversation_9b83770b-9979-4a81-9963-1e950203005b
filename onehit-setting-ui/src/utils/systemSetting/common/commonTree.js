/*
 * @Descriptin: 扁平数组转树结构
 */
export default class CommonTree {
	constructor({ data = [], id = 'id', parentId = 'parentId' }) {
		// 原数据
		this.originData = data;
		// 节点id字段标识(默认为‘id’)
		this.id = id;
		// 父节点字段标识(默认为‘parentId’)
		this.parentId = parentId;

		this.treeNodes = [];

		// 如果源数据不为空则初始化目录树结构
		if (data && data.length > 0) {
			this.treeNodes = this.initTreeNodes(data);
		}
	}

	/**
	 * @descripton: 将源数据初始化为目录树结构
	 * @return [treeNode]
	 */
	initTreeNodes(originData) {
		const idToNodeMap = {};
		originData.forEach(item => {
			item.treeLeval = 1;
			idToNodeMap[item.id] = item;
		});

		const rootNodes = [];

		originData.forEach(item => {
			if (item.parentId !== null) {
				const parentNode = idToNodeMap[item.parentId];
				if (parentNode) {
					item.treeLeval = parentNode.treeLeval + 1;
					if (!parentNode.children) {
						parentNode.children = [];
					}
					parentNode.children.push(item);
				}
			} else {
				rootNodes.push(item); // 如果没有parentId，则将节点添加到根节点列表
			}
		});

		// 遍历构建hierarchy属性
		function buildHierarchy(node, parentHierarchy = []) {
			node.hierarchy = [...parentHierarchy, node.name];
			if (node.children) {
				node.children.forEach(child => {
					buildHierarchy(child, node.hierarchy);
				});
			}
		}

		rootNodes.forEach(rootNode => {
			buildHierarchy(rootNode);
		});

		return rootNodes;
	}

	/**
	 * @descripton: 获取目录树结构数据
	 * @return {*}
	 * @author: Gary
	 */
	getTreeNodes() {
		return this.treeNodes;
	}
	getOriginData() {
		return this.originData;
	}
}
