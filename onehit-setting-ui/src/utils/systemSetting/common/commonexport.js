/*
 * @Descriptin:
 * @Autor: <PERSON>
 * @Date: 2022-11-29 20:27:18
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-11-30 18:58:05
 */
/*
header为表头，值为实际表头与key的映射对象
{
    username: '用户名'
    name: '姓名',
}

body为需要导出的原数据
[
    {
        username: 1,
        name: 'lihua'
    },
    {
        username: 2,
        name: '<PERSON><PERSON><PERSON>'
    }
]

fileName: 导出的文件文称
*/
import * as XLSX from 'xlsx';

export const exportFile = ({ header = [], body = [], fileName = '数据' }) => {
	return new Promise((resolve) => {
		if (header.length === 0 || body.length === 0) {
			throw '导出的数据不能为空';
		}
		let data = body.map((item) => {
			let newitem = {};
			Object.keys(header).forEach((key) => {
				newitem[header[key]] = item[key];
			});
			return newitem;
		});
		let sheet = XLSX.utils.json_to_sheet(data);
		let book = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(book, sheet, 'sheet1');
		XLSX.writeFile(book, `${fileName}.xls`);
		resolve(true);
	}).catch(() => {
	});
};
