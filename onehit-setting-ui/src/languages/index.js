import Vue from "vue";
import VueI18n from "vue-i18n";

Vue.use(VueI18n);

import enLocale from "element-ui/lib/locale/lang/en";
import zhLocale from "element-ui/lib/locale/lang/zh-CN";
import ruLocale from "element-ui/lib/locale/lang/ru-RU";
import en_US from "./en_US.json";
import zh_CN from "./zh_CN.json";
import ru_RU from "./ru_RU.json";

const locale = localStorage.getItem("lang");
const i18n = new VueI18n({
	locale: locale ? locale : "zh_CN",
	messages: {
		zh_CN: Object.assign(zh_CN, zhLocale),
		en_US: Object.assign(en_US, enLocale),
		ru_RU: Object.assign(ru_RU, ruLocale),
	},
	silentTranslationWarn: true,
});

export default i18n;