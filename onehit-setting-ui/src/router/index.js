/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-08-18 20:20:22
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-08-18 20:37:58
 */
import Vue from "vue";
import VueRouter from "vue-router";
import i18n from '../languages/index'

// 解决ElementUI导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
	return originalPush.call(this, location).catch((err) => err);
};

Vue.use(VueRouter);

const routes = [
	{
		path: "/",
		name: "Plugin",
		component: (resolve) => require(["@/views/PluginHome"], resolve),
		children: [
			{
				meta: {
					title: i18n.t("全部插件")
				},
				path: "/pluginManage",
				name: "pluginManage",
				component: (resolve) => require(["@/views/plugin/PluginManage"], resolve)
			},
			{
				meta: {
					title: i18n.t('创建插件')
				},
				path: "/createPlugin",
				name: "createPlugin",
				component: (resolve) => require(["@/views/plugin/CreatePlugin"], resolve)
			}
		]
	}
];

const router = new VueRouter({
	mode: "hash",
	routes,
});

export default router;
