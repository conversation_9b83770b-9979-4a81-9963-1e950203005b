
var $ = require('jquery');
import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "./assets/reset.css";
import "./assets/common/common.css";
import "@/assets/iconfont/iconfont.css"
import "@/assets/iconfont/iconfont.js"
import VueI18n from "vue-i18n";

import i18n from './languages/index'

import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

// import enLocale from "element-ui/lib/locale/lang/en";
// import zhLocale from "element-ui/lib/locale/lang/zh-CN";
// import ruLocale from "element-ui/lib/locale/lang/ru-RU";
// import en_US from "./languages/en_US.json";
// import zh_CN from "./languages/zh_CN.json";
// import ru_RU from "./languages/ru_RU.json";

import hljs from 'highlight.js'
import 'highlight.js/styles/dark.css'
import uploader from 'vue-simple-uploader';

Vue.use(uploader);

function userIsAdmin() {
	let isAdmin = false;
	$.ajax({
		type: "GET",
		async: false,
		url: "/UAMS/users/currentUser",
		success: (data) => {
			if (data.id === 'BEFE2FF1A20F4C419C8A94B7213C5219') isAdmin = true;
			if (data.roles){
				data.roles.forEach(role => {
					if (role.id === 'BEFE2FF1A20F4C419C8A94B7213C5220' || role.id === 'BEFE2FF1A20F4C419C8A94B7213C5221') isAdmin = true;
				})
			}
		},
		error: function () {
			throw "数据服务连接失败, 请联系管理员!";
		},
	});
	return isAdmin;
}

router.beforeEach((to, from, next) => {
	if (!userIsAdmin() && to.path === '/userManage') {
		next({ path: '/permissionTips' }); // 重定向到无权限页面
	}
	/* 路由发生变化修改页面meta */
	if(to.meta.content){
		let head = document.getElementsByTagName('head');
		let meta = document.createElement('meta');
		meta.content = to.meta.content;
		head[0].appendChild(meta)
	}
	/* 路由发生变化修改页面title */
	if (to.meta.title) {
		document.title = to.meta.title;
	}
	next()
});


Vue.config.productionTip = false;

Vue.use(VueI18n);

Vue.directive('highlight', function (el) {
	hljs.configure({ languages: [] })
	const blocks = el.querySelectorAll('span')
	blocks.forEach((block) => {
	  hljs.highlightBlock(block)
	})
})

// const locale = localStorage.getItem("lang");

// const i18n = new VueI18n({
// 	locale: locale ? locale : "zh_CN", // 语言标识 //this.$i18n.locale // 通过切换locale的值来实现语言切换
// 	messages: {
// 		zh_CN: Object.assign(zh_CN, zhLocale),
// 		en_US: Object.assign(en_US, enLocale),
// 		ru_RU: Object.assign(ru_RU, ruLocale),
// 	},
// 	silentTranslationWarn: true,
// });

Vue.use(ElementUI, { size: "small", i18n: (key, value) => i18n.t(key, value) });

new Vue({
	i18n,
	router,
	store,
	render: (h) => h(App),
}).$mount("#app");
