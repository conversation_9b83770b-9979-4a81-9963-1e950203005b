import axios from 'axios'
import { Notification, MessageBox } from 'element-ui'
import qs from 'qs'

const createAxios = baseURL => {
  const instance = axios.create({
    baseURL,
    timeout: 6000000,
    timeoutErrorMessage: '连接服务器超时',
    paramsSerializer (params) {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    }
  })
  bindRequestInterceptor(instance)
  bindResponseInterceptor(instance)
  return instance
}

const bindRequestInterceptor = axiosInstance => {
  axiosInstance.interceptors.request.use(config => {
    const timeSymbol = config.url.includes('?') ? '&t=' : '?t='
    config.url += timeSymbol + Date.now()
    return config
  }, (error) => {
    return Promise.reject(error)
  })
}

const bindResponseInterceptor = axiosInstance => {
  axiosInstance.interceptors.response.use(response => response, error => {
    const response = error.response
    if (response) {
      const status = error.response.status
      if (status.toString().startsWith('50')) {
        MessageBox.alert('内部服务异常', '错误', {
          type: 'error'
        })
      } else if (status === 401) {
        console.log(status);
        Notification.closeAll()
        Notification.warning({
          title: '登录过期',
          message: '3秒后将跳转到登录页面',
          duration: 3000,
          onClose: () => { window.location.reload() }
        })
      }
      return Promise.reject(error)
    }
    return Promise.reject(error.response)
  })
}

export { createAxios }
