import { createAxios } from './axios'
import baseUrls from './baseUrlConfig'

const axios = createAxios(baseUrls.onehit)

export const pluginApi = {
    getPlugins: (params) => axios.get(`/plugin`, {params: params}),
    updatePluginStatus: (param) => {
        const urlSearchParams = new URLSearchParams()
        urlSearchParams.append('ids', param.ids)
        return axios.put(`/plugin/status/${param.status}`, urlSearchParams)
    },
    insertPlugin: (param) => axios.post('/plugin/internal', param),
    updatePlugin: (param) => axios.put('/plugin', param),
    getIconGroup: () => axios.get('/plugin/internal/icon'),
    deletePlugin: (name) => axios.delete(`/plugin/${name}`)
}
