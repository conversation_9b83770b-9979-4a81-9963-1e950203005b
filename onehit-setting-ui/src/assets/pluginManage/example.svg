<?xml version="1.0" encoding="UTF-8"?>
<svg width="371px" height="70px" viewBox="0 0 371 70" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 6</title>
    <defs>
        <rect id="path-1" x="0" y="18.2352941" width="175.823529" height="21.5294118"></rect>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.933333333   0 0 0 0 0.933333333   0 0 0 0 0.937254902  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.933333333   0 0 0 0 0.933333333   0 0 0 0 0.937254902  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-3" x="0" y="0.0919117647" width="175.823529" height="17.9411765"></rect>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.933333333   0 0 0 0 0.933333333   0 0 0 0 0.937254902  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.933333333   0 0 0 0 0.933333333   0 0 0 0 0.937254902  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-5" x="0" y="0" width="175.823529" height="58" rx="1.86715252"></rect>
        <filter x="-4.6%" y="-12.1%" width="109.1%" height="127.6%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.05 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feColorMatrix values="0 0 0 0 0.850980392   0 0 0 0 0.850980392   0 0 0 0 0.850980392  0 0 0 1 0" type="matrix" in="shadowOffsetOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-8" x="0" y="0" width="119.808954" height="22.4058303"></rect>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.881455131   0 0 0 0 0.881455131   0 0 0 0 0.881455131  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#59EAEC" offset="0%"></stop>
            <stop stop-color="#1ACCD7" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="-----自定义插件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="状态说明" transform="translate(-1504.000000, -263.000000)">
            <g id="提示框备份" transform="translate(1492.000000, 199.000000)">
                <g id="编组-9" transform="translate(12.000000, 64.000000)">
                    <g id="编组-6" transform="translate(6.000000, 6.000000)">
                        <g id="编组-8" transform="translate(183.176471, 0.000000)">
                            <rect id="矩形" fill="#FFFFFF" x="0" y="0" width="175.823529" height="58"></rect>
                            <g id="Background">
                                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                            </g>
                            <g id="Background备份">
                                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-3"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                            </g>
                            <rect id="矩形" fill="#F7F7F7" x="7.17647059" y="5.4375" width="8.97058824" height="9.0625"></rect>
                            <rect id="矩形备份-2" fill="#F7F7F7" x="7.17647059" y="45.3125" width="8.97058824" height="9.0625"></rect>
                            <rect id="矩形" fill="#F7F7F7" x="23.3235294" y="5.4375" width="48.4411765" height="9.0625"></rect>
                            <rect id="矩形备份-3" fill="#F7F7F7" x="23.3235294" y="45.3125" width="48.4411765" height="9.0625"></rect>
                            <g id="Icon/物料匹配" transform="translate(7.176471, 25.375000)">
                                <polygon id="button-[9-9]" transform="translate(4.485294, 4.531250) scale(-1, -1) translate(-4.485294, -4.531250) " points="0 0 8.97058824 0 8.97058824 9.0625 0 9.0625"></polygon>
                                <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" fill-rule="nonzero" x="0" y="0" width="8.97058824" height="9.0625"></rect>
                                <path d="M0.840992647,5.65437015 L0.840992647,7.9296875 C0.840992647,8.08609627 0.966501058,8.21289062 1.12132353,8.21289062 L7.84926471,8.21289062 C8.00408718,8.21289062 8.12959559,8.08609627 8.12959559,7.9296875 L8.12959559,7.03660311 L8.69025735,7.03660311 L8.69025735,7.9296875 C8.69025735,8.3989138 8.31373212,8.77929687 7.84926471,8.77929687 L1.12132353,8.77929687 C0.656856116,8.77929687 0.280330882,8.3989138 0.280330882,7.9296875 L0.280330882,5.65437015 L0.840992647,5.65437015 Z M5.3396115,3.44455101 L7.52121528,5.64832899 L8.40992647,5.64832899 L8.40992647,6.30913628 L7.25009946,6.30913628 L5.33955576,4.37913845 L3.29365387,6.44599396 L1.84968546,4.9875217 L0.560661765,4.9875217 L0.560661765,4.32671441 L2.12091275,4.32671441 L3.29351409,5.51132161 L5.3396115,3.44455101 Z M7.84926471,0.283203125 C8.31373212,0.283203125 8.69025735,0.663586199 8.69025735,1.1328125 L8.69025735,4.68935812 L8.12959559,4.68935812 L8.12959559,1.1328125 C8.12959559,0.976403733 8.00408718,0.849609375 7.84926471,0.849609375 L1.12132353,0.849609375 C0.966501058,0.849609375 0.840992647,0.976403733 0.840992647,1.1328125 L0.840992647,3.44455101 L0.280330882,3.44455101 L0.280330882,1.1328125 C0.280330882,0.663586199 0.656856116,0.283203125 1.12132353,0.283203125 L7.84926471,0.283203125 Z" id="形状结合" fill="#0854A1" fill-rule="nonzero"></path>
                            </g>
                            <text id="Text" font-family="PingFangSC-Regular, PingFang SC" font-size="7.31561194" font-weight="normal" fill="#182A4E">
                                <tspan x="23.3235294" y="33.1176471">管廊断面分析</tspan>
                            </text>
                        </g>
                        <g id="卡片">
                            <g id="4">
                                <mask id="mask-6" fill="white">
                                    <use xlink:href="#path-5"></use>
                                </mask>
                                <g id="Background">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-5"></use>
                                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-5"></use>
                                </g>
                                <g id="Footer" mask="url(#mask-6)">
                                    <g transform="translate(0.000000, 86.940864)" id="Footer-Backgrpound">
                                        <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-8"></use>
                                        <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                                    </g>
                                </g>
                                <text id="Header-Title" mask="url(#mask-6)" font-family="PingFangSC-Regular, PingFang SC" font-size="6.53503384" font-weight="normal" line-spacing="9.33576262" fill="#5D697A">
                                    <tspan x="36.4094742" y="34.0737116">可以在三维场景中划出任意断面，系统自动</tspan>
                                    <tspan x="36.4094742" y="43.4094742">分析出当前断面中涉及到的管道…</tspan>
                                </text>
                            </g>
                            <g id="开关-关" transform="translate(145.896120, 11.885246)">
                                <rect id="Track" fill="#D5D5D5" x="0" y="0" width="18.7046308" height="9.50819672" rx="4.75409836"></rect>
                                <ellipse id="Handle" fill="#FFFFFF" cx="4.6761577" cy="4.75409836" rx="3.50711827" ry="3.56557377"></ellipse>
                            </g>
                            <text id="Header-Title" font-family="PingFangSC-Medium, PingFang SC" font-size="7.4686101" font-weight="400" fill="#182A4E">
                                <tspan x="36.4094742" y="19.2029151">管廊断面分析</tspan>
                            </text>
                            <g id="插件图标/断面分析" transform="translate(11.222778, 11.409836)">
                                <rect id="矩形" fill="url(#linearGradient-10)" x="0" y="0" width="17.7693992" height="18.0655738" rx="0.933576262"></rect>
                                <path d="M5.90141493,8.90733151 L7.53586428,10.5686919 L9.8516511,8.21431603 L12.014224,10.412796 L13.3270494,10.412796 L13.3270494,12.5637387 C13.3270494,13.1079831 12.8858522,13.5491803 12.3416078,13.5491803 L5.42779142,13.5491803 C4.88354705,13.5491803 4.44234981,13.1079831 4.44234981,12.5637387 L4.44234981,8.90733151 L5.90141493,8.90733151 Z M13.3270494,5.50183505 L13.3270494,9.66006375 L12.321104,9.66006375 L9.85171419,7.14971806 L7.53570606,9.50399727 L6.20842114,8.15459927 L4.44234981,8.15459927 L4.44234981,5.50183505 C4.44234981,4.95759068 4.88354705,4.51639344 5.42779142,4.51639344 L12.3416078,4.51639344 C12.8858522,4.51639344 13.3270494,4.95759068 13.3270494,5.50183505 Z" id="形状结合" fill="#FFFFFF"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>