.scenatorStyle_empty_container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.scenatorStyle_empty_container .empty_text {
    margin-top: 16px;
    color: #5D697A;
    font-size: 14px;
    text-align: center;
}
/*.scenatorStyle_empty_container.big_container > div {*/
/*    background: url("./common/scenatorStyle_empty_container_big.svg") no-repeat center center;*/
/*    width: 313px;*/
/*    height: 198px;*/
/*    margin: auto;*/
/*}*/
/*.scenatorStyle_empty_container.medium_container > div {*/
/*    background: url("./common/scenatorStyle_empty_container_medium.svg") no-repeat center center;*/
/*    width: 120px;*/
/*    height: 90px;*/
/*    margin: auto;*/
/*}*/
.scenatorStyle_empty_container.small_container > div {
    background: url("./scenatorStyle_empty_container_small.svg") no-repeat center center;
    width: 120px;
    height: 90px;
    margin: auto;
}
.el-table__empty-block{
    position: relative;
}
.el-table__empty-block .table-empty{
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: 0;
    bottom: 0;
    background: url("./tableEmpty.svg") no-repeat center;
}
/*穿梭框我i数据样式*/
.el-transfer-panel .el-transfer-panel__empty{
    height: 125px !important;
    line-height: 125px !important;
    width: 120px !important;
    background:  url('./tableEmpty.svg') no-repeat top center;
    background-size: 120px 125px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.el-form .el-form-item__label {
    color: #5D697A;
}
.el-message {
    width: auto;
}
.scenator_complexMsg article {
    width: 100%;
}
.el-select .el-input .el-select__caret {
    transition: none;
    transform: rotateZ(0deg)!important;
}
