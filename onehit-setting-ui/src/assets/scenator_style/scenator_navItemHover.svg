<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Icon/更多</title>
    <defs>
        <polygon id="path-1" points="0 0 216 0 216 852 0 852"></polygon>
        <filter x="-0.2%" y="-0.1%" width="100.9%" height="100.1%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.850980392   0 0 0 0 0.850980392   0 0 0 0 0.850980392  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <rect id="path-3" x="0" y="0" width="216" height="44"></rect>
        <filter x="-0.2%" y="-1.1%" width="100.5%" height="102.3%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="-1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.898039216   0 0 0 0 0.898039216   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <rect id="path-5" x="0" y="0" width="216" height="44"></rect>
        <filter x="-0.2%" y="-1.1%" width="100.5%" height="102.3%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="-1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.031372549   0 0 0 0 0.329411765   0 0 0 0 0.631372549  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <rect id="path-7" x="0" y="0" width="216" height="44"></rect>
        <filter x="-0.2%" y="-1.1%" width="100.5%" height="102.3%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="0" dy="-1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.933333333   0 0 0 0 0.933333333   0 0 0 0 0.937254902  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <rect id="path-9" x="0" y="0" width="216" height="44"></rect>
        <filter x="-0.2%" y="-1.1%" width="100.5%" height="102.3%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="-1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.898039216   0 0 0 0 0.898039216   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.898039216   0 0 0 0 0.898039216   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <polygon id="path-11" points="0 0 216 0 216 852 0 852"></polygon>
        <filter x="-0.2%" y="-0.1%" width="100.9%" height="100.1%" filterUnits="objectBoundingBox" id="filter-12">
            <feOffset dx="1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.850980392   0 0 0 0 0.850980392   0 0 0 0 0.850980392  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <rect id="path-13" x="0" y="0" width="216" height="44"></rect>
        <filter x="-0.2%" y="-1.1%" width="100.5%" height="102.3%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="-1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.031372549   0 0 0 0 0.329411765   0 0 0 0 0.631372549  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="工作空间" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="三维视图-属性信息" transform="translate(-188.000000, -104.000000)">
            <rect fill="#FFFFFF" x="0" y="0" width="1440" height="900"></rect>
            <g id="导航" transform="translate(-0.000000, 48.000000)">
                <g id="Rectangle备份">
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                </g>
                <g id="工作空间">
                    <g id="TOOL-DEVELOPMENT-(SAP-CLOUD-PLATFORM)/SIDE-NAVIGATION/COZY/LIST-ITEMS/EXPANDED/INACTIVE/EXPANDED/Icon,-Text-&amp;-Arrow">
                        <g id="Background">
                            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-3"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                        </g>
                        <g id="编组" transform="translate(15.000000, 14.000000)">
                            <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                            <g id="Product-Icon" transform="translate(0.500000, 1.000000)" fill="#0854A1" fill-rule="nonzero">
                                <path d="M14,0 C14.5522847,0 15,0.44771525 15,1 L15,10 C15,10.5522847 14.5522847,11 14,11 L8,11 L8,13 L11,13 L11,14 L4,14 L4,13 L7,13 L7,11 L1,11 C0.44771525,11 0,10.5522847 0,10 L0,1 C0,0.44771525 0.44771525,0 1,0 L14,0 Z M14,1 L1,1 L1,10 L14,10 L14,1 Z M12,6.5 L12,7.5 L3,7.5 L3,6.5 L12,6.5 Z M8,3.5 L8,4.5 L3,4.5 L3,3.5 L8,3.5 Z" id="形状结合"></path>
                            </g>
                        </g>
                        <text id="Text" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#182A4E">
                            <tspan x="47" y="27">工作空间</tspan>
                        </text>
                    </g>
                    <g id="三维视图" transform="translate(0.000000, 44.000000)">
                        <g id="Background">
                            <use fill="#E5F0FA" fill-rule="evenodd" xlink:href="#path-5"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                        </g>
                        <g id="编组" transform="translate(188.000000, 12.000000)" fill-rule="nonzero">
                            <g transform="translate(2.000000, 2.000000)">
                                <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" x="0" y="0" width="16" height="16"></rect>
                                <circle id="椭圆形" fill="#0854A1" cx="4" cy="8" r="1"></circle>
                                <circle id="椭圆形" fill="#0854A1" cx="8" cy="8" r="1"></circle>
                                <circle id="椭圆形" fill="#0854A1" cx="12" cy="8" r="1"></circle>
                            </g>
                        </g>
                        <text id="Text" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#0854A1">
                            <tspan x="47" y="27">三维视图</tspan>
                        </text>
                    </g>
                    <g id="TOOL-DEVELOPMENT-(SAP-CLOUD-PLATFORM)/SIDE-NAVIGATION/COZY/LIST-ITEMS/EXPANDED/INACTIVE/Sub-List-Item,-Icon" transform="translate(0.000000, 88.000000)">
                        <rect id="Background" fill="#FFFFFF" x="0" y="0" width="216" height="44"></rect>
                        <text id="Text" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#5D697A">
                            <tspan x="47" y="27">PID图纸</tspan>
                        </text>
                    </g>
                    <g id="TOOL-DEVELOPMENT-(SAP-CLOUD-PLATFORM)/SIDE-NAVIGATION/COZY/LIST-ITEMS/EXPANDED/INACTIVE/Sub-List-Item,-Icon" transform="translate(0.000000, 132.000000)">
                        <rect id="Background" fill="#FFFFFF" x="0" y="0" width="216" height="44"></rect>
                        <text id="Text" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#5D697A">
                            <tspan x="47" y="27">文档</tspan>
                        </text>
                    </g>
                    <g id="TOOL-DEVELOPMENT-(SAP-CLOUD-PLATFORM)/SIDE-NAVIGATION/COZY/LIST-ITEMS/EXPANDED/INACTIVE/Sub-List-Item,-Icon备份" transform="translate(0.000000, 176.000000)">
                        <rect id="Background" fill="#FFFFFF" x="0" y="0" width="216" height="44"></rect>
                        <text id="Text" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#5D697A">
                            <tspan x="47" y="27">台账</tspan>
                        </text>
                    </g>
                    <g id="TOOL-DEVELOPMENT-(SAP-CLOUD-PLATFORM)/SIDE-NAVIGATION/COZY/LIST-ITEMS/EXPANDED/INACTIVE/Sub-List-Item,-Icon备份-2" transform="translate(0.000000, 220.000000)">
                        <rect id="Background" fill="#FFFFFF" x="0" y="0" width="216" height="44"></rect>
                        <text id="Text" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#5D697A">
                            <tspan x="47" y="27">1月检修计划</tspan>
                        </text>
                    </g>
                    <g id="编组" transform="translate(160.000000, 12.000000)">
                        <g id="设置" transform="translate(2.000000, 2.000000)">
                            <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" x="0" y="0" width="16" height="16"></rect>
                            <g id="编组" transform="translate(1.333333, 1.333333)" stroke="#0854A1" stroke-linejoin="round">
                                <polygon id="路径" points="6.66666667 -2.22044605e-16 4.66666667 2 2 2 2 4.66666667 -2.22044605e-16 6.66666667 2 8.66666667 2 11.3333333 4.66666667 11.3333333 6.66666667 13.3333333 8.66666667 11.3333333 11.3333333 11.3333333 11.3333333 8.66666667 13.3333333 6.66666667 11.3333333 4.66666667 11.3333333 2 8.66666667 2"></polygon>
                                <circle id="椭圆形" cx="6.66666667" cy="6.66666667" r="2"></circle>
                            </g>
                        </g>
                    </g>
                    <g id="编组" transform="translate(188.000000, 12.000000)" fill-rule="nonzero">
                        <g id="下" transform="translate(2.000000, 2.000000)">
                            <rect id="矩形" fill="#FFFFFF" opacity="0" x="0" y="0" width="16" height="16"></rect>
                            <path d="M13.8660126,5.16340631 L8.42963278,11.7934259 L8.42963278,11.7934259 C8.22638386,12.0413148 7.87590192,12.0661037 7.64320639,11.8677926 L7.57036722,11.7934259 L2.13398735,5.16340631 C2.09896902,5.12069916 2.10520203,5.05769021 2.14790917,5.02267187 C2.1657881,5.00801178 2.18819462,5 2.21131548,5 L3.44268177,5 C3.47263522,5 3.50101133,5.01342707 3.52000536,5.03658816 L8,10.4994366 L8,10.4994366 L12.4789944,5.03659657 C12.4979883,5.01343047 12.5263676,5 12.5563249,5 L13.7886845,5 C13.843913,5 13.8886845,5.04477153 13.8886845,5.1 C13.8886845,5.12312086 13.8806727,5.14552738 13.8660126,5.16340631 Z" id="路径" fill="#0854A1"></path>
                        </g>
                    </g>
                </g>
                <g id="扒图扒料" transform="translate(0.000000, 308.000000)">
                    <g id="Background">
                        <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-7"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                    </g>
                    <text id="Text" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#182A4E">
                        <tspan x="47" y="27">扒图扒料</tspan>
                    </text>
                    <g id="Icon/扒图扒料" transform="translate(16.000000, 14.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <path d="M2,10.999 L2,14 L5,13.999 L5,14.999 L2,15 C1.44771525,15 1,14.5522847 1,14 L1,10.999 L2,10.999 Z M15,10.999 L15,14 C15,14.5522847 14.5522847,15 14,15 L11,14.999 L11,13.999 L14,14 L14,10.999 L15,10.999 Z M14,1 C14.5522847,1 15,1.44771525 15,2 L15,4.999 L14,4.999 L14,2 L11,1.999 L11,0.999 L14,1 Z M5,0.999 L5,1.999 L2,2 L2,4.999 L1,4.999 L1,2 C1,1.44771525 1.44771525,1 2,1 L5,0.999 Z" id="形状结合" fill="#0854A1" fill-rule="nonzero"></path>
                        <path d="M9.25258839,3.5 L11.5,5.85223397 L11.5,12 L5,12.5 L4.5,4 L9.25258839,3.5 Z" id="矩形" stroke="#0854A1"></path>
                        <rect id="矩形" fill="#0854A1" x="7" y="4" width="1" height="8"></rect>
                        <rect id="矩形" fill="#0854A1" x="5" y="9" width="6" height="1"></rect>
                    </g>
                </g>
                <g id="物料匹配" transform="translate(0.000000, 264.000000)">
                    <g id="Background">
                        <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-9"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                    </g>
                    <g id="Icon/物料匹配" transform="translate(16.000000, 14.000000)">
                        <polygon id="button-[9-9]" transform="translate(8.000000, 8.000000) scale(-1, -1) translate(-8.000000, -8.000000) " points="0 0 16 0 16 16 0 16"></polygon>
                        <path d="M12,1 C12.5522847,1 13,1.44771525 13,2 L13,4 L12,4 L12,2 L2,2 L2,14 L12,14 L12,12 L13,12 L13,14 C13,14.5522847 12.5522847,15 12,15 L2,15 C1.44771525,15 1,14.5522847 1,14 L1,2 C1,1.44771525 1.44771525,1 2,1 L12,1 Z" id="形状结合" fill="#0854A1" fill-rule="nonzero"></path>
                        <rect id="矩形" fill="#0854A1" x="3" y="4" width="3" height="1"></rect>
                        <rect id="矩形备份" fill="#0854A1" x="3" y="7.5" width="3" height="1"></rect>
                        <rect id="矩形备份-2" fill="#0854A1" x="3" y="11" width="3" height="1"></rect>
                        <path d="M14,4 C14.5522847,4 15,4.44771525 15,5 L15,11 C15,11.5522847 14.5522847,12 14,12 L8,12 C7.44771525,12 7,11.5522847 7,11 L7,5 C7,4.44771525 7.44771525,4 8,4 L14,4 Z M14,5 L8,5 L8,11 L14,11 L14,5 Z" id="矩形" fill="#0854A1" fill-rule="nonzero"></path>
                        <g id="编组" transform="translate(8.800000, 5.600000)" fill="#0854A1">
                            <path d="M3.2,1.78649584 L3.2,0.213504161 C3.2,0.158275686 3.24477153,0.113504161 3.3,0.113504161 C3.32339365,0.113504161 3.34604694,0.121705783 3.36401844,0.136682033 L4.30781345,0.923177872 C4.35024114,0.95853428 4.35597354,1.02159075 4.32061713,1.06401844 C4.31674521,1.06866475 4.31245975,1.07295021 4.30781345,1.07682213 L3.36401844,1.86331797 C3.32159075,1.89867437 3.25853428,1.89294197 3.22317787,1.85051428 C3.20820162,1.83254278 3.2,1.80988949 3.2,1.78649584 Z" id="路径-5"></path>
                            <path d="M3.43858431,0.6 L3.43858431,1.4 L0.9,1.4 C0.858578644,1.4 0.823039322,1.42518398 0.807858496,1.46107549 L0.8,1.5 L0.8,2.4 L0,2.4 L0,1.5 C0,1.04117882 0.343336665,0.662548221 0.787105912,0.607012278 L0.9,0.6 L3.43858431,0.6 Z" id="路径-6" fill-rule="nonzero"></path>
                        </g>
                        <g id="编组备份-2" transform="translate(11.028101, 9.143248) rotate(-180.000000) translate(-11.028101, -9.143248) translate(8.856202, 8.000000)" fill="#0854A1">
                            <path d="M3.3,-6.68909372e-15 C3.32339365,-6.68909372e-15 3.34604694,0.00820162194 3.36401844,0.023177872 L4.30781345,0.809673711 C4.35024114,0.845030119 4.35597354,0.908086589 4.32061713,0.950514279 C4.31674521,0.955160585 4.31245975,0.959446045 4.30781345,0.963317967 L3.36401844,1.74981381 C3.32159075,1.78517021 3.25853428,1.77943781 3.22317787,1.73701012 C3.20820162,1.71903862 3.2,1.69638533 3.2,1.67299168 L3.2,1.286 L0.9,1.28649584 C0.858578644,1.28649584 0.823039322,1.31167982 0.807858496,1.34757133 L0.8,1.38649584 L0.8,2.28649584 L0,2.28649584 L0,1.38649584 C0,0.927674662 0.343336665,0.54904406 0.787105912,0.493508116 L0.9,0.486495839 L3.2,0.486 L3.2,0.1 C3.2,0.044771525 3.24477153,-6.67521594e-15 3.3,-6.68909372e-15 Z" id="形状结合"></path>
                        </g>
                    </g>
                    <text id="Text" font-family="PingFangSC-Regular, PingFang SC" font-size="14" font-weight="normal" fill="#182A4E">
                        <tspan x="47" y="27">物料匹配</tspan>
                    </text>
                </g>
                <rect id="矩形" fill="#E5E5E5" x="0" y="807" width="216" height="1"></rect>
                <g id="编组" transform="translate(98.000000, 820.000000)" fill-rule="nonzero">
                    <g id="icon/收起">
                        <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" x="0" y="0" width="20" height="20"></rect>
                        <path d="M16.6666667,15 L16.6666667,16.25 L3.33333333,16.25 L3.33333333,15 L16.6666667,15 Z M16.6666667,11.25 L16.6666667,12.5 L10,12.5 L10,11.25 L16.6666667,11.25 Z M7.54093557,7.5 L7.54093557,12.5 L3.33333333,10 L7.54093557,7.5 Z M16.6666667,7.5 L16.6666667,8.75 L10,8.75 L10,7.5 L16.6666667,7.5 Z M16.6666667,3.75 L16.6666667,5 L3.33333333,5 L3.33333333,3.75 L16.6666667,3.75 Z" id="形状结合" fill="#0854A1"></path>
                    </g>
                </g>
            </g>
            <g id="导航" transform="translate(0.000000, 48.000000)">
                <g id="Rectangle备份">
                    <use fill="black" fill-opacity="1" filter="url(#filter-12)" xlink:href="#path-11"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-11"></use>
                </g>
                <g id="三维视图" transform="translate(0.000000, 44.000000)">
                    <g id="Background">
                        <use fill="#E5F0FA" fill-rule="evenodd" xlink:href="#path-13"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                    </g>
                    <g id="编组" transform="translate(188.000000, 12.000000)" fill-rule="nonzero">
                        <g transform="translate(2.000000, 2.000000)">
                            <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" x="0" y="0" width="16" height="16"></rect>
                            <circle id="椭圆形" fill="#0854A1" cx="4" cy="8" r="1"></circle>
                            <circle id="椭圆形" fill="#0854A1" cx="8" cy="8" r="1"></circle>
                            <circle id="椭圆形" fill="#0854A1" cx="12" cy="8" r="1"></circle>
                        </g>
                    </g>
                    <g id="Icon/更多" transform="translate(188.000000, 12.000000)" fill-rule="nonzero">
                        <g id="编组" transform="translate(2.000000, 2.000000)">
                            <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" x="0" y="0" width="16" height="16"></rect>
                            <circle id="椭圆形" fill="#0854A1" cx="4" cy="8" r="1"></circle>
                            <circle id="椭圆形" fill="#0854A1" cx="8" cy="8" r="1"></circle>
                            <circle id="椭圆形" fill="#0854A1" cx="12" cy="8" r="1"></circle>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>