<template>
  <div class="onehit-dropdown">
    <el-dropdown v-bind="$attrs" v-on="$listeners">
      <slot></slot>
      <el-dropdown-menu slot="dropdown" :append-to-body="false" ref="onehit-dropdown-menus">
        <el-dropdown-item
            v-for="menu in menus" :key="menu.command" :command="menu.command"
            :disabled="menu.disabled" :divided="menu.divided" :icon="menu.icon">
          {{menu.text}}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: "OneHit-Dropdown",
  props: {
    menus: {
      default: () => [],
      type: Array
    }
  }
}
</script>

<style lang="less" scoped>
</style>