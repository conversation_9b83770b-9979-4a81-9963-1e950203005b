<template>
  <div class="el-transfer" style="display: flex;align-items: center">
    <transfer-panel
      v-bind="$props"
      ref="leftPanel"
      :data="sourceData"
      :title="titles[0] || t('el.transfer.titles.0')"
      :default-checked="leftDefaultChecked"
      :placeholder="filterPlaceholder || t('el.transfer.filterPlaceholder')"
      @checked-change="onSourceCheckedChange">
      <slot name="left-footer"></slot>
    </transfer-panel>
    <div class="el-transfer__buttons">
      <el-button
        type="primary"
        :class="['el-transfer__button', hasButtonTexts ? 'is-with-texts' : '']"
        @click.native="addToLeft"
        :disabled="rightChecked.length === 0">
        <i class="el-icon-arrow-left"></i>
        <span v-if="buttonTexts[0] !== undefined">{{ buttonTexts[0] }}</span>
      </el-button>
      <el-button
        type="primary"
        :class="['el-transfer__button', hasButtonTexts ? 'is-with-texts' : '']"
        @click.native="addToRight"
        :disabled="leftChecked.length === 0">
        <span v-if="buttonTexts[1] !== undefined">{{ buttonTexts[1] }}</span>
        <i class="el-icon-arrow-right"></i>
      </el-button>
    </div>
    <transfer-panel
      v-bind="$props"
      ref="rightPanel"
      :data="targetData"
      :title="titles[1] || t('el.transfer.titles.1')"
      :default-checked="rightDefaultChecked"
      :placeholder="filterPlaceholder || t('el.transfer.filterPlaceholder')"
      @checked-change="onTargetCheckedChange">
      <slot name="right-footer"></slot>
    </transfer-panel>
  </div>
</template>

<script>
  import Emitter from 'element-ui/src/mixins/emitter';
  import Locale from 'element-ui/src/mixins/locale';
  import TransferPanel from './transfer-panel.vue';
  import Migrating from 'element-ui/src/mixins/migrating';

  export default {
    name: 'ElTransfer',

    mixins: [Emitter, Locale, Migrating],

    components: {
      TransferPanel
    },

    props: {
      data: {
        type: Array,
        default() {
          return [];
        }
      },
      titles: {
        type: Array,
        default() {
          return [];
        }
      },
      buttonTexts: {
        type: Array,
        default() {
          return [];
        }
      },
      filterPlaceholder: {
        type: String,
        default: ''
      },
      filterMethod: Function,
      leftDefaultChecked: {
        type: Array,
        default() {
          return [];
        }
      },
      rightDefaultChecked: {
        type: Array,
        default() {
          return [];
        }
      },
      renderContent: Function,
      value: {
        type: Array,
        default() {
          return [];
        }
      },
      format: {
        type: Object,
        default() {
          return {};
        }
      },
      filterable: Boolean,
      props: {
        type: Object,
        default() {
          return {
            label: 'label',
            key: 'key',
            disabled: 'disabled'
          };
        }
      },
      targetOrder: {
        type: String,
        default: 'original'
      },
      virtualScroll: {
        type: Boolean,
        default: false
      }
    },

    data() {
      return {
        leftChecked: [],
        rightChecked: []
      };
    },

    computed: {
      dataObj() {
        const key = this.props.key;
        return this.data.reduce((o, cur) => (o[cur[key]] = cur) && o, {});
      },
  
      sourceData() {
        let valueObj = {};
        this.value.forEach((item)=>{valueObj[item] = true;});
        return this.data.filter((item) => !valueObj[item[this.props.key]]);
      },

      targetData() {
        if (this.targetOrder === 'original') {
          let valueObj = {};
          this.value.forEach((item)=>{valueObj[item] = true;});
          let data = this.data.filter((item) => valueObj[item[this.props.key]]);
          return data;
        } else {
          return this.value.reduce((arr, cur) => {
            const val = this.dataObj[cur];
            if (val) {
              arr.push(val);
            }
            return arr;
          }, []);
        }
      },

      hasButtonTexts() {
        return this.buttonTexts.length === 2;
      }
    },

    watch: {
      value(val) {
        this.dispatch('ElFormItem', 'el.form.change', val);
      }
    },

    methods: {
      getMigratingConfig() {
        return {
          props: {
            'footer-format': 'footer-format is renamed to format.'
          }
        };
      },

      onSourceCheckedChange(val, movedKeys) {
        this.leftChecked = val;
        if (movedKeys === undefined) return;
        this.$emit('left-check-change', val, movedKeys);
      },

      onTargetCheckedChange(val, movedKeys) {
        this.rightChecked = val;
        if (movedKeys === undefined) return;
        this.$emit('right-check-change', val, movedKeys);
      },

      addToLeft() {
        let currentValue = this.value.slice();
        this.rightChecked.forEach(item => {
          const index = currentValue.indexOf(item);
          if (index > -1) {
            currentValue.splice(index, 1);
          }
        });
        this.$emit('input', currentValue);
        this.$emit('change', currentValue, 'left', this.rightChecked);
      },

      addToRight() {
        let currentValue = this.value.slice();
        const itemsToBeMoved = [];
        const key = this.props.key;
        let leftCheckedKeyPropsObj = {};
        this.leftChecked.forEach((item) => {
          leftCheckedKeyPropsObj[item] = true;
        });
        let valueKeyPropsObj = {};
        this.value.forEach((item) => {
          valueKeyPropsObj[item] = true;
        });
        this.data.forEach((item) => {
          const itemKey = item[key];
          // O(n)
          if (
              leftCheckedKeyPropsObj[itemKey] &&
              !valueKeyPropsObj[itemKey]) {
            itemsToBeMoved.push(itemKey);
          }
        });
        currentValue = this.targetOrder === 'unshift'
            ? itemsToBeMoved.concat(currentValue)
            : currentValue.concat(itemsToBeMoved);
        this.$emit('input', currentValue);
        this.$emit('change', currentValue, 'right', this.leftChecked);
      },

      clearQuery(which) {
        if (which === 'left') {
          this.$refs.leftPanel.query = '';
        } else if (which === 'right') {
          this.$refs.rightPanel.query = '';
        }
      }
    }
  };
</script>

<style lang="less">
.el-transfer-panel {
  width: 300px;
  height: 500px;
  border-color: #d9d9d9;
  .el-transfer-panel__header {
    background: #efefef;
    box-shadow: inset 0px -1px 0px 0px #d9d9d9;
  }
  .el-transfer-panel__body {
    .el-transfer-panel__filter {
      margin: 16px 16px 8px;
      .el-input__inner {
        border-radius: 2px;
        border: 1px solid #89919a;
      }
      .el-icon-search {
        width: 32px;
        margin-left: 0;
        &::before {
          width: 32px;
          height: 32px;
          font-family: "onehit-icon" !important;
          background: url("~@/assets/iconfont/iconfont.ttf") no-repeat center center;
          content: "\e61c";
          color: #0854a1;
          display: inline-block;
        }
      }
      .el-icon-circle-close {
        width: 32px;
        margin-left: 0;
        &::before {
          width: 32px;
          height: 32px;
          font-family: "onehit-icon" !important;
          background: url("~@/assets/iconfont/iconfont.ttf") no-repeat center center;
          content: "\e618";
          color: #0854a1;
          display: inline-block;
        }
      }
    }
  }
  .el-transfer-panel__list {
    height: 402px;
    .el-checkbox {
      height: 32px;
      line-height: 32px;
    }
  }
  .el-checkbox {
    .el-checkbox__label {
      color: #182a4e;
    }
    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      border: 1px solid #89919a;
      background-color: #ffffff;
      border-radius: 2px;
      &:hover {
        border-color: #0854a1;
      }
    }
    .el-checkbox__input {
      &.is-checked {
        .el-checkbox__inner {
          background-color: transparent;
          border-color: #89919a;
          border-radius: 2px;
          &::after {
            left: 5px;
            top: 2px;
            border-color: #0854a1;
          }
          &:hover {
            border-color: #0854a1;
          }
        }
      }
      &.is-indeterminate {
        .el-checkbox__inner {
          background-color: transparent;
          border-color: #89919a;
          border-radius: 2px;
          &::before {
            background-color: #0854a1;
            top: 3px;
            left: 3px;
            height: 8px;
            width: 8px;
            transform: rotate(0) scaleY(1);
          }
        }
        &:hover {
          border-color: #0854a1;
        }
      }
    }
  }
}
.el-transfer__buttons {
  padding: 0;
  margin: 10px;
  .el-button {
    width: 24px;
    height: 24px;
    margin: 0 0 10px 0;
    padding: 0;
    display: block;
    color: #0854a1;
    border-color: #0854a1;
    background-color: #ffffff;
    &.is-disabled {
      opacity: 0.4;
    }
  }
  .el-button:hover{
    color: #0854A1;
  }
}
</style>