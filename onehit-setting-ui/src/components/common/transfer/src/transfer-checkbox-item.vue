<template>
  <el-checkbox
      class="el-transfer-panel__item"
      :label="source[keyProp]"
      :disabled="source[disabledProp]">
    <option-content :option="source"></option-content>
  </el-checkbox>
</template>

<script>
export default {
  name: 'transfer-checkbox-item',
  props: {
    index: {
      type: Number
    },
    source: {
      type: Object,
      default() {
        return {};
      }
    },
    keyProp: {
      type: String
    },
    disabledProp: {
      type: String
    }
  },
  components: {
    OptionContent: {
      props: {
        option: Object
      },
      render(h) {
        const getParent = vm => {
          if (vm.$options.componentName === 'ElTransferPanel') {
            return vm;
          } else if (vm.$parent) {
            return getParent(vm.$parent);
          } else {
            return vm;
          }
        };
        const panel = getParent(this);
        const transfer = panel.$parent || panel;
        return panel.renderContent
            ? panel.renderContent(h, this.option)
            : transfer.$scopedSlots.default
                ? transfer.$scopedSlots.default({ option: this.option })
                : <span>{ this.option[panel.labelProp] || this.option[panel.keyProp] }</span>;
      }
    }
  }
};
</script>