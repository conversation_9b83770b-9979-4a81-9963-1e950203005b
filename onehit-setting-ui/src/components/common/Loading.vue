<template>
  <div class="loading-wrap">
    <div class="loading-item loading-active"></div>
    <span class="loading-text">{{this.text}}</span>
  </div>
</template>

<script>
export default {
    name: 'Loading',
    props: {
        text: String
    }
}
</script>

<style lang="less" scoped>
.loading-wrap {
  z-index: 500;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.8);
  opacity: 0.85;
}
.loading-item {
  width: 12px;
  height: 12px;
  margin-left: 36px;
  border-radius: 50%;
  box-shadow: -54px 0px #0854a1, -18px 0px #0854a1, 18px 0px #0854a1;
  background-color: #fff;
}
.loading-active {
  animation: shadowScale 1s linear infinite;
}
@keyframes shadowScale {
  0% {
    box-shadow: -54px 0px 0px 3px #0854a1, -18px 0px #0854a1, 18px 0px #0854a1;
  }

  33% {
   box-shadow: -54px 0px #0854a1, -18px 0px 0px 3px #0854a1, 18px 0px #0854a1;
  }

  66% {
   box-shadow: -54px 0px #0854a1, -18px 0px #0854a1, 18px 0px 0px 3px #0854a1;
  }

  100% {
   box-shadow: -54px 0px #0854a1, -18px 0px #0854a1, 18px 0px #0854a1;
  }
}

.loading-text {
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 40px;
  color: #5d697a;
}
</style>