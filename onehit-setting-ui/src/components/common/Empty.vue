<template>
  <div class="empty">
    <img :src="this.imgSrc" v-if="this.imgSrc"/>
    <div class="text" v-if="this.text">{{text}}</div>
  </div>
</template>

<script>
export default {
    name: 'Empty',
    props: {
        text: String,
        imgSrc: String
    }
}
</script>

<style lang="less" scoped>
.empty {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .text {
        height: 22px;
        line-height: 22px;
        font-size: 14px;
        font-weight: 400;
        color: #5D697A;
    }
}
</style>