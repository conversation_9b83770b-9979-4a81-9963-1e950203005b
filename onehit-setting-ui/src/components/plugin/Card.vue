<template>
  <div>
    <div class="card" @mouseenter="handleMouseEnterCard" @mouseleave="handleMouseLeaveCard">
      <div class="left">
        <div class="ico">
          <el-image :src="imgSrc" fit="fill" lazy></el-image>
        </div>
      </div>
      <div class="right">
        <div class="top">
          <el-tooltip ref="nameTip" :content="plugin.name" :visible-arrow="false" :disabled="!isShowNameTip"
                      :open-delay="1000">
            <div class="name">
              <span v-html="pluginName" ref="name"></span>
            </div>
          </el-tooltip>
          <Dropdown v-show="isShowMore" class="dropdown" ref="dropdown" :menus="menus" trigger="click"
                    placement="bottom-start" @command="handleMenuClick">
            <el-button type="secondary--button menu-icon" class="onehit-icon onehit-icon-more"
                       @click="handleDropdownClick"></el-button>
          </Dropdown>
          <div v-show="isShowMore" ref="dropdown-menus" class="dropdown-menus"></div>
          <div class="switch">
            <el-tooltip ref="switchTip" v-show="isOpen === true" :content="tooltipText" :visible-arrow="false"
                        :open-delay="1000">
              <el-switch :value="true" @change="this.toggleSwitch"></el-switch>
            </el-tooltip>
            <el-tooltip ref="switchTip" v-show="isOpen === false" :content="tooltipText" :visible-arrow="false"
                        :open-delay="1000">
              <el-switch :value="false" @change="this.toggleSwitch"></el-switch>
            </el-tooltip>
          </div>
        </div>
        <div class="bottom">
          <el-tooltip ref="descTip" :content="plugin.description" :visible-arrow="false" :open-delay="1000"
                      :disabled="!isShowDescTip">
            <div class="desc" ref="desc">{{ plugin.description }}</div>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Dropdown from "../common/Dropdown";

export default {
  name: 'Card',
  props: {
    plugin: Object,
    searchText: String
  },
  data() {
    return {
      tooltipText: '',
      imgSrc: '',
      isShowNameTip: false,
      isShowDescTip: false,
      isShowMore: false,
      isDropdownClick: false,
      menus: [
        {
          command: 'EDIT',
          text: this.$t('编辑')
        },
        {
          command: 'DELETE',
          text: this.$t('删除')
        }
      ],
      customBusinessType: ['自定义']
    }
  },
  components: {
    Dropdown
  },
  computed: {
    pluginName() {
      if (this.searchText === '') {
        return this.plugin.name
      }
      const reg = new RegExp(this.searchText, 'gi')
      const matchResult = reg.exec(this.plugin.name)
      if (matchResult !== null) {
        const keyword = this.plugin.name.substring(matchResult.index, matchResult.index + this.searchText.length)
        return this.plugin.name.replace(keyword, `<span class="highlight">${keyword}</span>`)
      }
      return this.plugin.name
    },
    isOpen: {
      set(newVal) {
        if (newVal) {
          this.tooltipText = this.$t('停用此插件')
        } else {
          this.tooltipText = this.$t('启用此插件')
        }
      },
      get() {
        const openStatus = ['ON', 'FIXED']
        return openStatus.includes(this.plugin?.status);
      }
    },
  },
  created() {
    this.initPluginInfo()
  },
  mounted() {
    this.handleDropdownMenu()
    this.handleToolTip()
    this.initWatchDocumentClick()
  },
  updated() {
    this.initPluginInfo()
    this.handleToolTip()
  },
  methods: {
    initWatchDocumentClick() {
      const that = this
      document.addEventListener('click', () => {
        if (that.isDropdownClick) {
          that.isShowMore = false
          that.isDropdownClick = false
          that.$refs.dropdown.$refs["onehit-dropdown-menus"].dropdown.hide()
        }
      }, true)
    },
    handleToolTip() {
      const descOffsetHeight = this.$refs.desc.offsetHeight
      const descScrollHeight = this.$refs.desc.scrollHeight
      this.isShowDescTip = descScrollHeight > descOffsetHeight
      const nameOffsetHeight = this.$refs.name.offsetHeight
      const nameHeight = this.$refs.name.parentNode.offsetHeight
      this.isShowNameTip = nameOffsetHeight > nameHeight
    },
    handleDropdownMenu() {
      const dropdown = this.$refs.dropdown
      const menuContainer = this.$refs["dropdown-menus"]
      if (dropdown && menuContainer && menuContainer.children.length === 0) {
        menuContainer.appendChild(dropdown.$refs["onehit-dropdown-menus"].$el)
      }
    },
    toggleSwitch(isOpen) {
      this.$emit('togglePluginStatus', {
        plugin: this.plugin,
        status: isOpen
      })
    },
    initPluginInfo() {
      if (this.customBusinessType.includes(this.plugin.businessType)) {
        this.imgSrc = window.location.origin + '/OneHit/plugin/' + this.plugin?.iconGroup + '/icon/preview/' + this.plugin?.pmIcon + '?isSystemInternal=true'
      } else {
        this.imgSrc = window.location.origin + '/OneHit/plugin/' + this.plugin?.name + '/icon/preview/' + this.plugin?.pmIcon
      }
      const openStatus = ['ON', 'FIXED']
      if (openStatus.includes(this.plugin?.status)) {
        this.tooltipText = this.$t('停用此插件')
      } else {
        this.tooltipText = this.$t('启用此插件')
      }
    },
    handleMenuClick(command) {
      const params = {
        command: command,
        plugin: this.plugin
      }
      this.$emit('menuClick', params)
    },
    handleMouseLeaveCard() {
      if (!this.isDropdownClick) {
        this.isShowMore = false
      }
      // this.$refs.dropdown.$refs["onehit-dropdown-menus"].dropdown.hide()
    },
    handleMouseEnterCard() {
      if (this.customBusinessType.includes(this.plugin.businessType)) {
        this.isShowMore = true
      }
    },
    handleDropdownClick() {
      this.isDropdownClick = true
    }
  }
}
</script>

<style lang="less" scoped>
.card {
  width: 376px;
  height: 122px;
  padding: 24px;
  border-radius: 4px;
  box-sizing: border-box;
  margin-bottom: 24px;
  display: flex;
  background-color: #FFFFFF;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.05), 0 0 0 1px #D9D9D9;

  .left {
    width: 38px;
    margin-right: 16px;

    .ico {
      width: 38px;
      height: 38px;

      /deep/ .el-image {
        width: 100%;
        height: 100%;
      }
    }
  }

  .right {
    width: calc(100% - 38px);

    .top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      height: 22px;
      line-height: 22px;
      position: relative;

      .name {
        height: 22px;
        line-height: 22px;
        font-size: 16px;
        font-weight: 500;
        width: 184px;
        color: #182A4E;
        overflow: hidden;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;

        span {
          font-family: PingFangSC-Medium, PingFang SC, Microsoft YaHei;
          word-break: break-all;
          word-wrap: break-word;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .dropdown {
        width: 48px;
        height: 22px;
        line-height: 22px;
        text-align: center;

        /deep/ .el-dropdown {
          height: 100%;
          line-height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .menu-icon {
        width: 20px;
        height: 20px;
        line-height: 20px;
        box-sizing: border-box;
        padding: 0;
        border: none;
        color: #0854a1;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .menu-icon:before {
        font-size: 16px;
      }

      .menu-icon:hover {
        border: 1px solid #0854A1;
      }

      .menu-icon:active, .menu-icon:focus {
        color: #ffffff;
        background-color: #0854A1 !important;
      }

      /deep/ .el-dropdown-menu {
        margin: 0;
        padding: 0;
        position: absolute;
        top: 26px !important;
        left: 200px !important;

        .popper__arrow {
          display: none;
        }

        .popper__arrow::after {
          display: none;
        }

        .el-dropdown-menu__item {
          width: 88px;
          height: 44px;
          line-height: 44px;
          text-align: center;
          font-size: 14px;
          padding: 0;
          box-sizing: border-box;
        }

        .el-dropdown-menu__item:not(.is-disabled):focus, .el-dropdown-menu__item:not(.is-disabled):hover {
          background-color: #EFF2F4;
          color: #182A4E;
        }

        .el-dropdown-menu__item:not(.is-disabled):active {
          background-color: #0854A1;
          color: #FFFFFF;
        }
      }

      .switch {
        /deep/ .el-switch__core {
          border-radius: 12px;
        }

        /deep/ .el-switch__core::after {
          width: 15px;
          height: 15px;
        }

        /deep/ .el-switch.is-checked .el-switch__core {
          border-color: #0854A1;
          background-color: #0854A1;
        }
      }
    }

    .bottom {
      .desc {
        width: 100%;
        line-height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #5D697A;
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        word-break: break-all;
        word-wrap: break-word;
      }
    }
  }
}
</style>
<style>
.highlight {
  background-color: transparent !important;
  color: red !important;
  font-size: 16px !important;
}

.el-tooltip__popper {
  max-width: 320px;
  font-size: 14px;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 20px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 2px;
  box-shadow: 0 4 8 0 rgba(0, 0, 0, 0.35);
}

.el-tooltip__popper.is-dark {
  background: rgba(0, 0, 0, 0.6);
}

.el-tooltip__popper[x-placement^=top] .popper__arrow::after {
  max-width: 320px;
  font-size: 14px;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 20px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 2px;
  box-shadow: 0 4 8 0 rgba(0, 0, 0, 0.35);
  opacity: 1;
}
</style>
