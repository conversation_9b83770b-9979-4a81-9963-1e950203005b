<template>
  <div>
    <div class="infoDialog">
      <el-dialog :title="$t('自定义插件')" :visible.sync="isShowInfoDialog" width="480px" top=""
                 :before-close="handleCloseInfoDialog">
        <el-form :model="form" label-width="86px">
          <el-form-item v-for="item in formItem" :key="item.prop" :label="item.label"
                        :prop="item.prop" :required="item.required" :error="errorMsg[item.prop]">
            <el-input v-if="item.component === 'input'" :type="item.type" :maxlength="item.length"
                      v-model.trim="form[item.prop]" :placeholder="item.placeholder" :validate-event="false"
                      @blur="item.validate" @focus="handleFocusHandler(item)" :resize="item.resize"
                      :show-word-limit="item.isShowWordLimit"
            ></el-input>
            <div class="plugin-icon" v-if="item.component === 'icon'" @mouseenter="handleShowIconModal"
                 @mouseleave="handleHiddenIconModal">
              <div class="content">
                <el-image class="unEmptyIcon" :src="iconUrl" v-show="iconUrl !== ''"></el-image>
                <i class="emptyIcon onehit-icon onehit-icon-addIcon" style="color: #0854A1" v-show="iconUrl === ''"
                   @click="handleOpenIconDialog"> </i>
              </div>
              <div class="modal" v-show="isShowModal">
                <i class="editIcon onehit-icon onehit-icon-editIcon" style="padding-right: 5px"
                   @click="handleEditIcon"/>
                <i class="deleteIcon onehit-icon onehit-icon-deleteIcon" @click="handleClearIcon"/>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button class="el-button--secondary--button" @click="handleCloseInfoDialog">{{ $t('取消') }}</el-button>
          <el-button class="el-button--main--button" :disabled="isAllowSubmit"
                     @click="handleSubmitPluginInfo">{{ $t('保存') }}
          </el-button>
        </div>
      </el-dialog>
    </div>
    <div class="iconDialog">
      <el-dialog :visible.sync="isShowIconDialog" width="480px" top="" :before-close="handleCloseIconDialog">
        <div slot="title">
          <div class="title">{{ $t('为您推荐') }}</div>
          <el-popover ref="popover"
                      trigger="hover"
                      placement="top"
                      popper-class="onehit-popover"
          >
            <div class="content">
              <div class="text">{{ $t('添加插件图标Tip') }}：</div>
              <div class="example">
                <img src="@/assets/pluginManage/example.svg">
              </div>
            </div>
            <div ref="reference" class="icon onehit-icon onehit-icon-tipIcon" slot="reference"></div>
          </el-popover>
        </div>
        <div>
          <div class="pmIcons">
            <div :class="pmIconClass(item)"
                 v-for="item in icons" :key="item.iconGroup" @click="handleSelectedIcon(item)">
              <img :src="item.pmIconUrl">
            </div>
          </div>
          <div class="pdIcons">
            <div class="text">{{ $t('下面展示的为左侧导航使用的图标，与上面所选的图标为一组') }}：</div>
            <div class="pdIcon">
              <img :src="selectedIconGroup.pdIconUrl"/>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button class="el-button--secondary--button" @click="handleCloseIconDialog">{{ $t('取消') }}</el-button>
          <el-button class="el-button--main--button" @click="handleSubmitIcon">{{ $t('保存') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {pluginApi} from "@/api/onehit";
import $ from 'jquery'

export default {
  name: 'EditInfo',
  data() {
    const that = this
    const validateName = function () {
      that.validate('name')
    }
    const validateDesc = function () {
      that.validate('description')
    }
    return {
      icons: [],
      currentPlugin: {},
      form: {},
      errorMsg: {},
      isShowInfoDialog: false,
      isShowIconDialog: false,
      isShowModal: false,
      iconUrl: '',
      isClearIcon: false,
      selectedIconGroup: {},
      formItem: [
        {
          label: this.$t('插件名称') + ':',
          prop: 'name',
          propZh: this.$t('名称'),
          validate: validateName,
          model: 'name',
          required: true,
          placeholder: this.$t('请输入'),
          component: 'input',
          type: 'text',
          length: 20
        },
        {
          label: this.$t('插件图标') + ':',
          prop: 'icon',
          model: 'icon',
          component: 'icon',
          required: true
        },
        {
          label: this.$t('插件描述') + ':',
          prop: 'description',
          propZh: this.$t('描述'),
          validate: validateDesc,
          model: 'description',
          required: true,
          placeholder: this.$t('请输入'),
          component: 'input',
          type: 'textarea',
          length: 200,
          isShowWordLimit: true,
          resize: 'none'
        }
      ]
    }
  },
  watch: {
    currentPlugin() {
      this.initFormData()
    }
  },
  computed: {
    pmIconClass() {
      return function (param) {
        if (param.iconGroup === this.selectedIconGroup.iconGroup) {
          return 'pmIcon selected-icon'
        } else {
          return 'pmIcon'
        }
      }
    },
    isAllowSubmit() {
      const nameValue = this.form.name
      const descValue = this.form.description
      let iconIsHaveValue
      if (this.selectedIconGroup) {
        iconIsHaveValue = Object.keys(this.selectedIconGroup).length === 0
      } else {
        iconIsHaveValue = false
      }
      const emptyValue = [null, undefined, '']
      return emptyValue.includes(nameValue) || emptyValue.includes(descValue) || iconIsHaveValue
    }
  },
  created() {
    this.initIconGroup()
  },
  updated() {
    this.initFormItemHeight()
  },
  methods: {
    initFormItemHeight() {
      for (const item of this.formItem) {
        const label = $("label[for='" + item.prop + "']")
        const formItem = label.parent()
        if (formItem.length !== 0) {
          formItem[0].style.marginBottom = 0;
          if (item.prop === 'name') {
            label[0].style.marginTop = '6px'
            formItem[0].style.height = '61px'
          } else if (item.prop === 'icon') {
            label[0].style.marginTop = '5px'
            formItem[0].style.height = '72px'
          } else if (item.prop === 'description') {
            label[0].style.marginTop = '6px'
            formItem[0].style.height = '189px'
          }
        }
      }
    },
    initIconGroup() {
      pluginApi.getIconGroup().then(res => {
        const result = res.data.result
        result.forEach(item => {
          item.pmIconUrl = window.location.origin + '/OneHit/plugin/' + item.iconGroup + '/icon/preview/' + item.pmIcon + '?isSystemInternal=true'
          item.pdIconUrl = window.location.origin + '/OneHit/plugin/' + item.iconGroup + '/icon/preview/' + item.pdIcon + '?isSystemInternal=true'
        })
        this.icons = result
      }).catch(error => {
        console.log('系统出错', error)
      })
    },
    initFormData() {
      const keys = Object.keys(this.currentPlugin)
      if (keys.length !== 0) {
        for (const item of this.formItem) {
          if (item.prop !== 'icon') {
            this.$set(this.form, item.prop, this.currentPlugin[item.prop])
          }
        }
        const customBusinessType = ['自定义']
        if (customBusinessType.includes(this.currentPlugin.businessType)) {
          this.iconUrl = window.location.origin + '/OneHit/plugin/' + this.currentPlugin?.iconGroup + '/icon/preview/' + this.currentPlugin?.pmIcon + '?isSystemInternal=true'
        } else {
          this.iconUrl = window.location.origin + '/OneHit/plugin/' + this.currentPlugin?.name + '/icon/preview/' + this.currentPlugin?.pmIcon
        }
      }
    },
    initSelectedIcon() {
      const currentIconGroup = this.currentPlugin.iconGroup
      console.log(currentIconGroup)
      const iconGroup = this.icons.find(icon => icon.iconGroup === currentIconGroup)
      console.log(iconGroup)
      this.selectedIconGroup = iconGroup ? iconGroup : this.icons[0]
      this.iconUrl = window.location.origin + '/OneHit/plugin/' + this.currentPlugin?.iconGroup + '/icon/preview/' + this.currentPlugin?.pmIcon + '?isSystemInternal=true'
    },
    validate(prop) {
      this.$set(this.errorMsg, prop, '')
      const ele = this.formItem.find(item => item.prop === prop)
      ele.placeholder = this.$t('请输入')
      const value = this.form[prop]
      if (value === '' || value === undefined || value === null) {
        this.$set(this.errorMsg, prop, this.$t('请输入') + ele.propZh)
        return false
      }
      if (prop === 'description') {
        const reg = new RegExp(/^[\u4E00-\u9FA5a-zA-Z0-9!"#$%&'()*+,\-./:;<=>?@\[\\\]^_`{|}~，、。；！“”]+$/)
        if (!reg.test(value)) {
          this.$set(this.errorMsg, prop, this.$t('请输入文字、字母和数字的组合'))
          return false
        }
      } else if (prop === 'name') {
        const reg = new RegExp(/^[\u4e00-\u9fa5a-zA-Z0-9]+$/)
        if (!reg.test(value)) {
          this.$set(this.errorMsg, prop, this.$t('请输入文字、字母和数字的组合'))
          return false
        }
      }
      this.$set(this.errorMsg, prop, '')
      return true
    },
    handleOpenIconDialog() {
      this.selectedIconGroup = this.icons[0]
      this.isShowIconDialog = true
    },
    handleEditIcon() {
      this.isShowIconDialog = true
    },
    handleClearIcon() {
      this.selectedIconGroup = {}
      this.isClearIcon = true
      this.iconUrl = ''
      this.isShowModal = false
    },
    handleCloseIconDialog() {
      this.isShowIconDialog = false
      if (this.isClearIcon) {
        this.selectedIconGroup = {}
      } else if (this.iconUrl.includes(this.selectedIconGroup.iconGroup)) {
      } else if (!this.isClearIcon && Object.keys(this.selectedIconGroup).length !== 0) {
        const currentIconGroup = this.currentPlugin.iconGroup
        const iconGroup = this.icons.find(icon => icon.iconGroup === currentIconGroup)
        this.selectedIconGroup = iconGroup ? iconGroup : this.icons[0]
      }
    },
    handleCloseInfoDialog() {
      this.isShowInfoDialog = false
      this.initFormData()
    },
    handleFocusHandler(item) {
      item.placeholder = ''
    },
    handleShowIconModal() {
      if (this.iconUrl !== '' && !this.isShowModal) {
        this.isShowModal = true
      }
    },
    handleHiddenIconModal() {
      if (this.isShowModal) {
        this.isShowModal = false
      }
    },
    handleSelectedIcon(iconGroup) {
      this.selectedIconGroup = iconGroup
    },
    handleSubmitIcon() {
      this.iconUrl = this.selectedIconGroup.pmIconUrl
      this.isClearIcon = false
      this.isShowIconDialog = false
    },
    validateIcon() {
      const keys = Object.keys(this.selectedIconGroup)
      return keys.length !== 0
    },
    handleSubmitPluginInfo() {
      const validateResult = []
      for (const item of this.formItem) {
        if (item.prop === 'icon') {
          validateResult.push(this.validateIcon())
        } else {
          validateResult.push(this.validate(item.prop))
        }
      }
      const isNotPass = validateResult.includes(false)
      if (!isNotPass) {
        const param = {
          id: this.currentPlugin.id,
          name: this.form.name,
          description: this.form.description,
          businessType: this.currentPlugin.businessType,
          pmIcon: this.selectedIconGroup.pmIcon,
          pdIcon: this.selectedIconGroup.pdIcon,
          iconGroup: this.selectedIconGroup.iconGroup
        }
        console.log(param, 'param')
        const message = Object.assign({}, param)
        pluginApi.updatePlugin(param).then(res => {
          this.$message({
            dangerouslyUseHTMLString: true,
            duration: 3000,
            customClass: 'scenatorStyle scenator_briefMsg success',
            message: '<div><article>' + this.$t('插件保存成功') + '</article></div>',
          });
          this.isShowInfoDialog = false
          this.$emit('updatePlugin', {
            prePlugin: this.currentPlugin,
            nextPlugin: message
          })
          this.handleClearIcon()
        }).catch(error => {
          if (error?.response?.data?.message.includes('系统内已有该插件')) {
            this.$set(this.errorMsg, 'name', this.$t('该名称已存在'))
          } else if (error?.response?.data?.message.includes("未找到指定插件")) {
            this.$message({
              dangerouslyUseHTMLString: true,
              duration: 3000,
              customClass: 'scenatorStyle scenator_complexMsg warn',
              message: '<div><article>' + this.$t('此插件不存在') + '</article></div>',
            });
            this.isShowInfoDialog = false
            this.$emit('updatePlugin', {
              prePlugin: this.currentPlugin,
              nextPlugin: message
            })
            this.handleClearIcon()
          } else {
            this.$message({
              dangerouslyUseHTMLString: true,
              duration: 3000,
              customClass: 'scenatorStyle scenator_complexMsg error',
              message: '<div><article>' + this.$t('插件保存失败') + '</article></div>',
            });
            this.isShowInfoDialog = false
            this.handleClearIcon()
          }
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.scenatorStyle {
  .el-form {
    .el-form-item {
      /deep/ .el-form-item__label {
        padding: 0;
        height: 20px;
        line-height: 20px;
      }

      /deep/ .el-form-item__content {
        width: 280px;

        .el-input__count {
          line-height: 16px;
          right: 0;
        }
      }

      /deep/ .el-form-item__error {
        max-width: 280px;
        font-size: 12px;
        font-weight: 400;
        line-height: 17px;
        color: #BB0000;
      }
    }

    .el-form-item:nth-of-type(3) {
      /deep/ .el-form-item__content {
        .el-form-item__error {
          margin-top: -19px;
        }
      }
    }

    .el-form-item.is-required:not(.is-no-asterisk) {
      /deep/ .el-form-item__label:before {
        margin-right: 0;
      }

      /deep/ .el-form-item__label:after {
        margin-left: 9px;
        margin-right: 8px;
        display: inline-block;
        width: 8px;
        text-align: center;
      }
    }
  }

  .el-input {
    /deep/ .el-input__inner {
      padding: 6px 10px;
    }
  }

  .el-textarea {
    /deep/ .el-textarea__inner {
      height: 172px;
      padding: 6px 10px;
    }
  }

  .infoDialog {
    .plugin-icon {
      width: 48px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      border: 1px solid #94999D;
      border-radius: 2px;
      box-sizing: border-box;
      position: relative;

      .content {
        width: 100%;
        height: 100%;
        position: relative;
      }

      .emptyIcon {
        cursor: pointer;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .unEmptyIcon {
        width: 38px;
        height: 38px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .modal {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.5);

        .editIcon, .deleteIcon {
          width: 16px;
          height: 16px;
          display: inline-block;
        }

        .editIcon, .deleteIcon {
          color: #ffffff;
        }

        .deleteIcon:hover, .editIcon:hover {
          color: #0854A1;
        }
      }
    }

    /deep/ .el-dialog__header {
      border-radius: 4px 4px 0 0;
      height: 44px;
      box-sizing: border-box;
    }

    /deep/ .el-dialog__body {
      padding: 24px 57px;
    }

    /deep/ .el-dialog__footer {
      border-radius: 0 0 4px 4px;
    }
  }

  .iconDialog {
    /deep/ .el-dialog__header {
      height: 44px;
      box-sizing: border-box;
    }

    /deep/ .el-dialog__header > div {
      display: flex;
    }

    .title {
      font-size: 16px;
      margin-right: 4px;
    }

    .popperContainer {
      position: relative;
    }

    .icon {
      width: 16px;
      height: 22px;
      color: #687173;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .pmIcon {
      width: 48px;
      height: 48px;
      border: 1px solid #E5E5E5;
      box-sizing: border-box;
      border-radius: 2px;
      line-height: 48px;
      text-align: center;
      position: relative;
      margin-right: 16px;
      margin-bottom: 16px;

      & > img {
        width: 38px;
        height: 38px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .pmIcon:nth-of-type(7n) {
      margin-right: 0;
    }

    .pmIcon:hover {
      background-color: #EAEFF3;
    }

    .pmIcon:active {
      border: 1px solid #0854A1;
      background-color: #E5F2FF;
    }

    .selected-icon {
      border: 1px solid #0854A1;
    }

    .pmIcons {
      height: 200px;
      display: flex;
      flex-wrap: wrap;
    }

    .pdIcons {
      border-top: 1px solid #D9D9D9;
      height: 82px;

      .text {
        height: 17px;
        line-height: 17px;
        font-size: 14px;
        font-weight: 400;
        color: #94999D;
        margin-top: 16px;
        margin-bottom: 16px;
      }

      .pdIcon {
        width: 100%;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-color: #F3F5F7;
      }
    }
  }
}
</style>
<style lang="less">
.el-popover.onehit-popover {
  padding: 12px;
  width: 395px;
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px #D9D9D9;
  box-sizing: border-box;

  .content {
    max-width: 371px;
    line-height: 22px;
    font-size: 14px;
    font-weight: 400;

    .text {
      margin-bottom: 8px;
    }

    .example {
      height: 70px;
      box-sizing: border-box;
      background-color: #EAEFF3;
    }
  }
}
</style>