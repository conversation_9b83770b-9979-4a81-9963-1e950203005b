{"name": "OneHit-SystemSetting", "version": "4.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "webpack --config ./webpack.prod.config.js", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.4", "element-ui": "^2.15.6", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "highlight.js": "^11.9.0", "jquery": "^3.7.0", "less": "^4.1.1", "lodash": "^4.17.21", "qs": "^6.11.2", "spark-md5": "^3.0.2", "vue": "^2.6.11", "vue-i18n": "^6.1.3", "vue-router": "^3.2.0", "vue-virtual-scroll-list": "^2.3.5", "vuex": "^3.6.2", "xlsx": "^0.17.4"}, "devDependencies": {"@babel/core": "^7.16.12", "@vue/cli-plugin-babel": "^4.5.15", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.3", "babel-polyfill": "^6.26.0", "clean-webpack-plugin": "^4.0.0", "compression-webpack-plugin": "^9.2.0", "copy-webpack-plugin": "^5.1.1", "core-js": "^3.20.3", "css-loader": "^3.3.2", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^4.5.2", "less-loader": "^7.3.0", "mini-css-extract-plugin": "^1.6.0", "style-loader": "^1.0.1", "url-loader": "^4.1.1", "vue-simple-uploader": "^0.7.6", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.11", "webpack": "^4.46.0", "webpack-cli": "^4.9.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 3 versions", "not ie <= 10"]}