pipeline {
    agent any

    parameters {
        booleanParam(name:'isBuildGaia', description:'是否打包Gaia与kkFileView', defaultValue:false)
        booleanParam(name:'isDeployNexus', description:'是否发布依赖到nexus上', defaultValue:false)
        booleanParam(name:'isEncrypt', description:'是否加密', defaultValue:false)
    }

    environment {
        ONEHIT_GAIA_BRANCH_NAME='OneHit-Gaia-5.10.0'
    }

    stages {


        stage('deploy') {
            when {
                allOf {
                    expression { params.isDeployNexus }
                }
            }
            steps {
                 echo '发布onehit-server到nexus'
                 dir('onehit-server'){
                    sh 'echo $pwd'
                    withMaven(jdk: 'JDK_1.8', maven: 'Maven_3.6.3', mavenSettingsConfig: '35740ff1-450d-4832-bd11-34246416fee8', options: [artifactsPublisher(disabled: true)]) {
                        sh 'mvn deploy -DskipTests -DaltReleaseDeploymentRepository=releases::default::http://scm.rzon.tech/nexus/repository/releases/'
                    }
                 }
            }

        }

        stage('Build-onehit-app') {
            steps {
                dir('onehit-ui'){
                    nodejs(configId: 'fulongtech', nodeJSInstallationName: 'NodeJS_12.14.1') {
                        sh 'npm install && npm run build-app'
                    }
                }
            }
        }
        stage('Build-onehit-base-app') {
            steps {
                dir('base-app/Renderer3D'){
                    nodejs(configId: 'fulongtech', nodeJSInstallationName: 'NodeJS_12.14.1') {
                        sh 'npm install && npm run build'
                    }
                }
                dir('base-app/DocumentPreview'){
                    nodejs(configId: 'fulongtech', nodeJSInstallationName: 'NodeJS_12.14.1') {
                        sh 'npm install && npm run build'
                    }
                }
                dir('base-app/Navigator'){
                    nodejs(configId: 'fulongtech', nodeJSInstallationName: 'NodeJS_12.14.1') {
                        sh 'npm install && npm run build'
                    }
                }
                dir('base-app/Collection'){
                    nodejs(configId: 'fulongtech', nodeJSInstallationName: 'NodeJS_12.14.1') {
                        sh 'npm install && npm run build'
                    }
                }
				dir('base-app/RepoModel'){
                    nodejs(configId: 'fulongtech', nodeJSInstallationName: 'NodeJS_12.14.1') {
                        sh 'npm install && npm run build'
                    }
                }
            }
        }
        stage('Build-onehit-system-center-ui') {
             steps {
                 dir('onehit-system-centre/ui'){
                      nodejs(configId: 'fulongtech', nodeJSInstallationName: 'NodeJS_12.14.1') {
                          sh 'npm install && npm run build-app'
                      }
                 }
             }
        }


        stage('Build-server') {
            steps {
                withMaven(jdk: 'JDK_1.8', maven: 'Maven_3.6.3', options: [artifactsPublisher(disabled: true)]) {
                    sh 'mvn clean package -e -U -Dmaven.test.skip=true'
                }
            }
        }

        stage('Build-systemSetting') {
            steps {
                dir('onehit-setting-ui'){
                    nodejs(configId: 'fulongtech', nodeJSInstallationName: 'NodeJS_12.14.1') {
                        sh 'npm install && npm run build'
                    }
                }
                sh 'cp -r $WORKSPACE/onehit-setting-ui/dist/. /$WORKSPACE/onehit-business/src/main/resources/static/'
            }
        }

        stage('Build-Gaia') {
            when {
                equals expected: "true", actual:"${params.isBuildGaia}"
            }
            steps {
               checkout scmGit(branches: [[name: "${ONEHIT_GAIA_BRANCH_NAME}"]], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'OneHit-Gaia']], gitTool: 'Default', userRemoteConfigs: [[credentialsId: 'wangrongUserToken', url: 'https://git.rzon.tech/rzon/onehit/OneHit-Gaia.git']])
            }
        }
//         stage('Build-kkFileView') {
//             when {
//                 equals expected: "true", actual:"${params.isBuildGaia}"
//             }
//             steps {
//                 checkout scmGit(branches: [[name: "develop"]], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'OneHit-kkFileView']], gitTool: 'Default', userRemoteConfigs: [[credentialsId: 'wangrongUserToken', url: 'https://git.rzon.tech/rzon/onehit/3.x.x/fileview.git']])
//                 dir('OneHit-kkFileView'){
//                     sh 'rm -rf $WORKSPACE/OneHit-kkFileView/server/target/*'
//                     withMaven(jdk: 'JDK_1.8', maven: 'Maven_3.6.3') {
//                         sh 'mvn clean package -e -U -Dmaven.test.skip=true'
//                     }
//                     sh 'mv $WORKSPACE/OneHit-kkFileView/server/target/kkFileView-4.1.0-SNAPSHOT.tar.gz $WORKSPACE/OneHit-kkFileView/server/target/OneHit-kkFileView-Linux.tar.gz'
//                     sh 'mv $WORKSPACE/OneHit-kkFileView/server/target/kkFileView-4.1.0-SNAPSHOT.zip $WORKSPACE/OneHit-kkFileView/server/target/OneHit-kkFileView-Win.zip'
//                 }
//             }
//         }
        stage('Deploy') {
            when {
              expression {
                currentBuild.result == null || currentBuild.result == 'SUCCESS'
              }
            }
            steps {
                echo 'Not Deploy...'
            }
        }
        stage('Encryption') {
            when {
                equals expected: "true", actual:"${params.isEncrypt}"
            }
            steps {
                sh '''
                    # 定义文件匹配模式
                    FILE_PATTERN="$WORKSPACE/onehit-business/target/onehit-business-*.jar"
                    # 获取实际文件路径
                    TARGET_FILE=$(ls $FILE_PATTERN 2>/dev/null | head -n1)
                    ENCRYPTED_FILE="${TARGET_FILE%.jar}-enc.jar"
                    echo "找到目标文件：$TARGET_FILE"
                    # 执行上传
                    curl -X POST -F "file=@$TARGET_FILE" "http://***********:8080/protect?productCode=8" -o "$ENCRYPTED_FILE"
                '''
				sh '''
                    # 定义文件匹配模式
                    FILE_PATTERN="$WORKSPACE/onehit-plugin/target/onehit-plugin-*.jar"
                    # 获取实际文件路径
                    TARGET_FILE=$(ls $FILE_PATTERN 2>/dev/null | head -n1)
                    ENCRYPTED_FILE="${TARGET_FILE%.jar}-enc.jar"
                    echo "找到目标文件：$TARGET_FILE"
                    # 执行上传
                    curl -X POST -F "file=@$TARGET_FILE" "http://***********:8080/protect?productCode=8" -o "$ENCRYPTED_FILE"
                '''
            }
        }
        stage('Archive') {
            when {
                equals expected: "true", actual:"${params.isBuildGaia}"
            }
            steps {
                // 复制 Scenator 相关文件
                sh 'mkdir -p target/onehit-scenator'
                sh 'rm -rf $WORKSPACE/target/onehit-scenator/*'
                sh 'cp -rf $WORKSPACE/onehit-scenator/apps $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/static $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/scenes $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/languages $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/tools $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/scenator-service/target/onehit-scenator.jar $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/scenator-service/src/main/resources/application.yml $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/scenator-service/src/main/resources/bootstrap.yml $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/start.bat $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/start.sh $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/stop.sh $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/APP_APIDoc $WORKSPACE/target/onehit-scenator'

                // 复制 gateway 相关模块
                sh 'mkdir -p target/onehit-gateway'
                sh 'rm -rf $WORKSPACE/target/onehit-gateway/*'
                sh 'mkdir -p target/onehit-gateway/config'
                sh 'cp -r $WORKSPACE/onehit-gateway/target/onehit-gateway-*.jar $WORKSPACE/target/onehit-gateway'
                sh 'cp -r $WORKSPACE/onehit-gateway/src/main/resources/application.yml $WORKSPACE/target/onehit-gateway/config'
                sh 'cp -r $WORKSPACE/onehit-gateway/src/main/resources/bootstrap.yml $WORKSPACE/target/onehit-gateway/config'
                sh 'cp -r $WORKSPACE/onehit-gateway/start.bat $WORKSPACE/target/onehit-gateway'
                sh 'cp -r $WORKSPACE/onehit-gateway/start.sh $WORKSPACE/target/onehit-gateway'
                sh 'cp -r $WORKSPACE/onehit-gateway/stop.sh $WORKSPACE/target/onehit-gateway'
                sh 'cp -r $WORKSPACE/onehit-gateway/static $WORKSPACE/target/onehit-gateway'

                // 复制 uams 相关模块
                sh 'mkdir -p target/onehit-uams'
                sh 'rm -rf $WORKSPACE/target/onehit-uams/*'
                sh 'cp -r $WORKSPACE/onehit-uams/target/UAMS.jar $WORKSPACE/target/onehit-uams'
                sh 'cp -rf $WORKSPACE/onehit-uams/config $WORKSPACE/target/onehit-uams'
                sh 'cp -r $WORKSPACE/onehit-uams/start.bat $WORKSPACE/target/onehit-uams'
                sh 'cp -r $WORKSPACE/onehit-uams/start.sh $WORKSPACE/target/onehit-uams'
                sh 'cp -r $WORKSPACE/onehit-uams/stop.sh $WORKSPACE/target/onehit-uams'
                sh 'cp -rf $WORKSPACE/onehit-uams/pages $WORKSPACE/target/onehit-uams'
                sh 'cp -rf $WORKSPACE/onehit-uams/lib $WORKSPACE/target/onehit-uams'

                // 复制 business 相关文件
                sh 'mkdir -p target/onehit-plugin'
                sh 'rm -rf $WORKSPACE/target/onehit-plugin/*'
                sh 'mkdir -p target/onehit-plugin/lib'
                sh 'cp -rf $WORKSPACE/onehit-business/lib/** $WORKSPACE/target/onehit-plugin/lib'
                sh 'cp -f $WORKSPACE/onehit-business/target/onehit-business-*.jar $WORKSPACE/target/onehit-plugin'
                // sh 'cp -r $WORKSPACE/onehit-business/src/main/resources/tools/WinOdsSvgExportEx $WORKSPACE/target/onehit-plugin'
                // sh 'cp -r $WORKSPACE/onehit-business/src/main/resources/tools/dwg2svg $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/src/main/resources/application-setting.yml $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/src/main/resources/application.yml $WORKSPACE/target/onehit-plugin'
				sh 'cp -r $WORKSPACE/onehit-business/fop.xml $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/start.bat $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/start.sh $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/stop.sh $WORKSPACE/target/onehit-plugin'


                // 复制 system-centre 相关文件
                sh 'mkdir -p target/onehit-system-centre'
                sh 'rm -rf $WORKSPACE/target/onehit-system-centre/*'
                sh 'cp -f $WORKSPACE/onehit-system-centre/target/onehit-system-centre-*.jar $WORKSPACE/target/onehit-system-centre'
                sh 'cp -r $WORKSPACE/onehit-system-centre/src/main/resources/application.yml $WORKSPACE/target/onehit-system-centre'
                sh 'cp -rf $WORKSPACE/onehit-system-centre/uploads $WORKSPACE/target/onehit-system-centre'

                sh 'rm -r $WORKSPACE/OneHit-Gaia/.git'
                sh 'rm -r $WORKSPACE/onehit-gaia'
                sh 'mv $WORKSPACE/OneHit-Gaia $WORKSPACE/onehit-gaia'
                sh 'cp -r $WORKSPACE/OneHit-kkFileView/server/target/OneHit-kkFileView-Linux.tar.gz $WORKSPACE/onehit-gaia'
                sh 'cp -r $WORKSPACE/OneHit-kkFileView/server/target/OneHit-kkFileView-Win.zip $WORKSPACE/onehit-gaia'

                sh 'mkdir -p archive'
                sh 'rm -rf $WORKSPACE/archive/*'
                sh 'tar -zcvf $WORKSPACE/archive/$(date +%Y%m%d)-${BUILD_NUMBER}-OneHit-install.tar.gz -C $WORKSPACE/target onehit-scenator -C $WORKSPACE/target onehit-gateway -C $WORKSPACE/target onehit-uams -C $WORKSPACE/target onehit-plugin -C $WORKSPACE/target onehit-system-centre -C $WORKSPACE onehit-gaia'
                dir('archive'){
                    archiveArtifacts artifacts: '*', fingerprint: true
                }
            }

        }
        stage('Archive-mini') {
            when {
                equals expected: "false", actual:"${params.isBuildGaia}"
            }
            steps {
                // 复制 Scenator 相关文件
                sh 'mkdir -p target/onehit-scenator'
                sh 'rm -rf $WORKSPACE/target/onehit-scenator/*'
                sh 'cp -rf $WORKSPACE/onehit-scenator/apps $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/config $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/static $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/scenes $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/structures $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/languages $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/tools $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/scenator-service/target/onehit-scenator.jar $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/scenator-service/src/main/resources/application.yml $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/scenator-service/src/main/resources/bootstrap.yml $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/start.bat $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/start.sh $WORKSPACE/target/onehit-scenator'
                sh 'cp -r $WORKSPACE/onehit-scenator/stop.sh $WORKSPACE/target/onehit-scenator'
                sh 'cp -rf $WORKSPACE/onehit-scenator/APP_APIDoc $WORKSPACE/target/onehit-scenator'

                // 复制 gateway 相关模块
                sh 'mkdir -p target/onehit-gateway'
                sh 'rm -rf $WORKSPACE/target/onehit-gateway/*'
				sh 'mkdir -p target/onehit-gateway/config'
                sh 'cp -r $WORKSPACE/onehit-gateway/target/onehit-gateway-*.jar $WORKSPACE/target/onehit-gateway'
                sh 'cp -r $WORKSPACE/onehit-gateway/src/main/resources/application.yml $WORKSPACE/target/onehit-gateway/config'
                sh 'cp -r $WORKSPACE/onehit-gateway/src/main/resources/bootstrap.yml $WORKSPACE/target/onehit-gateway/config'
                sh 'cp -r $WORKSPACE/onehit-gateway/start.bat $WORKSPACE/target/onehit-gateway'
                sh 'cp -r $WORKSPACE/onehit-gateway/start.sh $WORKSPACE/target/onehit-gateway'
                sh 'cp -r $WORKSPACE/onehit-gateway/stop.sh $WORKSPACE/target/onehit-gateway'
                sh 'cp -r $WORKSPACE/onehit-gateway/static $WORKSPACE/target/onehit-gateway'

                // 复制 uams 相关模块
                sh 'mkdir -p target/onehit-uams'
                sh 'rm -rf $WORKSPACE/target/onehit-uams/*'
                sh 'cp -r $WORKSPACE/onehit-uams/target/UAMS.jar $WORKSPACE/target/onehit-uams'
                sh 'cp -rf $WORKSPACE/onehit-uams/config $WORKSPACE/target/onehit-uams'
                sh 'cp -r $WORKSPACE/onehit-uams/start.bat $WORKSPACE/target/onehit-uams'
                sh 'cp -r $WORKSPACE/onehit-uams/start.sh $WORKSPACE/target/onehit-uams'
                sh 'cp -r $WORKSPACE/onehit-uams/stop.sh $WORKSPACE/target/onehit-uams'
                sh 'cp -rf $WORKSPACE/onehit-uams/pages $WORKSPACE/target/onehit-uams'
                sh 'cp -rf $WORKSPACE/onehit-uams/lib $WORKSPACE/target/onehit-uams'

                // 复制 business 相关文件
                sh 'mkdir -p target/onehit-plugin'
                sh 'rm -rf $WORKSPACE/target/onehit-plugin/*'
                sh 'mkdir -p target/onehit-plugin/lib'
                sh 'cp -rf $WORKSPACE/onehit-business/lib/** $WORKSPACE/target/onehit-plugin/lib'
                sh 'cp -f $WORKSPACE/onehit-business/target/onehit-business-*.jar $WORKSPACE/target/onehit-plugin'
                // sh 'cp -r $WORKSPACE/onehit-business/src/main/resources/tools/WinOdsSvgExportEx $WORKSPACE/target/onehit-plugin'
                // sh 'cp -r $WORKSPACE/onehit-business/src/main/resources/tools/dwg2svg $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/src/main/resources/application-setting.yml $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/src/main/resources/application.yml $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/src/main/resources/bootstrap.yml $WORKSPACE/target/onehit-plugin'
				sh 'cp -r $WORKSPACE/onehit-business/fop.xml $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/start.bat $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/start.sh $WORKSPACE/target/onehit-plugin'
                sh 'cp -r $WORKSPACE/onehit-business/stop.sh $WORKSPACE/target/onehit-plugin'


                // 复制 system-centre 相关文件
                sh 'mkdir -p target/onehit-system-centre'
                sh 'rm -rf $WORKSPACE/target/onehit-system-centre/*'
                sh 'cp -f $WORKSPACE/onehit-system-centre/target/onehit-system-centre-*.jar $WORKSPACE/target/onehit-system-centre'
                sh 'cp -r $WORKSPACE/onehit-system-centre/src/main/resources/application.yml $WORKSPACE/target/onehit-system-centre'
                sh 'cp -rf $WORKSPACE/onehit-system-centre/uploads $WORKSPACE/target/onehit-system-centre'
				
                //workflow
                sh 'mkdir -p target/onehit-workflow'
                sh 'rm -rf $WORKSPACE/target/onehit-workflow/*'
                sh 'cp -f $WORKSPACE/onehit-workflow/target/onehit-workflow-*.jar $WORKSPACE/target/onehit-workflow'
                sh 'cp -r $WORKSPACE/onehit-workflow/src/main/resources/application.yml $WORKSPACE/target/onehit-workflow'


                sh 'mkdir -p archive'
                sh 'rm -rf $WORKSPACE/archive/*'
                sh 'tar -zcvf $WORKSPACE/archive/$(date +%Y%m%d)-${BUILD_NUMBER}-OneHit-install.tar.gz -C $WORKSPACE/target onehit-scenator -C $WORKSPACE/target onehit-gateway -C $WORKSPACE/target onehit-uams -C $WORKSPACE/target onehit-plugin -C $WORKSPACE/target onehit-system-centre -C $WORKSPACE/target onehit-workflow'
                dir('archive'){
                    archiveArtifacts artifacts: '*', fingerprint: true
                }
            }
        }
    }
}
